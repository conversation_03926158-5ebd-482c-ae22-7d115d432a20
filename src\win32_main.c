#define SOKOL_IMPL
#define SOKOL_D3D11
#define SOKOL_APP_API_DECL static
#define SOKOL_GFX_IMPL

#include <sokol/sokol_app.h>
#include <sokol/sokol_gfx.h>
#include <sokol/sokol_time.h>
#include <sokol/sokol_glue.h>

#include <stdint.h> // for uint8_t
#include <string.h> // for memset

#include "platform.h"

////////////////////////////////////////////////////////////////////////////////
// Hot reload stub
////////////////////////////////////////////////////////////////////////////////

#ifdef DEBUG
typedef void (*game_init_fn)(PlatformLayer* platform);
typedef void (*game_update_fn)(PlatformLayer* platform);

static game_update_fn game_update = 0;
static game_init_fn game_init = 0;

global HMODULE lib;

static void load_library(void) {
    if (lib) {
        FreeLibrary(lib);
    }

    lib = LoadLibraryA("game.dll");
    if (!lib) {
        return;
    }

    game_init = (game_init_fn)GetProcAddress(lib, "game_init");
    game_update = (game_update_fn)GetProcAddress(lib, "game_update");
}

#else
#include "game.c"
#endif

////////////////////////////////////////////////////////////////////////////////
// Shader code
////////////////////////////////////////////////////////////////////////////////

#include "sprite.glsl.h"
#include "post.glsl.h"

////////////////////////////////////////////////////////////////////////////////
// Simple batch renderer
////////////////////////////////////////////////////////////////////////////////

#define MAX_BATCHES 10000

typedef union {
    struct {
        Pos pos;
        UV uv;
        Color color;
    };
    struct {
        float x, y;
        float u, v;
        float r, g, b, a;
    };
    float data[8];
} Vertex;

typedef struct {
    Vertex verts[6]; // 2 triangles = 6 vertices
} Quad;

typedef struct {
    Quad quads[MAX_BATCHES];
    int count;
    sg_pipeline pip;
    sg_bindings bind;
    sg_shader shader;
} BatchRenderer;

global BatchRenderer *renderer;

internal void renderer_init(void) {
    renderer->shader = sg_make_shader(simple_shader_desc(sg_query_backend()));
    
    renderer->bind.vertex_buffers[0] = sg_make_buffer(&(sg_buffer_desc){
        .size = sizeof(renderer->quads),
        .usage = {
            .vertex_buffer = true,
            .dynamic_update = true,
        },
    });
    
    sg_pipeline_desc pdesc = {0};
    
    pdesc.layout.attrs[0].format = SG_VERTEXFORMAT_FLOAT2; // pos
    pdesc.layout.attrs[1].format = SG_VERTEXFORMAT_FLOAT2; // uv
    pdesc.layout.attrs[2].format = SG_VERTEXFORMAT_FLOAT4; // color
    pdesc.shader = renderer->shader;

    renderer->pip = sg_make_pipeline(&pdesc);
    renderer->count = 0;
}

internal void renderer_begin(void) {
    renderer->count = 0;
}

internal void renderer_push(Pos pos, Size size, UV uv, Color color) {
    if (renderer->count >= MAX_BATCHES) {
        return;
    }

    Quad* quad = &renderer->quads[renderer->count++];
    // First triangle: top-left, top-right, bottom-left
    quad->verts[0] = (Vertex){ .pos = {pos.x, pos.y}, .uv = uv, .color = color };
    quad->verts[1] = (Vertex){ .pos = {pos.x+size.w, pos.y}, .uv = uv, .color = color };
    quad->verts[2] = (Vertex){ .pos = {pos.x, pos.y+size.h}, .uv = uv, .color = color };
    // Second triangle: top-right, bottom-right, bottom-left
    quad->verts[3] = (Vertex){ .pos = {pos.x+size.w, pos.y}, .uv = uv, .color = color };
    quad->verts[4] = (Vertex){ .pos = {pos.x+size.w, pos.y+size.h}, .uv = uv, .color = color };
    quad->verts[5] = (Vertex){ .pos = {pos.x, pos.y+size.h}, .uv = uv, .color = color };
}

internal void renderer_flush(void) {
    if (renderer->count == 0) return;
    
    sg_update_buffer(renderer->bind.vertex_buffers[0], &(sg_range){
        .ptr = renderer->quads,
        .size = renderer->count * sizeof(Quad)
    });
    
    sg_apply_pipeline(renderer->pip);
    sg_apply_bindings(&renderer->bind);
    sg_draw(0, renderer->count * 6, 1);
}

////////////////////////////////////////////////////////////////////////////////
// Sokol callbacks
//////////////////////////////////////////////////////////////////////////////////

global PlatformLayer platform_layer;

internal void init(void) {
    sg_setup(&(sg_desc){
        .environment = sglue_environment(),
    });

    size_t memory_size = Megabytes(64);
    void* memory = VirtualAlloc(0, memory_size, MEM_RESERVE|MEM_COMMIT, PAGE_READWRITE);

    renderer = (BatchRenderer*)memory;
    memory = (uint8_t*)memory + sizeof(BatchRenderer);
    memory_size -= sizeof(BatchRenderer);

    renderer_init();

    platform_layer.persistent_arena.base = (uint8_t*)memory;
    platform_layer.persistent_arena.size = memory_size / 5 * 4;
    platform_layer.persistent_arena.offset = 0;

    platform_layer.transient_arena.base = (uint8_t*)memory + platform_layer.transient_arena.size;
    platform_layer.transient_arena.size = memory_size - platform_layer.persistent_arena.size;
    platform_layer.transient_arena.offset = 0;

    platform_layer.renderer_push = renderer_push;

    stm_setup();

#ifdef DEBUG
    load_library();
    if(game_init) {
        game_init(&platform_layer);
    }
#else
    game_init(&platform_layer);
#endif
}

internal void frame(void) {
    renderer_begin();

#ifdef DEBUG
    if(game_update) {
        game_update(&platform_layer);
    }
#else
        game_update(&platform_layer);
#endif

    sg_begin_pass(&(sg_pass){ .action = (sg_pass_action){0}, .swapchain = sglue_swapchain() });
    renderer_flush();
    sg_end_pass();
    sg_commit();
}

internal void cleanup(void) {
    sg_shutdown();
}

internal void event(const sapp_event* event) {
    (void)event;
}

sapp_desc sokol_main(int argc, char* argv[]) {
    return (sapp_desc) {
        .init_cb = init,
        .frame_cb = frame,
        .cleanup_cb = cleanup,
        .event_cb = event,
        .width = 1280,
        .height = 720,
        .high_dpi = true,
        .window_title = "Base Layer",
    };
}
