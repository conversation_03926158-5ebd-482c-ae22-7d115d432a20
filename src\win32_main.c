#define SOKOL_IMPL
#define SOK<PERSON>_D3D11
#define SOKOL_APP_API_DECL static
#define SOKOL_GFX_IMPL

#include <sokol/sokol_app.h>
#include <sokol/sokol_gfx.h>
#include <sokol/sokol_time.h>
#include <sokol/sokol_glue.h>

#include <stdint.h> // for uint8_t
#include <string.h> // for memset

#define STR(...) # __VA_ARGS__

#define global static
#define persist static
#define internal static

////////////////////////////////////////////////////////////////////////////////
// Memory Arena
////////////////////////////////////////////////////////////////////////////////

#define Kilobytes(n) ((n) * 1024)
#define Megabytes(n) (Kilobytes(n) * 1024)
#define Gigabytes(n) (Megabytes(n) * 1024)

typedef struct {
    uint8_t* base;
    size_t size;
    size_t offset;
} Arena;

internal Arena arena_create(void* base, size_t size) {
    return (Arena) {
        .base = (uint8_t*)base,
        .size = size,
        .offset = 0,
    };
}

internal void* arena_alloc(Arena* arena, size_t size, size_t align) {
    size_t aligned_offset = (arena->offset + align - 1) & ~(align - 1);
    if (aligned_offset + size > arena->size) {
        return NULL;
    }
    
    void* ptr = arena->base + aligned_offset;
    arena->offset = aligned_offset + size;
    return ptr;
}

internal void* arena_zalloc(Arena* arena, size_t size, size_t align) {
    void* ptr = arena_alloc(arena, size, align);
    if (ptr) {
        memset(ptr, 0, size);
    }
    return ptr;
}

internal void arena_free(Arena* arena) {
    arena->offset = 0;
}

////////////////////////////////////////////////////////////////////////////////
// Shader code
////////////////////////////////////////////////////////////////////////////////

#include "shader.glsl.h"

////////////////////////////////////////////////////////////////////////////////
// Simple batch renderer
////////////////////////////////////////////////////////////////////////////////

#define MAX_BATCHES 10000

typedef struct {
    float x, y;
} Pos;

typedef struct {
    float w, h;
} Size;

typedef struct {
    float u, v;
} UV;

typedef struct {
    float r, g, b, a;
} Color;

typedef union {
    struct {
        Pos pos;
        UV uv;
        Color color;
    };
    struct {
        float x, y;
        float u, v;
        float r, g, b, a;
    };
    float data[8];
} Vertex;

typedef struct {
    Vertex verts[4];
} Quad;

typedef struct {
    Quad quads[MAX_BATCHES];
    int count;
    sg_pipeline pip;
    sg_bindings bind;
    sg_shader shader;
} BatchRenderer;

global BatchRenderer renderer;

internal void renderer_init(void) {
    renderer.shader = sg_make_shader(simple_shader_desc(sg_query_backend()));
    
    renderer.bind.vertex_buffers[0] = sg_make_buffer(&(sg_buffer_desc){
        .size = sizeof(renderer.quads),
        .usage = {
            .vertex_buffer = true,
            .dynamic_update = true,
        },
    });
    
    sg_pipeline_desc pdesc = {0};
    
    pdesc.layout.attrs[0].format = SG_VERTEXFORMAT_FLOAT2; // pos
    pdesc.layout.attrs[1].format = SG_VERTEXFORMAT_FLOAT2; // uv
    pdesc.layout.attrs[2].format = SG_VERTEXFORMAT_FLOAT4; // color
    pdesc.shader = renderer.shader;

    renderer.pip = sg_make_pipeline(&pdesc);
    renderer.count = 0;
}

internal void renderer_begin(void) {
    renderer.count = 0;
}

internal void renderer_push(Pos pos, Size size, UV uv, Color color) {
    if (renderer.count >= MAX_BATCHES) {
        return;
    }

    Quad* quad = &renderer.quads[renderer.count++];
    quad->verts[0] = (Vertex){ .pos = {pos.x, pos.y}, .uv = uv, .color = color };
    quad->verts[1] = (Vertex){ .pos = {pos.x+size.w, pos.y}, .uv = uv, .color = color };
    quad->verts[2] = (Vertex){ .pos = {pos.x+size.w, pos.y+size.h}, .uv = uv, .color = color };
    quad->verts[3] = (Vertex){ .pos = {pos.x, pos.y+size.h}, .uv = uv, .color = color };
}

internal void renderer_flush(void) {
    if (renderer.count == 0) return;
    
    sg_update_buffer(renderer.bind.vertex_buffers[0], &(sg_range){
        .ptr = renderer.quads,
        .size = renderer.count * sizeof(Quad)
    });
    
    sg_apply_pipeline(renderer.pip);
    sg_apply_bindings(&renderer.bind);
    sg_draw(0, renderer.count * 4, 1);
}

////////////////////////////////////////////////////////////////////////////////
// Coroutines (stackless, macros)
////////////////////////////////////////////////////////////////////////////////

#define CORO_BEGIN(ctx) switch((ctx).line) { case 0:
#define CORO_YIELD(ctx) do { (ctx).line = __LINE__; return; case __LINE__:; } while (0)
#define CORO_END(ctx) } (ctx).done = 1; return

typedef struct {
    int line;
    int done;
} Coroutine;

////////////////////////////////////////////////////////////////////////////////
// Hot reload stub
////////////////////////////////////////////////////////////////////////////////

#ifdef DEBUG

#endif

////////////////////////////////////////////////////////////////////////////////
// Game state
//////////////////////////////////////////////////////////////////////////////////

typedef struct {
    float t;
    Coroutine co;
} GameState;

global GameState *game;

internal void game_update(void) {
    persist int i;

    game->t += 0.016f;

    if (!game->co.done) {
        CORO_BEGIN(game->co);
        for (i=0;i<100;i++) {
            Pos pos = { -0.5f+(i*0.01f), -0.5f };
            Size size = { 0.01f, 0.01f };
            UV uv = { 0, 0 };
            Color color = { 1, 0, 0, 1 };
            renderer_push(pos, size, uv, color);
            CORO_YIELD(game->co);
        }
        CORO_END(game->co);
    }
}

////////////////////////////////////////////////////////////////////////////////
// Sokol callbacks
//////////////////////////////////////////////////////////////////////////////////

internal void init(void) {
    sg_setup(&(sg_desc){
        .environment = sglue_environment(),
    });

    renderer_init();

    size_t memory_size = Megabytes(16);
    void* game_memory = VirtualAlloc(0, memory_size, MEM_RESERVE|MEM_COMMIT, PAGE_READWRITE);
    Arena arena = arena_create(game_memory, memory_size);
    game = arena_zalloc(&arena, sizeof(GameState), 1);
    stm_setup();
}

internal void frame(void) {
    renderer_begin();
    game_update();
    sg_begin_pass(&(sg_pass){ .action = (sg_pass_action){0}, .swapchain = sglue_swapchain() });
    renderer_flush();
    sg_end_pass();
    sg_commit();
}

internal void cleanup(void) {
    sg_shutdown();
}

internal void event(const sapp_event* event) {
    (void)event;
}

sapp_desc sokol_main(int argc, char* argv[]) {
    return (sapp_desc) {
        .init_cb = init,
        .frame_cb = frame,
        .cleanup_cb = cleanup,
        .event_cb = event,
        .width = 1280,
        .height = 720,
        .high_dpi = true,
        .window_title = "Base Layer",
    };
}
