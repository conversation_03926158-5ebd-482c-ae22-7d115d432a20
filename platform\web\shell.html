<!DOCTYPE html>
<html lang="en-us">
<head>
    <meta charset="utf-8">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>Aces</title>
    <style>
        body {
            font-family: arial;
            margin: 0;
            padding: 0;
            background: #222;
            color: white;
            overflow: hidden;
        }
        
        .emscripten {
            padding-right: 0;
            margin-left: auto;
            margin-right: auto;
            display: block;
        }
        
        div.emscripten {
            text-align: center;
        }
        
        div.emscripten_border {
            border: 1px solid black;
        }
        
        /* the canvas *must not* have any border or padding, or mouse coords will be wrong */
        canvas.emscripten {
            border: 0px none;
            background-color: black;
            display: block;
            margin: 0 auto;
        }
        
        .spinner {
            height: 50px;
            width: 50px;
            margin: 0px auto;
            -webkit-animation: rotation .8s linear infinite;
            -moz-animation: rotation .8s linear infinite;
            -o-animation: rotation .8s linear infinite;
            animation: rotation 0.8s linear infinite;
            border-left: 10px solid rgb(0,150,240);
            border-right: 10px solid rgb(0,150,240);
            border-bottom: 10px solid rgb(0,150,240);
            border-top: 10px solid rgb(100,0,200);
            border-radius: 100%;
            background-color: rgb(200,100,250);
        }
        
        @-webkit-keyframes rotation {
            from {-webkit-transform: rotate(0deg);}
            to {-webkit-transform: rotate(360deg);}
        }
        @-moz-keyframes rotation {
            from {-moz-transform: rotate(0deg);}
            to {-moz-transform: rotate(360deg);}
        }
        @-o-keyframes rotation {
            from {-o-transform: rotate(0deg);}
            to {-o-transform: rotate(360deg);}
        }
        @keyframes rotation {
            from {transform: rotate(0deg);}
            to {transform: rotate(360deg);}
        }

        #status {
            display: inline-block;
            vertical-align: top;
            margin-top: 20px;
            margin-left: 20px;
            font-weight: bold;
            color: rgb(120, 120, 120);
        }

        #progress {
            height: 20px;
            width: 300px;
        }

        #controls {
            display: inline-block;
            float: right;
            vertical-align: top;
            margin-top: 20px;
            margin-right: 20px;
        }

        #output {
            width: 100%;
            height: 200px;
            margin: 0 auto;
            margin-top: 10px;
            border-left: 0px;
            border-right: 0px;
            padding-left: 0px;
            padding-right: 0px;
            display: none;
            background-color: black;
            color: white;
            font-family: 'Lucida Console', Monaco, monospace;
            outline: none;
        }

        .fullscreen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw !important;
            height: 100vh !important;
            z-index: 9999;
        }

        #gameContainer {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
            box-sizing: border-box;
        }

        #gameTitle {
            font-size: 2em;
            margin-bottom: 20px;
            color: #fff;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }

        .control-button {
            background: #333;
            color: white;
            border: 2px solid #666;
            padding: 10px 15px;
            margin: 5px;
            cursor: pointer;
            border-radius: 5px;
            font-family: arial;
        }

        .control-button:hover {
            background: #555;
            border-color: #888;
        }

        .control-button:active {
            background: #777;
        }

        #loadingContainer {
            text-align: center;
            margin: 20px;
        }

        #loadingText {
            margin-top: 10px;
            color: #ccc;
        }
    </style>
</head>
<body>
    <div id="gameContainer">
        <h1 id="gameTitle">Aces</h1>
        
        <div id="loadingContainer">
            <div class="spinner" id="spinner"></div>
            <div id="loadingText">Loading...</div>
            <div class="emscripten" id="status">Downloading...</div>
            <div class="emscripten">
                <progress value="0" max="100" id="progress" hidden=1></progress>
            </div>
        </div>

        <div class="emscripten_border">
            <canvas class="emscripten" id="canvas" oncontextmenu="event.preventDefault()" tabindex=-1></canvas>
        </div>
        
        <div id="controls">
            <button class="control-button" onclick="Module.requestFullscreen(false, false)">Fullscreen</button>
            <button class="control-button" onclick="toggleOutput()">Toggle Output</button>
        </div>
        
        <textarea class="emscripten" id="output" readonly></textarea>
    </div>

    <script type='text/javascript'>
        var statusElement = document.getElementById('status');
        var progressElement = document.getElementById('progress');
        var spinnerElement = document.getElementById('spinner');
        var loadingContainer = document.getElementById('loadingContainer');
        var canvas = document.getElementById('canvas');

        function toggleOutput() {
            var output = document.getElementById('output');
            output.style.display = output.style.display === 'none' ? 'block' : 'none';
        }

        // Resize canvas to fit container while maintaining aspect ratio
        function resizeCanvas() {
            var container = document.getElementById('gameContainer');
            var maxWidth = Math.min(window.innerWidth - 40, 1024);
            var maxHeight = Math.min(window.innerHeight - 200, 768);
            
            var aspectRatio = 4/3; // Adjust based on your game's aspect ratio
            
            if (maxWidth / aspectRatio <= maxHeight) {
                canvas.width = maxWidth;
                canvas.height = maxWidth / aspectRatio;
            } else {
                canvas.width = maxHeight * aspectRatio;
                canvas.height = maxHeight;
            }
        }

        window.addEventListener('resize', resizeCanvas);
        resizeCanvas();

        var Module = {
            preRun: [],
            postRun: [],
            print: (function() {
                var element = document.getElementById('output');
                if (element) element.value = ''; // clear browser cache
                return function(text) {
                    if (arguments.length > 1) text = Array.prototype.slice.call(arguments).join(' ');
                    console.log(text);
                    if (element) {
                        element.value += text + "\n";
                        element.scrollTop = element.scrollHeight; // focus on bottom
                    }
                };
            })(),
            canvas: (function() {
                var canvas = document.getElementById('canvas');
                
                // As a default initial behavior, pop up an alert when webgl context is lost.
                canvas.addEventListener("webglcontextlost", function(e) { 
                    alert('WebGL context lost. You will need to reload the page.'); 
                    e.preventDefault(); 
                }, false);

                return canvas;
            })(),
            setStatus: function(text) {
                if (!Module.setStatus.last) Module.setStatus.last = { time: Date.now(), text: '' };
                if (text === Module.setStatus.last.text) return;
                var m = text.match(/([^(]+)\((\d+(\.\d+)?)\/(\d+)\)/);
                var now = Date.now();
                if (m && now - Module.setStatus.last.time < 30) return; // if this is a progress update, skip it if too soon
                Module.setStatus.last.time = now;
                Module.setStatus.last.text = text;
                if (statusElement) statusElement.innerHTML = text;
            },
            totalDependencies: 0,
            monitorRunDependencies: function(left) {
                this.totalDependencies = Math.max(this.totalDependencies, left);
                Module.setStatus(left ? 'Preparing... (' + (this.totalDependencies-left) + '/' + this.totalDependencies + ')' : 'All downloads complete.');
            },
            onRuntimeInitialized: function() {
                // Hide loading elements
                loadingContainer.style.display = 'none';
                
                // Show the canvas
                canvas.style.display = 'block';
                
                // Final resize
                resizeCanvas();
                
                Module.setStatus('');
            }
        };
        
        Module.setStatus('Downloading...');
        
        window.onerror = function() {
            Module.setStatus('Exception thrown, see JavaScript console');
            spinnerElement.style.display = 'none';
            Module.setStatus = function(text) {
                if (text) console.error('[post-exception status] ' + text);
            };
        };
        
        // Handle fullscreen
        Module.requestFullscreen = function(lockPointer, resizeCanvas) {
            var canvas = Module.canvas;
            function fullscreenChange() {
                if (document.fullscreenElement === canvas ||
                    document.mozFullScreenElement === canvas ||
                    document.msFullscreenElement === canvas ||
                    document.webkitFullscreenElement === canvas) {
                    canvas.classList.add('fullscreen');
                } else {
                    canvas.classList.remove('fullscreen');
                }
            }
            
            document.addEventListener('fullscreenchange', fullscreenChange, false);
            document.addEventListener('mozfullscreenchange', fullscreenChange, false);
            document.addEventListener('webkitfullscreenchange', fullscreenChange, false);
            document.addEventListener('MSFullscreenChange', fullscreenChange, false);
            
            if (canvas.requestFullscreen) {
                canvas.requestFullscreen();
            } else if (canvas.msRequestFullscreen) {
                canvas.msRequestFullscreen();
            } else if (canvas.mozRequestFullScreen) {
                canvas.mozRequestFullScreen();
            } else if (canvas.webkitRequestFullscreen) {
                canvas.webkitRequestFullscreen();
            }
        };
    </script>
    {{{ SCRIPT }}}
</body>
</html>