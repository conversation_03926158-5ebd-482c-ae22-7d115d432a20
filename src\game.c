#include "platform.h"

#include <stdint.h> // for uint8_t
#include <string.h> // for memset

#ifdef DEBUG
#define API __declspec(dllexport)
#else
#define API internal
#endif


////////////////////////////////////////////////////////////////////////////////
// Memory Arena
////////////////////////////////////////////////////////////////////////////////

internal Arena arena_create(void* base, size_t size) {
    return (Arena) {
        .base = (uint8_t*)base,
        .size = size,
        .offset = 0,
    };
}

internal void* arena_alloc(Arena* arena, size_t size, size_t align) {
    size_t aligned_offset = (arena->offset + align - 1) & ~(align - 1);
    if (aligned_offset + size > arena->size) {
        return NULL;
    }
    
    void* ptr = arena->base + aligned_offset;
    arena->offset = aligned_offset + size;
    return ptr;
}

internal void* arena_zalloc(Arena* arena, size_t size, size_t align) {
    void* ptr = arena_alloc(arena, size, align);
    if (ptr) {
        memset(ptr, 0, size);
    }
    return ptr;
}

internal void arena_free(Arena* arena) {
    arena->offset = 0;
}


////////////////////////////////////////////////////////////////////////////////
// Coroutines (stackless, macros)
////////////////////////////////////////////////////////////////////////////////

#define CORO_BEGIN(ctx) switch((ctx).line) { case 0:
#define CORO_YIELD(ctx) do { (ctx).line = __LINE__; return; case __LINE__:; } while (0)
#define CORO_RESET(ctx) do { (ctx).line = 0; (ctx).done = 0; return; } while (0)
#define CORO_END(ctx) } (ctx).done = 1; return

typedef struct {
    int line;
    int done;
} Coroutine;


////////////////////////////////////////////////////////////////////////////////
// Game state
//////////////////////////////////////////////////////////////////////////////////

typedef struct {
    float t;
    Coroutine co;
} GameState;

global GameState *game;

API void game_init(PlatformLayer* platform) {
    game = arena_zalloc(&platform->persistent_arena, sizeof(GameState), 1);
}

API void game_update(PlatformLayer* platform) {
    game->t += 0.016f;
    if (!game->co.done) {
        CORO_BEGIN(game->co);
        for (;;) {
            Pos pos = { -0.5f, -0.5f };
            Size size = { 0.5f, 0.5f };
            UV uv = { 0, 0 };
            Color color = { 1, 0, 0, 1 };
            platform->renderer_push(pos, size, uv, color);
            CORO_YIELD(game->co);
        }
        CORO_END(game->co);
    }
}
