@echo off
setlocal enabledelayedexpansion

:: Configuration
set GAME_NAME=game
set SRC_DIR=src
set EXTERNAL_DIR=external
set BUILD_DIR=build
set BUILD_INT_DIR=build-int
set PLATFORM_DIR=platform/windows

:: Compiler settings
set CC=cl
set CFLAGS_COMMON=/nologo /W3 /I%EXTERNAL_DIR% /I%SRC_DIR%
set CFLAGS_DEBUG=/Od /Zi /D_DEBUG /DDEBUG /MTd
set CFLAGS_RELEASE=/O2 /DNDEBUG /MT
set LIBS=kernel32.lib user32.lib gdi32.lib d3d11.lib
set SHADER_TOOL=%EXTERNAL_DIR%\sokol_tools\bin\win32\sokol-shdc.exe

:: Compile shaders
echo Compiling shaders...
%SHADER_TOOL% -i shaders\shader.glsl -o src\shader.glsl.h -l hlsl4 -b

:: Parse command line arguments
set BUILD_TYPE=debug
if "%1"=="release" set BUILD_TYPE=release
if "%1"=="Release" set BUILD_TYPE=release
if "%1"=="RELEASE" set BUILD_TYPE=release

:: Create directories
set OUT_DIR=%BUILD_DIR%\%BUILD_TYPE%\windows
set INT_DIR=%BUILD_INT_DIR%\%BUILD_TYPE%\windows
if not exist "%OUT_DIR%" mkdir "%OUT_DIR%"
if not exist "%INT_DIR%" mkdir "%INT_DIR%"

:: Set build-specific flags
if "%BUILD_TYPE%"=="debug" (
    set CFLAGS=%CFLAGS_COMMON% %CFLAGS_DEBUG%
    set EXE_NAME=%GAME_NAME%_debug.exe
) else (
    set CFLAGS=%CFLAGS_COMMON% %CFLAGS_RELEASE%
    set EXE_NAME=%GAME_NAME%.exe
)

:: Check if Visual Studio environment is set up
where cl >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo Error: Visual Studio compiler not found!
    echo Please run this script from a Visual Studio Developer Command Prompt
    echo or run vcvarsall.bat first.
    exit /b 1
)

echo Building %BUILD_TYPE% configuration...

:: Compile and link
%CC% %CFLAGS% ^
    /Fe%OUT_DIR%\%EXE_NAME% ^
    /Fo%INT_DIR%\ ^
    /Fd%INT_DIR%\%GAME_NAME%.pdb ^
    %SRC_DIR%\win32_main.c ^
    /link %LIBS% /SUBSYSTEM:WINDOWS

if %ERRORLEVEL% neq 0 (
    exit /b 1
)

:: Copy platform-specific files if they exist
if exist "%PLATFORM_DIR%\*" (
    echo Copying platform files...
    xcopy /Y /Q "%PLATFORM_DIR%\*" "%OUT_DIR%\" >nul 2>&1
)

endlocal
