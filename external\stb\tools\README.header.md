stb
===

single-file public domain (or MIT licensed) libraries for C/C++

# This project discusses security-relevant bugs in public in Github Issues and Pull Requests, and it may take significant time for security fixes to be implemented or merged. If this poses an unreasonable risk to your project, do not use stb libraries.

Noteworthy:

* image loader: [stb_image.h](stb_image.h)
* image writer: [stb_image_write.h](stb_image_write.h)
* image resizer: [stb_image_resize2.h](stb_image_resize2.h)
* font text rasterizer: [stb_truetype.h](stb_truetype.h)
* typesafe containers: [stb_ds.h](stb_ds.h)

Most libraries by stb, except: stb_dxt by <PERSON> "r<PERSON><PERSON>" <PERSON>, original stb_image_resize
by <PERSON> "Vin<PERSON>" <PERSON>, and stb_image_resize2 and stb_sprintf by <PERSON>.

<a name="stb_libs"></a>

library    | latest version | category | LoC | description
--------------------- | ---- | -------- | --- | --------------------------------
