# Aces - Cross-Platform Game

A cross-platform 2D game built with the [Sokol](https://github.com/floooh/sokol) graphics library and [sokol_gp](https://github.com/edubart/sokol_gp) for 2D rendering. This project uses a unibuild approach where all implementations are contained in a single `main.c` file.

## Supported Platforms

- **Windows** (DirectX 11)
- **Linux** (OpenGL 3.3 Core)
- **macOS** (Metal, App Bundle)
- **WebAssembly** (WebGL2)
- **Android** (OpenGL ES 3.0)
- **iOS** (Metal)
- **Steam Deck** (Optimized Linux build)
- **Atari VCS** (Custom Linux build)

## Project Structure

```
aces/
├── src/
│   └── main.c              # Main game source (unibuild)
├── external/
│   ├── sokol/              # Sokol headers (git submodule)
│   └── sokol_gp/           # sokol_gp header (git submodule)
├── platform/
│   └── macos/
│   │    └── Info.plist.in   # macOS app bundle template
│   └── web/
│        └── shell.html     # HTML shell for WebAssembly
├── scripts/
│   └── build.py            # Cross-platform build script
├── CMakeLists.txt          # CMake build configuration
└── README.md
```

## Prerequisites

### Common Requirements
- **CMake** 3.20 or later
- **Git** (for submodules)

### Platform-Specific Requirements

#### Windows
- **Visual Studio 2022** with C++ tools, OR
- **MinGW-w64** with GCC

#### Linux
- **GCC** or **Clang**
- **Development packages**:
  ```bash
  # Ubuntu/Debian
  sudo apt install build-essential cmake git libx11-dev libxi-dev libxcursor-dev libgl1-mesa-dev libasound2-dev

  # Fedora/RHEL
  sudo dnf install gcc-c++ cmake git libX11-devel libXi-devel libXcursor-devel mesa-libGL-devel alsa-lib-devel

  # Arch Linux
  sudo pacman -S base-devel cmake git libx11 libxi libxcursor mesa alsa-lib
  ```

#### macOS
- **Xcode** with Command Line Tools
- **CMake** (via Homebrew): `brew install cmake`

#### WebAssembly
- **Emscripten SDK**: Follow the [installation guide](https://emscripten.org/docs/getting_started/downloads.html)
  ```bash
  git clone https://github.com/emscripten-core/emsdk.git
  cd emsdk
  ./emsdk install latest
  ./emsdk activate latest
  source ./emsdk_env.sh  # Add to your shell profile
  ```

#### Android
- **Android Studio** with NDK
- Set environment variable: `export ANDROID_NDK_ROOT=/path/to/ndk`

#### iOS
- **Xcode** (macOS only)
- **iOS Development Certificate** and **Provisioning Profile**

## Building

### Initialize Submodules
```bash
git submodule update --init --recursive
```

### Quick Build (Native Platform)
```bash
# Using the build script (recommended)
python scripts/build.py

# Or using CMake directly
mkdir build && cd build
cmake ..
cmake --build .
```

### Platform-Specific Builds

#### Windows
```bash
# Using build script
python scripts/build.py windows --config Release --arch x64

# Using CMake
mkdir build-windows && cd build-windows
cmake -G "Visual Studio 17 2022" -A x64 ..
cmake --build . --config Release
```

#### Linux
```bash
# Using build script
python scripts/build.py linux --config Release

# Using CMake
mkdir build-linux && cd build-linux
cmake -DCMAKE_BUILD_TYPE=Release ..
cmake --build . --parallel
```

#### macOS (App Bundle)
```bash
# Using build script
python scripts/build.py macos --config Release

# Using CMake
mkdir build-macos && cd build-macos
cmake -G Xcode -DCMAKE_OSX_ARCHITECTURES="arm64;x86_64" ..
cmake --build . --config Release
```

#### WebAssembly
```bash
# Activate Emscripten environment first
source /path/to/emsdk/emsdk_env.sh

# Using build script
python scripts/build.py web --config Release

# Using CMake
mkdir build-web && cd build-web
emcmake cmake -DCMAKE_BUILD_TYPE=Release ..
cmake --build .
```

#### Android
```bash
# Set NDK path
export ANDROID_NDK_ROOT=/path/to/android-ndk

# Using build script
python scripts/build.py android --config Release --android-abi arm64-v8a

# Using CMake
mkdir build-android && cd build-android
cmake -DCMAKE_TOOLCHAIN_FILE=$ANDROID_NDK_ROOT/build/cmake/android.toolchain.cmake \
      -DANDROID_ABI=arm64-v8a \
      -DANDROID_PLATFORM=android-21 \
      -DCMAKE_BUILD_TYPE=Release ..
cmake --build .
```

#### iOS
```bash
# Using build script (macOS only)
python scripts/build.py ios --config Release

# Using CMake
mkdir build-ios && cd build-ios
cmake -G Xcode -DCMAKE_SYSTEM_NAME=iOS \
      -DCMAKE_OSX_ARCHITECTURES=arm64 ..
cmake --build . --config Release
```

#### Steam Deck
```bash
# Optimized Linux build with Steam Deck CPU optimizations
python scripts/build.py steamdeck --config Release
```

#### Atari VCS
```bash
# Set VCS SDK path (if available)
export ATARI_VCS_SDK=/path/to/vcs-sdk

# Using build script
python scripts/build.py atarivcs --config Release
```

### Build All Platforms
```bash
python scripts/build.py all --config Release
```

## Running

### Desktop Platforms
```bash
# Windows
build/windows/x64/bin/aces.exe

# Linux
build/linux/bin/aces

# macOS (App Bundle)
open build/macos/Release/aces.app
```

### WebAssembly
```bash
# Serve the HTML file (requires a web server due to CORS)
cd build/web
python -m http.server 8000
# Open http://localhost:8000/aces.html in your browser
```

### Mobile Platforms
- **Android**: Use Android Studio to create an APK with the generated .so file
- **iOS**: Open the generated Xcode project and deploy to device

## Game Controls

- **WASD** or **Arrow Keys**: Move player
- **Mouse/Touch**: Move player to cursor/touch position
- **Escape**: Quit game
- **F11**: Toggle fullscreen (desktop platforms)

## Development

### Adding New Features
1. Edit `src/main.c` - all game logic is in this single file
2. Add new sokol headers to `CMakeLists.txt` if needed
3. Test on multiple platforms using the build script

### Debugging
```bash
# Debug builds
python scripts/build.py --config Debug

# WebAssembly with debug info
python scripts/build.py web --config Debug
# Includes source maps and assertions
```

### Asset Loading
- Place assets in an `assets/` directory
- WebAssembly: Assets are preloaded automatically
- Desktop: Copy assets to executable directory
- Mobile: Assets are bundled with the app

## Sokol Features Used

- **sokol_app**: Cross-platform application framework
- **sokol_gfx**: 3D graphics API abstraction
- **sokol_gp**: 2D graphics primitives
- **sokol_time**: High-resolution timing
- **sokol_audio**: Cross-platform audio
- **sokol_log**: Logging system

## Architecture

This project uses a "unibuild" approach where:
- All platform-specific code is contained in `main.c`
- Conditional compilation selects the appropriate backend
- No separate platform layers or wrapper libraries
- Direct use of sokol's cross-platform abstractions

## Performance Notes

### Platform-Specific Optimizations
- **Windows**: Uses DirectX 11 for optimal performance
- **macOS/iOS**: Uses Metal for native performance
- **Linux**: OpenGL 3.3 Core Profile
- **WebAssembly**: WebGL2 with optimized flags
- **Android**: OpenGL ES 3.0
- **Steam Deck**: AMD Zen 2 CPU optimizations

### Memory Usage
- Designed for low memory usage
- No external dependencies beyond sokol
- Efficient 2D rendering with sokol_gp

## Contributing

1. Fork the repository
2. Create a feature branch
3. Test on multiple platforms using `build.py all`
4. Submit a pull request

## License

[Add your license here]

## Credits

- [Sokol](https://github.com/floooh/sokol) by Andre Weissflog
- [sokol_gp](https://github.com/edubart/sokol_gp) by Eduardo Bart
