name: CIFuzz
on: [pull_request]
jobs:
  Fuzzing:
    runs-on: ubuntu-latest
    steps:
    - name: Build Fuzzers
      uses: google/oss-fuzz/infra/cifuzz/actions/build_fuzzers@master
      with:
        oss-fuzz-project-name: 'stb'
        dry-run: false
    - name: Run Fuzzers
      uses: google/oss-fuzz/infra/cifuzz/actions/run_fuzzers@master
      with:
        oss-fuzz-project-name: 'stb'
        fuzz-seconds: 900
        dry-run: false
    - name: Upload Crash
      uses: actions/upload-artifact@v1
      if: failure()
      with:
        name: artifacts
        path: ./out/artifacts
