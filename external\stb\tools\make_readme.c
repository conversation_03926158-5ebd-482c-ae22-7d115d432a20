#define STB_DEFINE
#include "../stb.h"

int main(int argc, char  **argv)
{
   int i;
   int hlen, flen, listlen, total_lines = 0;
   char *header = stb_file("README.header.md", &hlen);      // stb_file - read file into malloc()ed buffer
   char *footer = stb_file("README.footer.md", &flen);      // stb_file - read file into malloc()ed buffer
   char **list  = stb_stringfile("README.list", &listlen);  // stb_stringfile - read file lines into malloced array of strings

   FILE *f = fopen("../README.md", "wb");

   fprintf(f, "<!---   THIS FILE IS AUTOMATICALLY GENERATED, DO NOT CHANGE IT BY HAND   --->\r\n\r\n");
   fwrite(header, 1, hlen, f);

   for (i=0; i < listlen; ++i) {
      int num,j;
      char **tokens = stb_tokens_stripwhite(list[i], "|", &num);  // stb_tokens -- tokenize string into malloced array of strings
      int num_lines;
      char **lines = stb_stringfile(stb_sprintf("../%s", tokens[0]), &num_lines);
      char *s1, *s2,*s3;
      if (lines == NULL) stb_fatal("Couldn't open '%s'", tokens[0]);
      s1 = strchr(lines[0], '-');
      if (!s1) stb_fatal("Couldn't find '-' before version number in %s", tokens[0]); // stb_fatal -- print error message & exit
      s2 = strchr(s1+2, '-');
      if (!s2) stb_fatal("Couldn't find '-' after version number in %s", tokens[0]);  // stb_fatal -- print error message & exit
      *s2 = 0;
      s1 += 1;
      s1 = stb_trimwhite(s1);                  // stb_trimwhite -- advance pointer to after whitespace & delete trailing whitespace
      if (*s1 == 'v') ++s1;
      s3 = tokens[0];
      stb_trimwhite(s3);
      fprintf(f, "**[");
      if (strlen(s3) < 21) {
         fprintf(f, "%s", tokens[0]);
      } else {
         char buffer[256];
         strncpy(buffer, s3, 18);
         buffer[18] = 0;   
         strcat(buffer, "...");
         fprintf(f, "%s", buffer);
      }
      fprintf(f, "](%s)**", tokens[0]);
      fprintf(f, " | %s", s1);
      s1 = stb_trimwhite(tokens[1]);           // stb_trimwhite -- advance pointer to after whitespace & delete trailing whitespace
      s2 = stb_dupreplace(s1, " ", "&nbsp;");  // stb_dupreplace -- search & replace string and malloc result
      fprintf(f, " | %s", s2);
      free(s2);
      fprintf(f, " | %d", num_lines);
      total_lines += num_lines;
      for (j=2; j < num; ++j)
         fprintf(f, " | %s", tokens[j]);
      fprintf(f, "\r\n");
   }

   fprintf(f, "\r\n");
   fprintf(f, "Total libraries: %d\r\n", listlen);
   fprintf(f, "Total lines of C code: %d\r\n\r\n", total_lines);

   fwrite(footer, 1, flen, f);
   fclose(f);

   return 0;
}
