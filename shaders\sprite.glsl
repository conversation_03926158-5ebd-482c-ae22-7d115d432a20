@vs vs_main
in vec2 pos;
in vec2 uv;
in vec4 color;
out vec2 uv_;
out vec4 color_;
void main() {
    gl_Position = vec4(pos, 0.0, 1.0);
    uv_ = uv;
    color_ = color;
}
@end

@fs fs_main
in vec2 uv_;
in vec4 color_;
out vec4 frag_color;
layout(binding=0) uniform texture2D tex;
layout(binding=0) uniform sampler tex_smp;
void main() {
    frag_color = texture(sampler2D(tex, tex_smp), uv_) * color_;
}
@end

@program sprite vs_main fs_main
