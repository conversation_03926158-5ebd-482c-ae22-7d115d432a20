@vs vs_main
in vec2 pos;
out vec2 uv;
void main() {
    gl_Position = vec4(pos, 0.0, 1.0);
    uv = (pos + 1.0) * 0.5;
}
@end

@fs fs_main
in vec2 uv;
out vec4 frag_color;
layout(binding=0) uniform texture2D scene_tex;
layout(binding=0) uniform sampler scene_smp;
void main() {
    vec4 c = texture(sampler2D(scene_tex, scene_smp), uv);
    // exemplo simples: inverter cores
    frag_color = vec4(1.0 - c.rgb, 1.0);
}
@end

@program post vs_main fs_main