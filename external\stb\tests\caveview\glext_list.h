GLARB(<PERSON>T<PERSON>ture,ACTIVETEXTURE)
GLARB(ClientActiveTexture,CL<PERSON><PERSON>ACTIVETEXTURE)
GLARB(MultiTexCoord2f,MULTITEXCOORD2F)
GLEXT(TexImage3D,TEXIMAGE3D)
GLEXT(TexSub<PERSON>mage3D,<PERSON><PERSON><PERSON><PERSON>MAGE3D)
GLEXT(GenerateMipmap,GENERA<PERSON>MIPMAP)
GLARB(DebugMessageCallback,DEBU<PERSON><PERSON><PERSON><PERSON>CALLBACK)

GLCORE(VertexAttribIPointer,VERTEXATTRIBIPOINTER)

GLEXT(BindFramebuffer,BINDFRAMEBUFFER)
GLEXT(DeleteFramebuffers,DELETEFRAMEBUFFERS)
GLEXT(GenFramebuffers,<PERSON><PERSON><PERSON>MEBUFFERS)
GLEXT(CheckFramebufferStatus,CHEC<PERSON><PERSON><PERSON>MEBUFFERSTATUS)
GLEXT(FramebufferTexture2D,FRAMEBUFFERTEXTURE2D)
GLEXT(BindRenderBuffer,B<PERSON><PERSON><PERSON><PERSON>RBUFFER)
GLEXT(RenderbufferStorage,R<PERSON><PERSON><PERSON><PERSON>FFERSTORAGE)
GLEXT(GenRenderbu<PERSON>s,<PERSON>NR<PERSON><PERSON>RBUFFERS)
GLEXT(BindRenderbuffer,BIN<PERSON><PERSON><PERSON>RBUFFER)
GLEXT(FramebufferRenderbuffer,FRAMEBUFFERRENDERBUFFER)
GLEXT(GenerateMipmap,GENERATEMIPMAP)

GLARB(BindBuffer   ,BINDBUFFER,)
GLARB(GenBuffers   ,GENBUFFERS   )
GLARB(DeleteBuffers,DELETEBUFFERS)
GLARB(BufferData   ,BUFFERDATA   )
GLARB(BufferSubData,BUFFERSUBDATA)
GLARB(MapBuffer    ,MAPBUFFER    )
GLARB(UnmapBuffer  ,UNMAPBUFFER  )
GLARB(TexBuffer    ,TEXBUFFER    )

GLEXT(NamedBufferStorage,NAMEDBUFFERSTORAGE)
GLE(BufferStorage,BUFFERSTORAGE)
GLE(GetStringi,GETSTRINGI)