#if defined(SOK<PERSON>_IMPL) && !defined(SOKOL_DEBUGTEXT_IMPL)
#define SOKOL_DEBUGTEXT_IMPL
#endif
#ifndef SOKOL_DEBUGTEXT_INCLUDED
/*
    sokol_debugtext.h   - simple ASCII debug text rendering on top of sokol_gfx.h

    Project URL: https://github.com/floooh/sokol

    Do this:
        #define SOKOL_IMPL or
        #define SOKOL_DEBUGTEXT_IMPL
    before you include this file in *one* C or C++ file to create the
    implementation.

    The following defines are used by the implementation to select the
    platform-specific embedded shader code (these are the same defines as
    used by sokol_gfx.h and sokol_app.h):

    SOKOL_GLCORE
    SOKOL_GLES3
    SOKOL_D3D11
    SOKOL_METAL
    SOKOL_WGPU

    ...optionally provide the following macros to override defaults:

    SOKOL_VSNPRINTF     - the function name of an alternative vsnprintf() function (default: vsnprintf)
    SOKOL_ASSERT(c)     - your own assert macro (default: assert(c))
    SOKOL_DEBUGTEXT_API_DECL    - public function declaration prefix (default: extern)
    SOKOL_API_DECL      - same as <PERSON><PERSON><PERSON>_DEBUGTEXT_API_DECL
    SOKOL_API_IMPL      - public function implementation prefix (default: -)
    SOKOL_UNREACHABLE() - a guard macro for unreachable code (default: assert(false))

    If sokol_debugtext.h is compiled as a DLL, define the following before
    including the declaration or implementation:

    SOKOL_DLL

    On Windows, SOKOL_DLL will define SOKOL_DEBUGTEXT_API_DECL as __declspec(dllexport)
    or __declspec(dllimport) as needed.

    Include the following headers before including sokol_debugtext.h:

        sokol_gfx.h

    FEATURES AND CONCEPTS
    =====================
    - renders 8-bit ASCII text as fixed-size 8x8 pixel characters
    - comes with 6 embedded 8-bit home computer fonts (each taking up 2 KBytes)
    - easily plug in your own fonts
    - create multiple contexts for rendering text in different layers or render passes

    STEP BY STEP
    ============

    --- to initialize sokol-debugtext, call sdtx_setup() *after* initializing
        sokol-gfx:

            sdtx_setup(&(sdtx_desc_t){ ... });

        To see any warnings and errors, you should always install a logging callback.
        The easiest way is via sokol_log.h:

            #include "sokol_log.h"

            sdtx_setup(&(sdtx_desc_t){
                .logger.func = slog_func,
            });

    --- configure sokol-debugtext by populating the sdtx_desc_t struct:

        .context_pool_size (default: 8)
            The max number of text contexts that can be created.

        .printf_buf_size (default: 4096)
            The size of the internal text formatting buffer used by
            sdtx_printf() and sdtx_vprintf().

        .fonts (default: none)
            An array of sdtx_font_desc_t structs used to configure the
            fonts that can be used for rendering. To use all builtin
            fonts call sdtx_setup() like this (in C99):

            sdtx_setup(&(sdtx_desc_t){
                .fonts = {
                    [0] = sdtx_font_kc853(),
                    [1] = sdtx_font_kc854(),
                    [2] = sdtx_font_z1013(),
                    [3] = sdtx_font_cpc(),
                    [4] = sdtx_font_c64(),
                    [5] = sdtx_font_oric()
                }
            });

            For documentation on how to use you own font data, search
            below for "USING YOUR OWN FONT DATA".

        .context
            The setup parameters for the default text context. This will
            be active right after sdtx_setup(), or when calling
            sdtx_set_context(SDTX_DEFAULT_CONTEXT):

            .max_commands (default: 4096)
                The max number of render commands that can be recorded
                into the internal command buffer. This directly translates
                to the number of render layer changes in a single frame.

            .char_buf_size (default: 4096)
                The number of characters that can be rendered per frame in this
                context, defines the size of an internal fixed-size vertex
                buffer.  Any additional characters will be silently ignored.

            .canvas_width (default: 640)
            .canvas_height (default: 480)
                The 'virtual canvas size' in pixels. This defines how big
                characters will be rendered relative to the default framebuffer
                dimensions. Each character occupies a grid of 8x8 'virtual canvas
                pixels' (so a virtual canvas size of 640x480 means that 80x60 characters
                fit on the screen). For rendering in a resizeable window, you
                should dynamically update the canvas size in each frame by
                calling sdtx_canvas(w, h).

            .tab_width (default: 4)
                The width of a tab character in number of character cells.

            .color_format (default: 0)
            .depth_format (default: 0)
            .sample_count (default: 0)
                The pixel format description for the default context needed
                for creating the context's sg_pipeline object. When
                rendering to the default framebuffer you can leave those
                zero-initialized, in this case the proper values will be
                filled in by sokol-gfx. You only need to provide non-default
                values here when rendering to render targets with different
                pixel format attributes than the default framebuffer.

    --- Before starting to render text, optionally call sdtx_canvas() to
        dynamically resize the virtual canvas. This is recommended when
        rendering to a resizeable window. The virtual canvas size can
        also be used to scale text in relation to the display resolution.

        Examples when using sokol-app:

        - to render characters at 8x8 'physical pixels':

            sdtx_canvas(sapp_width(), sapp_height());

        - to render characters at 16x16 physical pixels:

            sdtx_canvas(sapp_width()/2.0f, sapp_height()/2.0f);

        Do *not* use integer math here, since this will not look nice
        when the render target size isn't divisible by 2.

    --- Optionally define the origin for the character grid with:

            sdtx_origin(x, y);

        The provided coordinates are in character grid cells, not in
        virtual canvas pixels. E.g. to set the origin to 2 character tiles
        from the left and top border:

            sdtx_origin(2, 2);

        You can define fractions, e.g. to start rendering half
        a character tile from the top-left corner:

            sdtx_origin(0.5f, 0.5f);

    --- Optionally set a different font by calling:

            sdtx_font(font_index)

        sokol-debugtext provides 8 font slots which can be populated
        with the builtin fonts or with user-provided font data, so
        'font_index' must be a number from 0 to 7.

    --- Position the text cursor with one of the following calls. All arguments
        are in character grid cells as floats and relative to the
        origin defined with sdtx_origin():

            sdtx_pos(x, y)      - sets absolute cursor position
            sdtx_pos_x(x)       - only set absolute x cursor position
            sdtx_pos_y(y)       - only set absolute y cursor position

            sdtx_move(x, y)     - move cursor relative in x and y direction
            sdtx_move_x(x)      - move cursor relative only in x direction
            sdtx_move_y(y)      - move cursor relative only in y direction

            sdtx_crlf()         - set cursor to beginning of next line
                                  (same as sdtx_pos_x(0) + sdtx_move_y(1))
            sdtx_home()         - resets the cursor to the origin
                                  (same as sdtx_pos(0, 0))

    --- Set a new text color with any of the following functions:

            sdtx_color3b(r, g, b)       - RGB 0..255, A=255
            sdtx_color3f(r, g, b)       - RGB 0.0f..1.0f, A=1.0f
            sdtx_color4b(r, g, b, a)    - RGBA 0..255
            sdtx_color4f(r, g, b, a)    - RGBA 0.0f..1.0f
            sdtx_color1i(uint32_t rgba) - ABGR (0xAABBGGRR)

    --- Output 8-bit ASCII text with the following functions:

            sdtx_putc(c)             - output a single character

            sdtx_puts(str)           - output a null-terminated C string, note that
                                       this will *not* append a newline (so it behaves
                                       differently than the CRT's puts() function)

            sdtx_putr(str, len)     - 'put range' output the first 'len' characters of
                                       a C string or until the zero character is encountered

            sdtx_printf(fmt, ...)   - output with printf-formatting, note that you
                                      can inject your own printf-compatible function
                                      by overriding the SOKOL_VSNPRINTF define before
                                      including the implementation

            sdtx_vprintf(fmt, args) - same as sdtx_printf() but with the arguments
                                      provided in a va_list

        - Note that the text will not yet be rendered, only recorded for rendering
          at a later time, the actual rendering happens when sdtx_draw() is called
          inside a sokol-gfx render pass.
        - This means also you can output text anywhere in the frame, it doesn't
          have to be inside a render pass.
        - Note that character codes <32 are reserved as control characters
          and won't render anything. Currently only the following control
          characters are implemented:

            \r  - carriage return (same as sdtx_pos_x(0))
            \n  - carriage return + line feed (same as stdx_crlf())
            \t  - a tab character

    --- You can 'record' text into render layers, this allows to mix/interleave
        sokol-debugtext rendering with other rendering operations inside
        sokol-gfx render passes. To start recording text into a different render
        layer, call:

            sdtx_layer(int layer_id)

        ...outside a sokol-gfx render pass.

    --- finally, from within a sokol-gfx render pass, call:

            sdtx_draw()

        ...for non-layered rendering, or to draw a specific layer:

            sdtx_draw_layer(int layer_id)

        NOTE that sdtx_draw() is equivalent to:

            sdtx_draw_layer(0)

        ...so sdtx_draw() will *NOT* render all text layers, instead it will
        only render the 'default layer' 0.

    --- at the end of a frame (defined by the call to sg_commit()), sokol-debugtext
        will rewind all contexts:

            - the internal vertex index is set to 0
            - the internal command index is set to 0
            - the current layer id is set to 0
            - the current font is set to 0
            - the cursor position is reset


    RENDERING WITH MULTIPLE CONTEXTS
    ================================
    Use multiple text contexts if you need to render debug text in different
    sokol-gfx render passes, or want to render text to different layers
    in the same render pass, each with its own set of parameters.

    To create a new text context call:

        sdtx_context ctx = sdtx_make_context(&(sdtx_context_desc_t){ ... });

    The creation parameters in the sdtx_context_desc_t struct are the same
    as already described above in the sdtx_setup() function:

        .char_buf_size      -- max number of characters rendered in one frame, default: 4096
        .canvas_width       -- the initial virtual canvas width, default: 640
        .canvas_height      -- the initial virtual canvas height, default: 400
        .tab_width          -- tab width in number of characters, default: 4
        .color_format       -- color pixel format of target render pass
        .depth_format       -- depth pixel format of target render pass
        .sample_count       -- MSAA sample count of target render pass

    To make a new context the active context, call:

        sdtx_set_context(ctx)

    ...and after that call the text output functions as described above, and
    finally, inside a sokol-gfx render pass, call sdtx_draw() to actually
    render the text for this context.

    A context keeps track of the following parameters:

        - the active font
        - the virtual canvas size
        - the origin position
        - the current cursor position
        - the current tab width
        - the current color
        - and the current layer-id

    You can get the currently active context with:

        sdtx_get_context()

    To make the default context current, call sdtx_set_context() with the
    special SDTX_DEFAULT_CONTEXT handle:

        sdtx_set_context(SDTX_DEFAULT_CONTEXT)

    Alternatively, use the function sdtx_default_context() to get the default
    context handle:

        sdtx_set_context(sdtx_default_context());

    To destroy a context, call:

        sdtx_destroy_context(ctx)

    If a context is set as active that no longer exists, all sokol-debugtext
    functions that require an active context will silently fail.

    You can directly draw the recorded text in a specific context without
    setting the active context:

        sdtx_context_draw(ctx)
        sdtx_context_draw_layer(ctx, layer_id)

    USING YOUR OWN FONT DATA
    ========================

    Instead of the built-in fonts you can also plug your own font data
    into sokol-debugtext by providing one or several sdtx_font_desc_t
    structures in the sdtx_setup call.

    For instance to use a built-in font at slot 0, and a user-font at
    font slot 1, the sdtx_setup() call might look like this:

        sdtx_setup(&sdtx_desc_t){
            .fonts = {
                [0] = sdtx_font_kc853(),
                [1] = {
                    .data = {
                        .ptr = my_font_data,
                        .size = sizeof(my_font_data)
                    },
                    .first_char = ...,
                    .last_char = ...
                }
            }
        });

    Where 'my_font_data' is a byte array where every character is described
    by 8 bytes arranged like this:

        bits
        7 6 5 4 3 2 1 0
        . . . X X . . .     byte 0: 0x18
        . . X X X X . .     byte 1: 0x3C
        . X X . . X X .     byte 2: 0x66
        . X X . . X X .     byte 3: 0x66
        . X X X X X X .     byte 4: 0x7E
        . X X . . X X .     byte 5: 0x66
        . X X . . X X .     byte 6: 0x66
        . . . . . . . .     byte 7: 0x00

    A complete font consists of 256 characters, resulting in 2048 bytes for
    the font data array (but note that the character codes 0..31 will never
    be rendered).

    If you provide such a complete font data array, you can drop the .first_char
    and .last_char initialization parameters since those default to 0 and 255,
    note that you can also use the SDTX_RANGE() helper macro to build the
    .data item:

        sdtx_setup(&sdtx_desc_t){
            .fonts = {
                [0] = sdtx_font_kc853(),
                [1] = {
                    .data = SDTX_RANGE(my_font_data)
                }
            }
        });

    If the font doesn't define all 256 character tiles, or you don't need an
    entire 256-character font and want to save a couple of bytes, use the
    .first_char and .last_char initialization parameters to define a sub-range.
    For instance if the font only contains the characters between the Space
    (ASCII code 32) and uppercase character 'Z' (ASCII code 90):

        sdtx_setup(&sdtx_desc_t){
            .fonts = {
                [0] = sdtx_font_kc853(),
                [1] = {
                    .data = SDTX_RANGE(my_font_data),
                    .first_char = 32,       // could also write ' '
                    .last_char = 90         // could also write 'Z'
                }
            }
        });

    Character tiles that haven't been defined in the font will be rendered
    as a solid 8x8 quad.


    MEMORY ALLOCATION OVERRIDE
    ==========================
    You can override the memory allocation functions at initialization time
    like this:

        void* my_alloc(size_t size, void* user_data) {
            return malloc(size);
        }

        void my_free(void* ptr, void* user_data) {
            free(ptr);
        }

        ...
            sdtx_setup(&(sdtx_desc_t){
                // ...
                .allocator = {
                    .alloc_fn = my_alloc,
                    .free_fn = my_free,
                    .user_data = ...;
                }
            });
        ...

    If no overrides are provided, malloc and free will be used.


    ERROR REPORTING AND LOGGING
    ===========================
    To get any logging information at all you need to provide a logging callback in the setup call,
    the easiest way is to use sokol_log.h:

        #include "sokol_log.h"

        sdtx_setup(&(sdtx_desc_t){
            // ...
            .logger.func = slog_func
        });

    To override logging with your own callback, first write a logging function like this:

        void my_log(const char* tag,                // e.g. 'sdtx'
                    uint32_t log_level,             // 0=panic, 1=error, 2=warn, 3=info
                    uint32_t log_item_id,           // SDTX_LOGITEM_*
                    const char* message_or_null,    // a message string, may be nullptr in release mode
                    uint32_t line_nr,               // line number in sokol_debugtext.h
                    const char* filename_or_null,   // source filename, may be nullptr in release mode
                    void* user_data)
        {
            ...
        }

    ...and then setup sokol-debugtext like this:

        sdtx_setup(&(sdtx_desc_t){
            .logger = {
                .func = my_log,
                .user_data = my_user_data,
            }
        });

    The provided logging function must be reentrant (e.g. be callable from
    different threads).

    If you don't want to provide your own custom logger it is highly recommended to use
    the standard logger in sokol_log.h instead, otherwise you won't see any warnings or
    errors.


    LICENSE
    =======
    zlib/libpng license

    Copyright (c) 2020 Andre Weissflog

    This software is provided 'as-is', without any express or implied warranty.
    In no event will the authors be held liable for any damages arising from the
    use of this software.

    Permission is granted to anyone to use this software for any purpose,
    including commercial applications, and to alter it and redistribute it
    freely, subject to the following restrictions:

        1. The origin of this software must not be misrepresented; you must not
        claim that you wrote the original software. If you use this software in a
        product, an acknowledgment in the product documentation would be
        appreciated but is not required.

        2. Altered source versions must be plainly marked as such, and must not
        be misrepresented as being the original software.

        3. This notice may not be removed or altered from any source
        distribution.
*/
#define SOKOL_DEBUGTEXT_INCLUDED (1)
#include <stdint.h>
#include <stdbool.h>
#include <stddef.h> // size_t
#include <stdarg.h> // va_list

#if !defined(SOKOL_GFX_INCLUDED)
#error "Please include sokol_gfx.h before sokol_debugtext.h"
#endif

#if defined(SOKOL_API_DECL) && !defined(SOKOL_DEBUGTEXT_API_DECL)
#define SOKOL_DEBUGTEXT_API_DECL SOKOL_API_DECL
#endif
#ifndef SOKOL_DEBUGTEXT_API_DECL
#if defined(_WIN32) && defined(SOKOL_DLL) && defined(SOKOL_DEBUGTEXT_IMPL)
#define SOKOL_DEBUGTEXT_API_DECL __declspec(dllexport)
#elif defined(_WIN32) && defined(SOKOL_DLL)
#define SOKOL_DEBUGTEXT_API_DECL __declspec(dllimport)
#else
#define SOKOL_DEBUGTEXT_API_DECL extern
#endif
#endif

#if defined(__GNUC__)
#define SOKOL_DEBUGTEXT_PRINTF_ATTR __attribute__((format(printf, 1, 2)))
#else
#define SOKOL_DEBUGTEXT_PRINTF_ATTR
#endif

#ifdef __cplusplus
extern "C" {
#endif

/*
    sdtx_log_item_t

    Log items are defined via X-Macros, and expanded to an
    enum 'sdtx_log_item' - and in debug mode only - corresponding strings.

    Used as parameter in the logging callback.
*/
#define _SDTX_LOG_ITEMS \
    _SDTX_LOGITEM_XMACRO(OK, "Ok") \
    _SDTX_LOGITEM_XMACRO(MALLOC_FAILED, "memory allocation failed") \
    _SDTX_LOGITEM_XMACRO(ADD_COMMIT_LISTENER_FAILED, "sg_add_commit_listener() failed") \
    _SDTX_LOGITEM_XMACRO(COMMAND_BUFFER_FULL, "command buffer full (adjust via sdtx_context_desc_t.max_commands)") \
    _SDTX_LOGITEM_XMACRO(CONTEXT_POOL_EXHAUSTED, "context pool exhausted (adjust via sdtx_desc_t.context_pool_size)") \
    _SDTX_LOGITEM_XMACRO(CANNOT_DESTROY_DEFAULT_CONTEXT, "cannot destroy default context") \

#define _SDTX_LOGITEM_XMACRO(item,msg) SDTX_LOGITEM_##item,
typedef enum sdtx_log_item_t {
    _SDTX_LOG_ITEMS
} sdtx_log_item_t;
#undef _SDTX_LOGITEM_XMACRO

/*
    sdtx_logger_t

    Used in sdtx_desc_t to provide a custom logging and error reporting
    callback to sokol-debugtext.
*/
typedef struct sdtx_logger_t {
    void (*func)(
        const char* tag,                // always "sdtx"
        uint32_t log_level,             // 0=panic, 1=error, 2=warning, 3=info
        uint32_t log_item_id,           // SDTX_LOGITEM_*
        const char* message_or_null,    // a message string, may be nullptr in release mode
        uint32_t line_nr,               // line number in sokol_debugtext.h
        const char* filename_or_null,   // source filename, may be nullptr in release mode
        void* user_data);
    void* user_data;
} sdtx_logger_t;

/* a rendering context handle */
typedef struct sdtx_context { uint32_t id; } sdtx_context;

/* the default context handle */
static const sdtx_context SDTX_DEFAULT_CONTEXT = { 0x00010001 };

/*
    sdtx_range is a pointer-size-pair struct used to pass memory
    blobs into sokol-debugtext. When initialized from a value type
    (array or struct), use the SDTX_RANGE() macro to build
    an sdtx_range struct.
*/
typedef struct sdtx_range {
    const void* ptr;
    size_t size;
} sdtx_range;

// disabling this for every includer isn't great, but the warning is also quite pointless
#if defined(_MSC_VER)
#pragma warning(disable:4221)   /* /W4 only: nonstandard extension used: 'x': cannot be initialized using address of automatic variable 'y' */
#pragma warning(disable:4204)   /* VS2015: nonstandard extension used: non-constant aggregate initializer */
#endif
#if defined(__cplusplus)
#define SDTX_RANGE(x) sdtx_range{ &x, sizeof(x) }
#else
#define SDTX_RANGE(x) (sdtx_range){ &x, sizeof(x) }
#endif

/*
    sdtx_font_desc_t

    Describes the pixel data of a font. A font consists of up to
    256 8x8 character tiles, where each character tile is described
    by 8 consecutive bytes, each byte describing 8 pixels.

    For instance the character 'A' could look like this (this is also
    how most home computers used to describe their fonts in ROM):

        bits
        7 6 5 4 3 2 1 0
        . . . X X . . .     byte 0: 0x18
        . . X X X X . .     byte 1: 0x3C
        . X X . . X X .     byte 2: 0x66
        . X X . . X X .     byte 3: 0x66
        . X X X X X X .     byte 4: 0x7E
        . X X . . X X .     byte 5: 0x66
        . X X . . X X .     byte 6: 0x66
        . . . . . . . .     byte 7: 0x00
 */
#define SDTX_MAX_FONTS (8)

typedef struct sdtx_font_desc_t {
    sdtx_range data;        // pointer to and size of font pixel data
    uint8_t first_char;     // first character index in font pixel data
    uint8_t last_char;      // last character index in font pixel data, inclusive (default: 255)
} sdtx_font_desc_t;

/*
    sdtx_context_desc_t

    Describes the initialization parameters of a rendering context. Creating
    additional rendering contexts is useful if you want to render in
    different sokol-gfx rendering passes, or when rendering several layers
    of text.
*/
typedef struct sdtx_context_desc_t {
    int max_commands;                       // max number of draw commands, each layer transition counts as a command, default: 4096
    int char_buf_size;                      // max number of characters rendered in one frame, default: 4096
    float canvas_width;                     // the initial virtual canvas width, default: 640
    float canvas_height;                    // the initial virtual canvas height, default: 400
    int tab_width;                          // tab width in number of characters, default: 4
    sg_pixel_format color_format;           // color pixel format of target render pass
    sg_pixel_format depth_format;           // depth pixel format of target render pass
    int sample_count;                       // MSAA sample count of target render pass
} sdtx_context_desc_t;

/*
    sdtx_allocator_t

    Used in sdtx_desc_t to provide custom memory-alloc and -free functions
    to sokol_debugtext.h. If memory management should be overridden, both the
    alloc_fn and free_fn function must be provided (e.g. it's not valid to
    override one function but not the other).
*/
typedef struct sdtx_allocator_t {
    void* (*alloc_fn)(size_t size, void* user_data);
    void (*free_fn)(void* ptr, void* user_data);
    void* user_data;
} sdtx_allocator_t;

/*
    sdtx_desc_t

    Describes the sokol-debugtext API initialization parameters. Passed
    to the sdtx_setup() function.

    NOTE: to populate the fonts item array with builtin fonts, use any
    of the following functions:

        sdtx_font_kc853()
        sdtx_font_kc854()
        sdtx_font_z1013()
        sdtx_font_cpc()
        sdtx_font_c64()
        sdtx_font_oric()
*/
typedef struct sdtx_desc_t {
    int context_pool_size;                  // max number of rendering contexts that can be created, default: 8
    int printf_buf_size;                    // size of internal buffer for snprintf(), default: 4096
    sdtx_font_desc_t fonts[SDTX_MAX_FONTS]; // up to 8 fonts descriptions
    sdtx_context_desc_t context;            // the default context creation parameters
    sdtx_allocator_t allocator;             // optional memory allocation overrides (default: malloc/free)
    sdtx_logger_t logger;                   // optional log override function (default: NO LOGGING)
} sdtx_desc_t;

/* initialization/shutdown */
SOKOL_DEBUGTEXT_API_DECL void sdtx_setup(const sdtx_desc_t* desc);
SOKOL_DEBUGTEXT_API_DECL void sdtx_shutdown(void);

/* builtin font data (use to populate sdtx_desc.font[]) */
SOKOL_DEBUGTEXT_API_DECL sdtx_font_desc_t sdtx_font_kc853(void);
SOKOL_DEBUGTEXT_API_DECL sdtx_font_desc_t sdtx_font_kc854(void);
SOKOL_DEBUGTEXT_API_DECL sdtx_font_desc_t sdtx_font_z1013(void);
SOKOL_DEBUGTEXT_API_DECL sdtx_font_desc_t sdtx_font_cpc(void);
SOKOL_DEBUGTEXT_API_DECL sdtx_font_desc_t sdtx_font_c64(void);
SOKOL_DEBUGTEXT_API_DECL sdtx_font_desc_t sdtx_font_oric(void);

/* context functions */
SOKOL_DEBUGTEXT_API_DECL sdtx_context sdtx_make_context(const sdtx_context_desc_t* desc);
SOKOL_DEBUGTEXT_API_DECL void sdtx_destroy_context(sdtx_context ctx);
SOKOL_DEBUGTEXT_API_DECL void sdtx_set_context(sdtx_context ctx);
SOKOL_DEBUGTEXT_API_DECL sdtx_context sdtx_get_context(void);
SOKOL_DEBUGTEXT_API_DECL sdtx_context sdtx_default_context(void);

/* drawing functions (call inside sokol-gfx render pass) */
SOKOL_DEBUGTEXT_API_DECL void sdtx_draw(void);
SOKOL_DEBUGTEXT_API_DECL void sdtx_context_draw(sdtx_context ctx);
SOKOL_DEBUGTEXT_API_DECL void sdtx_draw_layer(int layer_id);
SOKOL_DEBUGTEXT_API_DECL void sdtx_context_draw_layer(sdtx_context ctx, int layer_id);

/* switch render layer */
SOKOL_DEBUGTEXT_API_DECL void sdtx_layer(int layer_id);

/* switch to a different font */
SOKOL_DEBUGTEXT_API_DECL void sdtx_font(int font_index);

/* set a new virtual canvas size in screen pixels */
SOKOL_DEBUGTEXT_API_DECL void sdtx_canvas(float w, float h);

/* set a new origin in character grid coordinates */
SOKOL_DEBUGTEXT_API_DECL void sdtx_origin(float x, float y);

/* cursor movement functions (relative to origin in character grid coordinates) */
SOKOL_DEBUGTEXT_API_DECL void sdtx_home(void);
SOKOL_DEBUGTEXT_API_DECL void sdtx_pos(float x, float y);
SOKOL_DEBUGTEXT_API_DECL void sdtx_pos_x(float x);
SOKOL_DEBUGTEXT_API_DECL void sdtx_pos_y(float y);
SOKOL_DEBUGTEXT_API_DECL void sdtx_move(float dx, float dy);
SOKOL_DEBUGTEXT_API_DECL void sdtx_move_x(float dx);
SOKOL_DEBUGTEXT_API_DECL void sdtx_move_y(float dy);
SOKOL_DEBUGTEXT_API_DECL void sdtx_crlf(void);

/* set the current text color */
SOKOL_DEBUGTEXT_API_DECL void sdtx_color3b(uint8_t r, uint8_t g, uint8_t b);              // RGB 0..255, A=255
SOKOL_DEBUGTEXT_API_DECL void sdtx_color3f(float r, float g, float b);                    // RGB 0.0f..1.0f, A=1.0f
SOKOL_DEBUGTEXT_API_DECL void sdtx_color4b(uint8_t r, uint8_t g, uint8_t b, uint8_t a);   // RGBA 0..255
SOKOL_DEBUGTEXT_API_DECL void sdtx_color4f(float r, float g, float b, float a);           // RGBA 0.0f..1.0f
SOKOL_DEBUGTEXT_API_DECL void sdtx_color1i(uint32_t rgba);                                // ABGR 0xAABBGGRR

/* text rendering */
SOKOL_DEBUGTEXT_API_DECL void sdtx_putc(char c);
SOKOL_DEBUGTEXT_API_DECL void sdtx_puts(const char* str);             // does NOT append newline!
SOKOL_DEBUGTEXT_API_DECL void sdtx_putr(const char* str, int len);    // 'put range', also stops at zero-char
SOKOL_DEBUGTEXT_API_DECL int sdtx_printf(const char* fmt, ...) SOKOL_DEBUGTEXT_PRINTF_ATTR;
SOKOL_DEBUGTEXT_API_DECL int sdtx_vprintf(const char* fmt, va_list args);

/* language bindings helper: get the internal printf format buffer */
SOKOL_DEBUGTEXT_API_DECL sdtx_range sdtx_get_cleared_fmt_buffer(void);

#ifdef __cplusplus
} /* extern "C" */
/* C++ const-ref wrappers */
inline void sdtx_setup(const sdtx_desc_t& desc) { return sdtx_setup(&desc); }
inline sdtx_context sdtx_make_context(const sdtx_context_desc_t& desc) { return sdtx_make_context(&desc); }
#endif
#endif /* SOKOL_DEBUGTEXT_INCLUDED */

// ██ ███    ███ ██████  ██      ███████ ███    ███ ███████ ███    ██ ████████  █████  ████████ ██  ██████  ███    ██
// ██ ████  ████ ██   ██ ██      ██      ████  ████ ██      ████   ██    ██    ██   ██    ██    ██ ██    ██ ████   ██
// ██ ██ ████ ██ ██████  ██      █████   ██ ████ ██ █████   ██ ██  ██    ██    ███████    ██    ██ ██    ██ ██ ██  ██
// ██ ██  ██  ██ ██      ██      ██      ██  ██  ██ ██      ██  ██ ██    ██    ██   ██    ██    ██ ██    ██ ██  ██ ██
// ██ ██      ██ ██      ███████ ███████ ██      ██ ███████ ██   ████    ██    ██   ██    ██    ██  ██████  ██   ████
//
// >>implementation
#ifdef SOKOL_DEBUGTEXT_IMPL
#define SOKOL_DEBUGTEXT_IMPL_INCLUDED (1)

#if defined(SOKOL_MALLOC) || defined(SOKOL_CALLOC) || defined(SOKOL_FREE)
#error "SOKOL_MALLOC/CALLOC/FREE macros are no longer supported, please use sdtx_desc_t.allocator to override memory allocation functions"
#endif

#include <string.h> // memset
#include <math.h>   // fmodf
#include <stdarg.h> // for vsnprintf
#include <stdlib.h> // malloc/free

#ifndef SOKOL_API_IMPL
    #define SOKOL_API_IMPL
#endif
#ifndef SOKOL_DEBUG
    #ifndef NDEBUG
        #define SOKOL_DEBUG
    #endif
#endif
#ifndef SOKOL_ASSERT
    #include <assert.h>
    #define SOKOL_ASSERT(c) assert(c)
#endif

#ifndef SOKOL_UNREACHABLE
    #define SOKOL_UNREACHABLE SOKOL_ASSERT(false)
#endif
#ifndef _SOKOL_UNUSED
    #define _SOKOL_UNUSED(x) (void)(x)
#endif

#ifndef SOKOL_VSNPRINTF
#include <stdio.h>
#define SOKOL_VSNPRINTF vsnprintf
#endif

#define _sdtx_def(val, def) (((val) == 0) ? (def) : (val))
#define _SDTX_INIT_COOKIE (0xACBAABCA)

#define _SDTX_DEFAULT_MAX_COMMANDS (4096)
#define _SDTX_DEFAULT_CONTEXT_POOL_SIZE (8)
#define _SDTX_DEFAULT_CHAR_BUF_SIZE (4096)
#define _SDTX_DEFAULT_PRINTF_BUF_SIZE (4096)
#define _SDTX_DEFAULT_CANVAS_WIDTH (640)
#define _SDTX_DEFAULT_CANVAS_HEIGHT (480)
#define _SDTX_DEFAULT_TAB_WIDTH (4)
#define _SDTX_DEFAULT_COLOR (0xFF00FFFF)
#define _SDTX_INVALID_SLOT_INDEX (0)
#define _SDTX_SLOT_SHIFT (16)
#define _SDTX_MAX_POOL_SIZE (1<<_SDTX_SLOT_SHIFT)
#define _SDTX_SLOT_MASK (_SDTX_MAX_POOL_SIZE-1)

/* embedded font data */
static const uint8_t _sdtx_font_kc853[2048] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x81, 0xFF, // 00
    0x00, 0x00, 0x22, 0x72, 0x22, 0x3E, 0x00, 0x00, // 01
    0x00, 0x00, 0x12, 0x32, 0x7E, 0x32, 0x12, 0x00, // 02
    0x7E, 0x81, 0xB9, 0xA5, 0xB9, 0xA5, 0xB9, 0x81, // 03
    0x55, 0xFF, 0x55, 0xFF, 0x55, 0xFF, 0x55, 0xFF, // 04
    0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, // 05
    0xFF, 0x00, 0xFF, 0x00, 0xFF, 0x00, 0xFF, 0x00, // 06
    0x00, 0x00, 0x3C, 0x42, 0x42, 0x7E, 0x00, 0x00, // 07
    0x00, 0x10, 0x30, 0x7E, 0x30, 0x10, 0x00, 0x00, // 08
    0x00, 0x08, 0x0C, 0x7E, 0x0C, 0x08, 0x00, 0x00, // 09
    0x00, 0x10, 0x10, 0x10, 0x7C, 0x38, 0x10, 0x00, // 0A
    0x08, 0x1C, 0x3E, 0x08, 0x08, 0x08, 0x08, 0x00, // 0B
    0x38, 0x30, 0x28, 0x08, 0x08, 0x08, 0x3E, 0x00, // 0C
    0x00, 0x00, 0x12, 0x32, 0x7E, 0x30, 0x10, 0x00, // 0D
    0xAA, 0x55, 0xAA, 0x55, 0xAA, 0x55, 0xAA, 0x55, // 0E
    0x3E, 0x7C, 0x7C, 0x3E, 0x3E, 0x7C, 0xF8, 0xF8, // 0F
    0x38, 0x30, 0x28, 0x04, 0x04, 0x04, 0x04, 0x00, // 10
    0x7F, 0x08, 0x1C, 0x2A, 0x08, 0x08, 0x08, 0x00, // 11
    0x00, 0x08, 0x08, 0x08, 0x2A, 0x1C, 0x08, 0x7F, // 12
    0x7E, 0x81, 0x9D, 0xA1, 0xB9, 0x85, 0x85, 0xB9, // 13
    0x00, 0x3C, 0x42, 0x5A, 0x5A, 0x42, 0x3C, 0x00, // 14
    0x88, 0x44, 0x22, 0x11, 0x88, 0x44, 0x22, 0x11, // 15
    0x00, 0x7F, 0x22, 0x72, 0x27, 0x22, 0x7F, 0x00, // 16
    0x11, 0x22, 0x44, 0x88, 0x11, 0x22, 0x44, 0x88, // 17
    0x00, 0x01, 0x09, 0x0D, 0x7F, 0x0D, 0x09, 0x01, // 18
    0x00, 0x90, 0xB0, 0xFE, 0xB0, 0x90, 0x00, 0x00, // 19
    0x00, 0x08, 0x7C, 0x06, 0x7C, 0x08, 0x00, 0x00, // 1A
    0xCC, 0xCC, 0x33, 0x33, 0xCC, 0xCC, 0x33, 0x33, // 1B
    0x7E, 0x81, 0xA1, 0xA1, 0xA1, 0xA1, 0xBD, 0x81, // 1C
    0x7E, 0x81, 0xB9, 0xA5, 0xB9, 0xA5, 0xA5, 0x81, // 1D
    0x7E, 0x81, 0x99, 0xA1, 0xA1, 0xA1, 0x99, 0x81, // 1E
    0x00, 0x10, 0x3E, 0x60, 0x3E, 0x10, 0x00, 0x00, // 1F
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // 20
    0x00, 0x18, 0x18, 0x18, 0x18, 0x00, 0x18, 0x00, // 21
    0x00, 0x66, 0x66, 0xCC, 0x00, 0x00, 0x00, 0x00, // 22
    0x00, 0x36, 0x7F, 0x36, 0x36, 0x7F, 0x36, 0x00, // 23
    0x18, 0x3E, 0x6C, 0x3E, 0x1B, 0x1B, 0x7E, 0x18, // 24
    0x00, 0x63, 0x66, 0x0C, 0x18, 0x36, 0x66, 0x00, // 25
    0x18, 0x24, 0x28, 0x11, 0x2A, 0x44, 0x4A, 0x31, // 26
    0x00, 0x18, 0x18, 0x30, 0x00, 0x00, 0x00, 0x00, // 27
    0x00, 0x18, 0x30, 0x30, 0x30, 0x30, 0x18, 0x00, // 28
    0x00, 0x18, 0x0C, 0x0C, 0x0C, 0x0C, 0x18, 0x00, // 29
    0x00, 0x00, 0x24, 0x18, 0x7E, 0x18, 0x24, 0x00, // 2A
    0x00, 0x00, 0x18, 0x18, 0x7E, 0x18, 0x18, 0x00, // 2B
    0x00, 0x00, 0x00, 0x00, 0x18, 0x18, 0x30, 0x00, // 2C
    0x00, 0x00, 0x00, 0x00, 0x3E, 0x00, 0x00, 0x00, // 2D
    0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0x18, 0x00, // 2E
    0x06, 0x0C, 0x18, 0x30, 0x60, 0xC0, 0x00, 0x00, // 2F
    0x00, 0x3C, 0x6E, 0x6E, 0x76, 0x76, 0x3C, 0x00, // 30
    0x00, 0x1C, 0x3C, 0x0C, 0x0C, 0x0C, 0x3E, 0x00, // 31
    0x00, 0x3C, 0x66, 0x06, 0x3C, 0x60, 0x7E, 0x00, // 32
    0x00, 0x3C, 0x66, 0x0C, 0x06, 0x66, 0x3C, 0x00, // 33
    0x00, 0x3C, 0x6C, 0xCC, 0xFE, 0x0C, 0x0C, 0x00, // 34
    0x00, 0x7E, 0x60, 0x7C, 0x06, 0x66, 0x3C, 0x00, // 35
    0x00, 0x3C, 0x60, 0x7C, 0x66, 0x66, 0x3C, 0x00, // 36
    0x00, 0x7E, 0x06, 0x0C, 0x18, 0x30, 0x60, 0x00, // 37
    0x00, 0x3C, 0x66, 0x3C, 0x66, 0x66, 0x3C, 0x00, // 38
    0x00, 0x3C, 0x66, 0x66, 0x3E, 0x06, 0x3C, 0x00, // 39
    0x00, 0x00, 0x18, 0x18, 0x00, 0x18, 0x18, 0x00, // 3A
    0x00, 0x00, 0x18, 0x00, 0x18, 0x18, 0x30, 0x00, // 3B
    0x00, 0x00, 0x18, 0x30, 0x60, 0x30, 0x18, 0x00, // 3C
    0x00, 0x00, 0x00, 0x3E, 0x00, 0x3E, 0x00, 0x00, // 3D
    0x00, 0x00, 0x30, 0x18, 0x0C, 0x18, 0x30, 0x00, // 3E
    0x00, 0x3C, 0x66, 0x06, 0x1C, 0x18, 0x00, 0x18, // 3F
    0x3C, 0x42, 0x81, 0x35, 0x49, 0x49, 0x49, 0x36, // 40
    0x00, 0x3C, 0x66, 0x66, 0x7E, 0x66, 0x66, 0x00, // 41
    0x00, 0x7C, 0x66, 0x7C, 0x66, 0x66, 0x7C, 0x00, // 42
    0x00, 0x3C, 0x66, 0x60, 0x60, 0x66, 0x3C, 0x00, // 43
    0x00, 0x7C, 0x66, 0x66, 0x66, 0x66, 0x7C, 0x00, // 44
    0x00, 0x7E, 0x60, 0x7C, 0x60, 0x60, 0x7E, 0x00, // 45
    0x00, 0x7E, 0x60, 0x7C, 0x60, 0x60, 0x60, 0x00, // 46
    0x00, 0x3C, 0x66, 0x60, 0x6E, 0x66, 0x3C, 0x00, // 47
    0x00, 0x66, 0x66, 0x7E, 0x66, 0x66, 0x66, 0x00, // 48
    0x00, 0x3C, 0x18, 0x18, 0x18, 0x18, 0x3C, 0x00, // 49
    0x00, 0x1E, 0x0C, 0x0C, 0x0C, 0x6C, 0x38, 0x00, // 4A
    0x00, 0x66, 0x6C, 0x78, 0x6C, 0x66, 0x63, 0x00, // 4B
    0x00, 0x60, 0x60, 0x60, 0x60, 0x60, 0x7E, 0x00, // 4C
    0x00, 0x63, 0x77, 0x6B, 0x63, 0x63, 0x63, 0x00, // 4D
    0x00, 0x63, 0x73, 0x6B, 0x67, 0x63, 0x63, 0x00, // 4E
    0x00, 0x3C, 0x66, 0x66, 0x66, 0x66, 0x3C, 0x00, // 4F
    0x00, 0x7C, 0x66, 0x7C, 0x60, 0x60, 0x60, 0x00, // 50
    0x00, 0x3C, 0x66, 0x66, 0x6E, 0x66, 0x3A, 0x01, // 51
    0x00, 0x7C, 0x66, 0x7C, 0x6C, 0x66, 0x63, 0x00, // 52
    0x00, 0x3C, 0x60, 0x3C, 0x06, 0x66, 0x3C, 0x00, // 53
    0x00, 0x7E, 0x18, 0x18, 0x18, 0x18, 0x18, 0x00, // 54
    0x00, 0x66, 0x66, 0x66, 0x66, 0x66, 0x3C, 0x00, // 55
    0x00, 0x66, 0x66, 0x66, 0x66, 0x3C, 0x18, 0x00, // 56
    0x00, 0x63, 0x63, 0x6B, 0x6B, 0x7F, 0x36, 0x00, // 57
    0x00, 0x66, 0x3C, 0x18, 0x18, 0x3C, 0x66, 0x00, // 58
    0x00, 0x66, 0x3C, 0x18, 0x18, 0x18, 0x18, 0x00, // 59
    0x00, 0x7E, 0x0C, 0x18, 0x30, 0x60, 0x7E, 0x00, // 5A
    0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, // 5B
    0x00, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x00, // 5C
    0x00, 0x7E, 0x06, 0x06, 0x00, 0x00, 0x00, 0x00, // 5D
    0x00, 0x00, 0x00, 0x08, 0x1C, 0x36, 0x00, 0x00, // 5E
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFF, // 5F
    0x7E, 0x81, 0x99, 0xA1, 0xA1, 0x99, 0x81, 0x7E, // 60
    0x00, 0x00, 0x3C, 0x66, 0x66, 0x66, 0x3B, 0x00, // 61
    0x00, 0x60, 0x60, 0x78, 0x6C, 0x6C, 0x78, 0x00, // 62
    0x00, 0x00, 0x3C, 0x66, 0x60, 0x66, 0x3C, 0x00, // 63
    0x00, 0x06, 0x06, 0x1E, 0x36, 0x36, 0x1E, 0x00, // 64
    0x00, 0x00, 0x38, 0x6C, 0x7C, 0x60, 0x38, 0x00, // 65
    0x00, 0x1E, 0x18, 0x7E, 0x18, 0x18, 0x18, 0x00, // 66
    0x00, 0x00, 0x3C, 0x66, 0x66, 0x3F, 0x06, 0x3C, // 67
    0x00, 0x60, 0x60, 0x6C, 0x76, 0x66, 0x66, 0x00, // 68
    0x00, 0x18, 0x00, 0x18, 0x18, 0x18, 0x18, 0x00, // 69
    0x00, 0x18, 0x00, 0x38, 0x18, 0x18, 0x18, 0x30, // 6A
    0x00, 0x60, 0x66, 0x6C, 0x78, 0x6C, 0x66, 0x00, // 6B
    0x00, 0x30, 0x30, 0x30, 0x30, 0x30, 0x18, 0x00, // 6C
    0x00, 0x00, 0x36, 0x7F, 0x6B, 0x63, 0x63, 0x00, // 6D
    0x00, 0x00, 0x7C, 0x66, 0x66, 0x66, 0x66, 0x00, // 6E
    0x00, 0x00, 0x3C, 0x66, 0x66, 0x66, 0x3C, 0x00, // 6F
    0x00, 0x00, 0x7C, 0x66, 0x66, 0x7C, 0x60, 0x60, // 70
    0x00, 0x00, 0x3C, 0x66, 0x66, 0x3E, 0x06, 0x06, // 71
    0x00, 0x00, 0x36, 0x38, 0x30, 0x30, 0x30, 0x00, // 72
    0x00, 0x00, 0x1C, 0x30, 0x1C, 0x06, 0x3C, 0x00, // 73
    0x00, 0x18, 0x18, 0x3C, 0x18, 0x18, 0x0C, 0x00, // 74
    0x00, 0x00, 0x66, 0x66, 0x66, 0x66, 0x3C, 0x00, // 75
    0x00, 0x00, 0x66, 0x66, 0x3C, 0x3C, 0x18, 0x00, // 76
    0x00, 0x00, 0x63, 0x63, 0x6B, 0x7F, 0x36, 0x00, // 77
    0x00, 0x00, 0x66, 0x3C, 0x18, 0x3C, 0x66, 0x00, // 78
    0x00, 0x00, 0x66, 0x3C, 0x18, 0x30, 0x60, 0x00, // 79
    0x00, 0x00, 0x7E, 0x0C, 0x18, 0x30, 0x7E, 0x00, // 7A
    0x66, 0x00, 0x3C, 0x66, 0x66, 0x66, 0x3B, 0x00, // 7B
    0x66, 0x00, 0x3C, 0x66, 0x66, 0x66, 0x3C, 0x00, // 7C
    0x66, 0x00, 0x66, 0x66, 0x66, 0x66, 0x3C, 0x00, // 7D
    0x00, 0x38, 0x6C, 0x78, 0x6C, 0x78, 0x60, 0x60, // 7E
    0xFF, 0x81, 0x81, 0x81, 0x81, 0x81, 0x81, 0xFF, // 7F
    0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x7E, 0x00, // 80
    0xFF, 0xFF, 0xDD, 0x8D, 0xDD, 0xC1, 0xFF, 0xFF, // 81
    0xFF, 0xFF, 0xED, 0xCD, 0x81, 0xCD, 0xED, 0xFF, // 82
    0x81, 0x7E, 0x46, 0x5A, 0x46, 0x5A, 0x46, 0x7E, // 83
    0xAA, 0x00, 0xAA, 0x00, 0xAA, 0x00, 0xAA, 0x00, // 84
    0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, // 85
    0x00, 0xFF, 0x00, 0xFF, 0x00, 0xFF, 0x00, 0xFF, // 86
    0xFF, 0xFF, 0xC3, 0xBD, 0xBD, 0x81, 0xFF, 0xFF, // 87
    0xFF, 0xEF, 0xCF, 0x81, 0xCF, 0xEF, 0xFF, 0xFF, // 88
    0xFF, 0xF7, 0xF3, 0x81, 0xF3, 0xF7, 0xFF, 0xFF, // 89
    0xFF, 0xEF, 0xEF, 0xEF, 0x83, 0xC7, 0xEF, 0xFF, // 8A
    0xF7, 0xE3, 0xC1, 0xF7, 0xF7, 0xF7, 0xF7, 0xFF, // 8B
    0xC7, 0xCF, 0xD7, 0xF7, 0xF7, 0xF7, 0xC1, 0xFF, // 8C
    0xFF, 0xFF, 0xED, 0xCD, 0x81, 0xCF, 0xEF, 0xFF, // 8D
    0x55, 0xAA, 0x55, 0xAA, 0x55, 0xAA, 0x55, 0xAA, // 8E
    0xC1, 0x83, 0x83, 0xC1, 0xC1, 0x83, 0x07, 0x07, // 8F
    0xC7, 0xCF, 0xD7, 0xFB, 0xFB, 0xFB, 0xFB, 0xFF, // 90
    0x80, 0xF7, 0xE3, 0xD5, 0xF7, 0xF7, 0xF7, 0xFF, // 91
    0xFF, 0xF7, 0xF7, 0xF7, 0xD5, 0xE3, 0xF7, 0x80, // 92
    0x81, 0x7E, 0x62, 0x5E, 0x46, 0x7A, 0x7A, 0x46, // 93
    0xFF, 0xC3, 0xBD, 0xA5, 0xA5, 0xBD, 0xC3, 0xFF, // 94
    0x77, 0xBB, 0xDD, 0xEE, 0x77, 0xBB, 0xDD, 0xEE, // 95
    0xFF, 0x80, 0xDD, 0x8D, 0xD8, 0xDD, 0x80, 0xFF, // 96
    0xEE, 0xDD, 0xBB, 0x77, 0xEE, 0xDD, 0xBB, 0x77, // 97
    0xFF, 0xFE, 0xF6, 0xF2, 0x80, 0xF2, 0xF6, 0xFE, // 98
    0xFF, 0x6F, 0x4F, 0x01, 0x4F, 0x6F, 0xFF, 0xFF, // 99
    0xFF, 0xF7, 0x83, 0xF9, 0x83, 0xF7, 0xFF, 0xFF, // 9A
    0x33, 0x33, 0xCC, 0xCC, 0x33, 0x33, 0xCC, 0xCC, // 9B
    0x81, 0x7E, 0x5E, 0x5E, 0x5E, 0x5E, 0x42, 0x7E, // 9C
    0x81, 0x7E, 0x46, 0x5A, 0x46, 0x5A, 0x5A, 0x7E, // 9D
    0x81, 0x7E, 0x66, 0x5E, 0x5E, 0x5E, 0x66, 0x7E, // 9E
    0xFF, 0xEF, 0xC1, 0x9F, 0xC1, 0xEF, 0xFF, 0xFF, // 9F
    0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, // A0
    0xFF, 0xE7, 0xE7, 0xE7, 0xE7, 0xFF, 0xE7, 0xFF, // A1
    0xFF, 0x99, 0x99, 0x33, 0xFF, 0xFF, 0xFF, 0xFF, // A2
    0xFF, 0xC9, 0x80, 0xC9, 0xC9, 0x80, 0xC9, 0xFF, // A3
    0xE7, 0xC1, 0x93, 0xC1, 0xE4, 0xE4, 0x81, 0xE7, // A4
    0xFF, 0x9C, 0x99, 0xF3, 0xE7, 0xC9, 0x99, 0xFF, // A5
    0xE7, 0xDB, 0xD7, 0xEE, 0xD5, 0xBB, 0xB5, 0xCE, // A6
    0xFF, 0xE7, 0xE7, 0xCF, 0xFF, 0xFF, 0xFF, 0xFF, // A7
    0xFF, 0xE7, 0xCF, 0xCF, 0xCF, 0xCF, 0xE7, 0xFF, // A8
    0xFF, 0xE7, 0xF3, 0xF3, 0xF3, 0xF3, 0xE7, 0xFF, // A9
    0xFF, 0xFF, 0xDB, 0xE7, 0x81, 0xE7, 0xDB, 0xFF, // AA
    0xFF, 0xFF, 0xE7, 0xE7, 0x81, 0xE7, 0xE7, 0xFF, // AB
    0xFF, 0xFF, 0xFF, 0xFF, 0xE7, 0xE7, 0xCF, 0xFF, // AC
    0xFF, 0xFF, 0xFF, 0xFF, 0xC1, 0xFF, 0xFF, 0xFF, // AD
    0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xE7, 0xE7, 0xFF, // AE
    0xF9, 0xF3, 0xE7, 0xCF, 0x9F, 0x3F, 0xFF, 0xFF, // AF
    0xFF, 0xC3, 0x91, 0x91, 0x89, 0x89, 0xC3, 0xFF, // B0
    0xFF, 0xE3, 0xC3, 0xF3, 0xF3, 0xF3, 0xC1, 0xFF, // B1
    0xFF, 0xC3, 0x99, 0xF9, 0xC3, 0x9F, 0x81, 0xFF, // B2
    0xFF, 0xC3, 0x99, 0xF3, 0xF9, 0x99, 0xC3, 0xFF, // B3
    0xFF, 0xC3, 0x93, 0x33, 0x01, 0xF3, 0xF3, 0xFF, // B4
    0xFF, 0x81, 0x9F, 0x83, 0xF9, 0x99, 0xC3, 0xFF, // B5
    0xFF, 0xC3, 0x9F, 0x83, 0x99, 0x99, 0xC3, 0xFF, // B6
    0xFF, 0x81, 0xF9, 0xF3, 0xE7, 0xCF, 0x9F, 0xFF, // B7
    0xFF, 0xC3, 0x99, 0xC3, 0x99, 0x99, 0xC3, 0xFF, // B8
    0xFF, 0xC3, 0x99, 0x99, 0xC1, 0xF9, 0xC3, 0xFF, // B9
    0xFF, 0xFF, 0xE7, 0xE7, 0xFF, 0xE7, 0xE7, 0xFF, // BA
    0xFF, 0xFF, 0xE7, 0xFF, 0xE7, 0xE7, 0xCF, 0xFF, // BB
    0xFF, 0xFF, 0xE7, 0xCF, 0x9F, 0xCF, 0xE7, 0xFF, // BC
    0xFF, 0xFF, 0xFF, 0xC1, 0xFF, 0xC1, 0xFF, 0xFF, // BD
    0xFF, 0xFF, 0xCF, 0xE7, 0xF3, 0xE7, 0xCF, 0xFF, // BE
    0xFF, 0xC3, 0x99, 0xF9, 0xE3, 0xE7, 0xFF, 0xE7, // BF
    0xC3, 0xBD, 0x7E, 0xCA, 0xB6, 0xB6, 0xB6, 0xC9, // C0
    0xFF, 0xC3, 0x99, 0x99, 0x81, 0x99, 0x99, 0xFF, // C1
    0xFF, 0x83, 0x99, 0x83, 0x99, 0x99, 0x83, 0xFF, // C2
    0xFF, 0xC3, 0x99, 0x9F, 0x9F, 0x99, 0xC3, 0xFF, // C3
    0xFF, 0x83, 0x99, 0x99, 0x99, 0x99, 0x83, 0xFF, // C4
    0xFF, 0x81, 0x9F, 0x83, 0x9F, 0x9F, 0x81, 0xFF, // C5
    0xFF, 0x81, 0x9F, 0x83, 0x9F, 0x9F, 0x9F, 0xFF, // C6
    0xFF, 0xC3, 0x99, 0x9F, 0x91, 0x99, 0xC3, 0xFF, // C7
    0xFF, 0x99, 0x99, 0x81, 0x99, 0x99, 0x99, 0xFF, // C8
    0xFF, 0xC3, 0xE7, 0xE7, 0xE7, 0xE7, 0xC3, 0xFF, // C9
    0xFF, 0xE1, 0xF3, 0xF3, 0xF3, 0x93, 0xC7, 0xFF, // CA
    0xFF, 0x99, 0x93, 0x87, 0x93, 0x99, 0x9C, 0xFF, // CB
    0xFF, 0x9F, 0x9F, 0x9F, 0x9F, 0x9F, 0x81, 0xFF, // CC
    0xFF, 0x9C, 0x88, 0x94, 0x9C, 0x9C, 0x9C, 0xFF, // CD
    0xFF, 0x9C, 0x8C, 0x94, 0x98, 0x9C, 0x9C, 0xFF, // CE
    0xFF, 0xC3, 0x99, 0x99, 0x99, 0x99, 0xC3, 0xFF, // CF
    0xFF, 0x83, 0x99, 0x83, 0x9F, 0x9F, 0x9F, 0xFF, // D0
    0xFF, 0xC3, 0x99, 0x99, 0x91, 0x99, 0xC5, 0xFE, // D1
    0xFF, 0x83, 0x99, 0x83, 0x93, 0x99, 0x9C, 0xFF, // D2
    0xFF, 0xC3, 0x9F, 0xC3, 0xF9, 0x99, 0xC3, 0xFF, // D3
    0xFF, 0x81, 0xE7, 0xE7, 0xE7, 0xE7, 0xE7, 0xFF, // D4
    0xFF, 0x99, 0x99, 0x99, 0x99, 0x99, 0xC3, 0xFF, // D5
    0xFF, 0x99, 0x99, 0x99, 0x99, 0xC3, 0xE7, 0xFF, // D6
    0xFF, 0x9C, 0x9C, 0x94, 0x94, 0x80, 0xC9, 0xFF, // D7
    0xFF, 0x99, 0xC3, 0xE7, 0xE7, 0xC3, 0x99, 0xFF, // D8
    0xFF, 0x99, 0xC3, 0xE7, 0xE7, 0xE7, 0xE7, 0xFF, // D9
    0xFF, 0x81, 0xF3, 0xE7, 0xCF, 0x9F, 0x81, 0xFF, // DA
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // DB
    0xFF, 0xE7, 0xE7, 0xE7, 0xE7, 0xE7, 0xE7, 0xFF, // DC
    0xFF, 0x81, 0xF9, 0xF9, 0xFF, 0xFF, 0xFF, 0xFF, // DD
    0xFF, 0xFF, 0xFF, 0xF7, 0xE3, 0xC9, 0xFF, 0xFF, // DE
    0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x00, // DF
    0x81, 0x7E, 0x66, 0x5E, 0x5E, 0x66, 0x7E, 0x81, // E0
    0xFF, 0xFF, 0xC3, 0x99, 0x99, 0x99, 0xC4, 0xFF, // E1
    0xFF, 0x9F, 0x9F, 0x87, 0x93, 0x93, 0x87, 0xFF, // E2
    0xFF, 0xFF, 0xC3, 0x99, 0x9F, 0x99, 0xC3, 0xFF, // E3
    0xFF, 0xF9, 0xF9, 0xE1, 0xC9, 0xC9, 0xE1, 0xFF, // E4
    0xFF, 0xFF, 0xC7, 0x93, 0x83, 0x9F, 0xC7, 0xFF, // E5
    0xFF, 0xE1, 0xE7, 0x81, 0xE7, 0xE7, 0xE7, 0xFF, // E6
    0xFF, 0xFF, 0xC3, 0x99, 0x99, 0xC0, 0xF9, 0xC3, // E7
    0xFF, 0x9F, 0x9F, 0x93, 0x89, 0x99, 0x99, 0xFF, // E8
    0xFF, 0xE7, 0xFF, 0xE7, 0xE7, 0xE7, 0xE7, 0xFF, // E9
    0xFF, 0xE7, 0xFF, 0xC7, 0xE7, 0xE7, 0xE7, 0xCF, // EA
    0xFF, 0x9F, 0x99, 0x93, 0x87, 0x93, 0x99, 0xFF, // EB
    0xFF, 0xCF, 0xCF, 0xCF, 0xCF, 0xCF, 0xE7, 0xFF, // EC
    0xFF, 0xFF, 0xC9, 0x80, 0x94, 0x9C, 0x9C, 0xFF, // ED
    0xFF, 0xFF, 0x83, 0x99, 0x99, 0x99, 0x99, 0xFF, // EE
    0xFF, 0xFF, 0xC3, 0x99, 0x99, 0x99, 0xC3, 0xFF, // EF
    0xFF, 0xFF, 0x83, 0x99, 0x99, 0x83, 0x9F, 0x9F, // F0
    0xFF, 0xFF, 0xC3, 0x99, 0x99, 0xC1, 0xF9, 0xF9, // F1
    0xFF, 0xFF, 0xC9, 0xC7, 0xCF, 0xCF, 0xCF, 0xFF, // F2
    0xFF, 0xFF, 0xE3, 0xCF, 0xE3, 0xF9, 0xC3, 0xFF, // F3
    0xFF, 0xE7, 0xE7, 0xC3, 0xE7, 0xE7, 0xF3, 0xFF, // F4
    0xFF, 0xFF, 0x99, 0x99, 0x99, 0x99, 0xC3, 0xFF, // F5
    0xFF, 0xFF, 0x99, 0x99, 0xC3, 0xC3, 0xE7, 0xFF, // F6
    0xFF, 0xFF, 0x9C, 0x9C, 0x94, 0x80, 0xC9, 0xFF, // F7
    0xFF, 0xFF, 0x99, 0xC3, 0xE7, 0xC3, 0x99, 0xFF, // F8
    0xFF, 0xFF, 0x99, 0xC3, 0xE7, 0xCF, 0x9F, 0xFF, // F9
    0xFF, 0xFF, 0x81, 0xF3, 0xE7, 0xCF, 0x81, 0xFF, // FA
    0x99, 0xFF, 0xC3, 0x99, 0x99, 0x99, 0xC4, 0xFF, // FB
    0x99, 0xFF, 0xC3, 0x99, 0x99, 0x99, 0xC3, 0xFF, // FC
    0x99, 0xFF, 0x99, 0x99, 0x99, 0x99, 0xC3, 0xFF, // FD
    0xFF, 0xC7, 0x93, 0x87, 0x93, 0x87, 0x9F, 0x9F, // FE
    0x00, 0x7E, 0x7E, 0x7E, 0x7E, 0x7E, 0x7E, 0x00, // FF
};
static const uint8_t _sdtx_font_kc854[2048] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x81, 0xFF, // 00
    0x00, 0x00, 0x22, 0x72, 0x22, 0x3E, 0x00, 0x00, // 01
    0x00, 0x00, 0x12, 0x32, 0x7E, 0x32, 0x12, 0x00, // 02
    0x7E, 0x81, 0xB9, 0xA5, 0xB9, 0xA5, 0xB9, 0x81, // 03
    0x55, 0xFF, 0x55, 0xFF, 0x55, 0xFF, 0x55, 0xFF, // 04
    0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, // 05
    0xFF, 0x00, 0xFF, 0x00, 0xFF, 0x00, 0xFF, 0x00, // 06
    0x00, 0x00, 0x3C, 0x42, 0x42, 0x7E, 0x00, 0x00, // 07
    0x00, 0x10, 0x30, 0x7E, 0x30, 0x10, 0x00, 0x00, // 08
    0x00, 0x08, 0x0C, 0x7E, 0x0C, 0x08, 0x00, 0x00, // 09
    0x00, 0x10, 0x10, 0x10, 0x7C, 0x38, 0x10, 0x00, // 0A
    0x08, 0x1C, 0x3E, 0x08, 0x08, 0x08, 0x08, 0x00, // 0B
    0x38, 0x30, 0x28, 0x08, 0x08, 0x08, 0x3E, 0x00, // 0C
    0x00, 0x00, 0x12, 0x32, 0x7E, 0x30, 0x10, 0x00, // 0D
    0xAA, 0x55, 0xAA, 0x55, 0xAA, 0x55, 0xAA, 0x55, // 0E
    0x3E, 0x7C, 0x7C, 0x3E, 0x3E, 0x7C, 0xF8, 0xF8, // 0F
    0x38, 0x30, 0x28, 0x04, 0x04, 0x04, 0x04, 0x00, // 10
    0x7F, 0x08, 0x1C, 0x2A, 0x08, 0x08, 0x08, 0x00, // 11
    0x00, 0x08, 0x08, 0x08, 0x2A, 0x1C, 0x08, 0x7F, // 12
    0x7E, 0x81, 0x9D, 0xA1, 0xB9, 0x85, 0x85, 0xB9, // 13
    0x00, 0x3C, 0x42, 0x5A, 0x5A, 0x42, 0x3C, 0x00, // 14
    0x88, 0x44, 0x22, 0x11, 0x88, 0x44, 0x22, 0x11, // 15
    0x00, 0x7F, 0x22, 0x72, 0x27, 0x22, 0x7F, 0x00, // 16
    0x11, 0x22, 0x44, 0x88, 0x11, 0x22, 0x44, 0x88, // 17
    0x00, 0x01, 0x09, 0x0D, 0x7F, 0x0D, 0x09, 0x01, // 18
    0x00, 0x90, 0xB0, 0xFE, 0xB0, 0x90, 0x00, 0x00, // 19
    0x00, 0x08, 0x7C, 0x06, 0x7C, 0x08, 0x00, 0x00, // 1A
    0xCC, 0xCC, 0x33, 0x33, 0xCC, 0xCC, 0x33, 0x33, // 1B
    0x7E, 0x81, 0xA1, 0xA1, 0xA1, 0xA1, 0xBD, 0x81, // 1C
    0x7E, 0x81, 0xB9, 0xA5, 0xB9, 0xA5, 0xA5, 0x81, // 1D
    0x7E, 0x81, 0x99, 0xA1, 0xA1, 0xA1, 0x99, 0x81, // 1E
    0x00, 0x10, 0x3E, 0x60, 0x3E, 0x10, 0x00, 0x00, // 1F
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // 20
    0x30, 0x30, 0x30, 0x30, 0x30, 0x00, 0x30, 0x00, // 21
    0x77, 0x33, 0x66, 0x00, 0x00, 0x00, 0x00, 0x00, // 22
    0x36, 0x36, 0xFE, 0x6C, 0xFE, 0xD8, 0xD8, 0x00, // 23
    0x18, 0x3E, 0x6C, 0x3E, 0x1B, 0x1B, 0x7E, 0x18, // 24
    0x00, 0xC6, 0xCC, 0x18, 0x30, 0x66, 0xC6, 0x00, // 25
    0x38, 0x6C, 0x38, 0x76, 0xDC, 0xCC, 0x76, 0x00, // 26
    0x1C, 0x0C, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00, // 27
    0x18, 0x30, 0x60, 0x60, 0x60, 0x30, 0x18, 0x00, // 28
    0x60, 0x30, 0x18, 0x18, 0x18, 0x30, 0x60, 0x00, // 29
    0x00, 0x66, 0x3C, 0xFF, 0x3C, 0x66, 0x00, 0x00, // 2A
    0x00, 0x30, 0x30, 0xFC, 0x30, 0x30, 0x00, 0x00, // 2B
    0x00, 0x00, 0x00, 0x00, 0x00, 0x1C, 0x0C, 0x18, // 2C
    0x00, 0x00, 0x00, 0xFE, 0x00, 0x00, 0x00, 0x00, // 2D
    0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x30, 0x00, // 2E
    0x06, 0x0C, 0x18, 0x30, 0x60, 0xC0, 0x80, 0x00, // 2F
    0x7C, 0xC6, 0xCE, 0xDE, 0xF6, 0xE6, 0x7C, 0x00, // 30
    0x30, 0x70, 0x30, 0x30, 0x30, 0x30, 0xFC, 0x00, // 31
    0x78, 0xCC, 0x0C, 0x38, 0x60, 0xCC, 0xFC, 0x00, // 32
    0xFC, 0x18, 0x30, 0x78, 0x0C, 0xCC, 0x78, 0x00, // 33
    0x1C, 0x3C, 0x6C, 0xCC, 0xFE, 0x0C, 0x1E, 0x00, // 34
    0xFC, 0xC0, 0xF8, 0x0C, 0x0C, 0xCC, 0x78, 0x00, // 35
    0x38, 0x60, 0xC0, 0xF8, 0xCC, 0xCC, 0x78, 0x00, // 36
    0xFC, 0xCC, 0x0C, 0x18, 0x30, 0x30, 0x30, 0x00, // 37
    0x78, 0xCC, 0xCC, 0x78, 0xCC, 0xCC, 0x78, 0x00, // 38
    0x78, 0xCC, 0xCC, 0x7C, 0x0C, 0x18, 0x70, 0x00, // 39
    0x00, 0x00, 0x30, 0x30, 0x00, 0x30, 0x30, 0x00, // 3A
    0x00, 0x00, 0x30, 0x30, 0x00, 0x30, 0x30, 0x60, // 3B
    0x18, 0x30, 0x60, 0xC0, 0x60, 0x30, 0x18, 0x00, // 3C
    0x00, 0x00, 0xFC, 0x00, 0xFC, 0x00, 0x00, 0x00, // 3D
    0x60, 0x30, 0x18, 0x0C, 0x18, 0x30, 0x60, 0x00, // 3E
    0x78, 0xCC, 0x0C, 0x18, 0x30, 0x00, 0x30, 0x00, // 3F
    0x7C, 0xC6, 0xDE, 0xDE, 0xDE, 0xC0, 0x78, 0x00, // 40
    0x30, 0x78, 0xCC, 0xCC, 0xFC, 0xCC, 0xCC, 0x00, // 41
    0xFC, 0x66, 0x66, 0x7C, 0x66, 0x66, 0xFC, 0x00, // 42
    0x3C, 0x66, 0xC0, 0xC0, 0xC0, 0x66, 0x3C, 0x00, // 43
    0xF8, 0x6C, 0x66, 0x66, 0x66, 0x6C, 0xF8, 0x00, // 44
    0xFE, 0x62, 0x68, 0x78, 0x68, 0x62, 0xFE, 0x00, // 45
    0xFE, 0x62, 0x68, 0x78, 0x68, 0x60, 0xF0, 0x00, // 46
    0x3C, 0x66, 0xC0, 0xC0, 0xCE, 0x66, 0x3C, 0x00, // 47
    0xCC, 0xCC, 0xCC, 0xFC, 0xCC, 0xCC, 0xCC, 0x00, // 48
    0x78, 0x30, 0x30, 0x30, 0x30, 0x30, 0x78, 0x00, // 49
    0x1E, 0x0C, 0x0C, 0x0C, 0xCC, 0xCC, 0x78, 0x00, // 4A
    0xE6, 0x66, 0x6C, 0x70, 0x6C, 0x66, 0xE6, 0x00, // 4B
    0xF0, 0x60, 0x60, 0x60, 0x62, 0x66, 0xFE, 0x00, // 4C
    0xC6, 0xEE, 0xFE, 0xD6, 0xC6, 0xC6, 0xC6, 0x00, // 4D
    0xC6, 0xE6, 0xF6, 0xDE, 0xCE, 0xC6, 0xC6, 0x00, // 4E
    0x38, 0x6C, 0xC6, 0xC6, 0xC6, 0x6C, 0x38, 0x00, // 4F
    0xFC, 0x66, 0x66, 0x7C, 0x60, 0x60, 0xF0, 0x00, // 50
    0x78, 0xCC, 0xCC, 0xCC, 0xDC, 0x78, 0x1C, 0x00, // 51
    0xFC, 0x66, 0x66, 0x7C, 0x6C, 0x66, 0xE6, 0x00, // 52
    0x7C, 0xC6, 0xF0, 0x3C, 0x0E, 0xC6, 0x7C, 0x00, // 53
    0xFC, 0xB4, 0x30, 0x30, 0x30, 0x30, 0x78, 0x00, // 54
    0xCC, 0xCC, 0xCC, 0xCC, 0xCC, 0xCC, 0x78, 0x00, // 55
    0xCC, 0xCC, 0xCC, 0x78, 0x78, 0x30, 0x30, 0x00, // 56
    0xC6, 0xC6, 0xC6, 0xD6, 0xFE, 0xEE, 0xC6, 0x00, // 57
    0xC6, 0xC6, 0x6C, 0x38, 0x6C, 0xC6, 0xC6, 0x00, // 58
    0xCC, 0xCC, 0xCC, 0x78, 0x30, 0x30, 0x78, 0x00, // 59
    0xFE, 0xC6, 0x8C, 0x18, 0x32, 0x66, 0xFE, 0x00, // 5A
    0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, // 5B
    0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x00, // 5C
    0x00, 0xFE, 0x06, 0x06, 0x00, 0x00, 0x00, 0x00, // 5D
    0x10, 0x38, 0x6C, 0xC6, 0x00, 0x00, 0x00, 0x00, // 5E
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFF, // 5F
    0x3C, 0x42, 0x99, 0xA1, 0xA1, 0x99, 0x42, 0x3C, // 60
    0x00, 0x00, 0x78, 0x0C, 0x7C, 0xCC, 0x76, 0x00, // 61
    0xE0, 0x60, 0x7C, 0x66, 0x66, 0x66, 0xDC, 0x00, // 62
    0x00, 0x00, 0x78, 0xCC, 0xC0, 0xCC, 0x78, 0x00, // 63
    0x1C, 0x0C, 0x7C, 0xCC, 0xCC, 0xCC, 0x76, 0x00, // 64
    0x00, 0x00, 0x78, 0xCC, 0xFC, 0xC0, 0x78, 0x00, // 65
    0x38, 0x6C, 0x60, 0xF0, 0x60, 0x60, 0xF0, 0x00, // 66
    0x00, 0x00, 0x76, 0xCC, 0xCC, 0x7C, 0x0C, 0xF8, // 67
    0xE0, 0x60, 0x6C, 0x76, 0x66, 0x66, 0xE6, 0x00, // 68
    0x30, 0x00, 0x70, 0x30, 0x30, 0x30, 0xFC, 0x00, // 69
    0x0C, 0x00, 0x1C, 0x0C, 0x0C, 0xCC, 0xCC, 0x78, // 6A
    0xE0, 0x60, 0x66, 0x6C, 0x78, 0x6C, 0xE6, 0x00, // 6B
    0x70, 0x30, 0x30, 0x30, 0x30, 0x30, 0xFC, 0x00, // 6C
    0x00, 0x00, 0xCC, 0xFE, 0xFE, 0xD6, 0xC6, 0x00, // 6D
    0x00, 0x00, 0xF8, 0xCC, 0xCC, 0xCC, 0xCC, 0x00, // 6E
    0x00, 0x00, 0x78, 0xCC, 0xCC, 0xCC, 0x78, 0x00, // 6F
    0x00, 0x00, 0xDC, 0x66, 0x66, 0x7C, 0x60, 0xF0, // 70
    0x00, 0x00, 0x76, 0xCC, 0xCC, 0x7C, 0x0C, 0x1E, // 71
    0x00, 0x00, 0xDC, 0x76, 0x66, 0x60, 0xF0, 0x00, // 72
    0x00, 0x00, 0x7C, 0xC0, 0x78, 0x0C, 0xF8, 0x00, // 73
    0x10, 0x30, 0x7C, 0x30, 0x30, 0x34, 0x18, 0x00, // 74
    0x00, 0x00, 0xCC, 0xCC, 0xCC, 0xCC, 0x76, 0x00, // 75
    0x00, 0x00, 0xCC, 0xCC, 0xCC, 0x78, 0x30, 0x00, // 76
    0x00, 0x00, 0xC6, 0xD6, 0xFE, 0xFE, 0x6C, 0x00, // 77
    0x00, 0x00, 0xC6, 0x6C, 0x38, 0x6C, 0xC6, 0x00, // 78
    0x00, 0x00, 0xCC, 0xCC, 0xCC, 0x7C, 0x0C, 0xF8, // 79
    0x00, 0x00, 0xFC, 0x98, 0x30, 0x64, 0xFC, 0x00, // 7A
    0x6C, 0x00, 0x78, 0x0C, 0x7C, 0xCC, 0x76, 0x00, // 7B
    0xCC, 0x00, 0x78, 0xCC, 0xCC, 0xCC, 0x78, 0x00, // 7C
    0xCC, 0x00, 0xCC, 0xCC, 0xCC, 0xCC, 0x76, 0x00, // 7D
    0x3C, 0x66, 0x66, 0x6C, 0x66, 0x66, 0x6C, 0xF0, // 7E
    0xFF, 0x81, 0x81, 0x81, 0x81, 0x81, 0x81, 0xFF, // 7F
    0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x7E, 0x00, // 80
    0xFF, 0xFF, 0xDD, 0x8D, 0xDD, 0xC1, 0xFF, 0xFF, // 81
    0xFF, 0xFF, 0xED, 0xCD, 0x81, 0xCD, 0xED, 0xFF, // 82
    0x81, 0x7E, 0x46, 0x5A, 0x46, 0x5A, 0x46, 0x7E, // 83
    0xAA, 0x00, 0xAA, 0x00, 0xAA, 0x00, 0xAA, 0x00, // 84
    0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, // 85
    0x00, 0xFF, 0x00, 0xFF, 0x00, 0xFF, 0x00, 0xFF, // 86
    0xFF, 0xFF, 0xC3, 0xBD, 0xBD, 0x81, 0xFF, 0xFF, // 87
    0xFF, 0xEF, 0xCF, 0x81, 0xCF, 0xEF, 0xFF, 0xFF, // 88
    0xFF, 0xF7, 0xF3, 0x81, 0xF3, 0xF7, 0xFF, 0xFF, // 89
    0xFF, 0xEF, 0xEF, 0xEF, 0x83, 0xC7, 0xEF, 0xFF, // 8A
    0xF7, 0xE3, 0xC1, 0xF7, 0xF7, 0xF7, 0xF7, 0xFF, // 8B
    0xC7, 0xCF, 0xD7, 0xF7, 0xF7, 0xF7, 0xC1, 0xFF, // 8C
    0xFF, 0xFF, 0xED, 0xCD, 0x81, 0xCF, 0xEF, 0xFF, // 8D
    0x55, 0xAA, 0x55, 0xAA, 0x55, 0xAA, 0x55, 0xAA, // 8E
    0xC1, 0x83, 0x83, 0xC1, 0xC1, 0x83, 0x07, 0x07, // 8F
    0xC7, 0xCF, 0xD7, 0xFB, 0xFB, 0xFB, 0xFB, 0xFF, // 90
    0x80, 0xF7, 0xE3, 0xD5, 0xF7, 0xF7, 0xF7, 0xFF, // 91
    0xFF, 0xF7, 0xF7, 0xF7, 0xD5, 0xE3, 0xF7, 0x80, // 92
    0x81, 0x7E, 0x62, 0x5E, 0x46, 0x7A, 0x7A, 0x46, // 93
    0xFF, 0xC3, 0xBD, 0xA5, 0xA5, 0xBD, 0xC3, 0xFF, // 94
    0x77, 0xBB, 0xDD, 0xEE, 0x77, 0xBB, 0xDD, 0xEE, // 95
    0xFF, 0x80, 0xDD, 0x8D, 0xD8, 0xDD, 0x80, 0xFF, // 96
    0xEE, 0xDD, 0xBB, 0x77, 0xEE, 0xDD, 0xBB, 0x77, // 97
    0xFF, 0xFE, 0xF6, 0xF2, 0x80, 0xF2, 0xF6, 0xFE, // 98
    0xFF, 0x6F, 0x4F, 0x01, 0x4F, 0x6F, 0xFF, 0xFF, // 99
    0xFF, 0xF7, 0x83, 0xF9, 0x83, 0xF7, 0xFF, 0xFF, // 9A
    0x33, 0x33, 0xCC, 0xCC, 0x33, 0x33, 0xCC, 0xCC, // 9B
    0x81, 0x7E, 0x5E, 0x5E, 0x5E, 0x5E, 0x42, 0x7E, // 9C
    0x81, 0x7E, 0x46, 0x5A, 0x46, 0x5A, 0x5A, 0x7E, // 9D
    0x81, 0x7E, 0x66, 0x5E, 0x5E, 0x5E, 0x66, 0x7E, // 9E
    0xFF, 0xEF, 0xC1, 0x9F, 0xC1, 0xEF, 0xFF, 0xFF, // 9F
    0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, // A0
    0xCF, 0xCF, 0xCF, 0xCF, 0xCF, 0xFF, 0xCF, 0xFF, // A1
    0x88, 0xCC, 0x99, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, // A2
    0xC9, 0xC9, 0x01, 0x93, 0x01, 0x27, 0x27, 0xFF, // A3
    0xE7, 0xC1, 0x93, 0xC1, 0xE4, 0xE4, 0x81, 0xE7, // A4
    0xFF, 0x39, 0x33, 0xE7, 0xCF, 0x99, 0x39, 0xFF, // A5
    0xC7, 0x93, 0xC7, 0x89, 0x23, 0x33, 0x89, 0xFF, // A6
    0xE3, 0xF3, 0xE7, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, // A7
    0xE7, 0xCF, 0x9F, 0x9F, 0x9F, 0xCF, 0xE7, 0xFF, // A8
    0x9F, 0xCF, 0xE7, 0xE7, 0xE7, 0xCF, 0x9F, 0xFF, // A9
    0xFF, 0x99, 0xC3, 0x00, 0xC3, 0x99, 0xFF, 0xFF, // AA
    0xFF, 0xCF, 0xCF, 0x03, 0xCF, 0xCF, 0xFF, 0xFF, // AB
    0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xE3, 0xF3, 0xE7, // AC
    0xFF, 0xFF, 0xFF, 0x01, 0xFF, 0xFF, 0xFF, 0xFF, // AD
    0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xCF, 0xCF, 0xFF, // AE
    0xF9, 0xF3, 0xE7, 0xCF, 0x9F, 0x3F, 0x7F, 0xFF, // AF
    0x83, 0x39, 0x31, 0x21, 0x09, 0x19, 0x83, 0xFF, // B0
    0xCF, 0x8F, 0xCF, 0xCF, 0xCF, 0xCF, 0x03, 0xFF, // B1
    0x87, 0x33, 0xF3, 0xC7, 0x9F, 0x33, 0x03, 0xFF, // B2
    0x03, 0xE7, 0xCF, 0x87, 0xF3, 0x33, 0x87, 0xFF, // B3
    0xE3, 0xC3, 0x93, 0x33, 0x01, 0xF3, 0xE1, 0xFF, // B4
    0x03, 0x3F, 0x07, 0xF3, 0xF3, 0x33, 0x87, 0xFF, // B5
    0xC7, 0x9F, 0x3F, 0x07, 0x33, 0x33, 0x87, 0xFF, // B6
    0x03, 0x33, 0xF3, 0xE7, 0xCF, 0xCF, 0xCF, 0xFF, // B7
    0x87, 0x33, 0x33, 0x87, 0x33, 0x33, 0x87, 0xFF, // B8
    0x87, 0x33, 0x33, 0x83, 0xF3, 0xE7, 0x8F, 0xFF, // B9
    0xFF, 0xFF, 0xCF, 0xCF, 0xFF, 0xCF, 0xCF, 0xFF, // BA
    0xFF, 0xFF, 0xCF, 0xCF, 0xFF, 0xCF, 0xCF, 0x9F, // BB
    0xE7, 0xCF, 0x9F, 0x3F, 0x9F, 0xCF, 0xE7, 0xFF, // BC
    0xFF, 0xFF, 0x03, 0xFF, 0x03, 0xFF, 0xFF, 0xFF, // BD
    0x9F, 0xCF, 0xE7, 0xF3, 0xE7, 0xCF, 0x9F, 0xFF, // BE
    0x87, 0x33, 0xF3, 0xE7, 0xCF, 0xFF, 0xCF, 0xFF, // BF
    0x83, 0x39, 0x21, 0x21, 0x21, 0x3F, 0x87, 0xFF, // C0
    0xCF, 0x87, 0x33, 0x33, 0x03, 0x33, 0x33, 0xFF, // C1
    0x03, 0x99, 0x99, 0x83, 0x99, 0x99, 0x03, 0xFF, // C2
    0xC3, 0x99, 0x3F, 0x3F, 0x3F, 0x99, 0xC3, 0xFF, // C3
    0x07, 0x93, 0x99, 0x99, 0x99, 0x93, 0x07, 0xFF, // C4
    0x01, 0x9D, 0x97, 0x87, 0x97, 0x9D, 0x01, 0xFF, // C5
    0x01, 0x9D, 0x97, 0x87, 0x97, 0x9F, 0x0F, 0xFF, // C6
    0xC3, 0x99, 0x3F, 0x3F, 0x31, 0x99, 0xC3, 0xFF, // C7
    0x33, 0x33, 0x33, 0x03, 0x33, 0x33, 0x33, 0xFF, // C8
    0x87, 0xCF, 0xCF, 0xCF, 0xCF, 0xCF, 0x87, 0xFF, // C9
    0xE1, 0xF3, 0xF3, 0xF3, 0x33, 0x33, 0x87, 0xFF, // CA
    0x19, 0x99, 0x93, 0x8F, 0x93, 0x99, 0x19, 0xFF, // CB
    0x0F, 0x9F, 0x9F, 0x9F, 0x9D, 0x99, 0x01, 0xFF, // CC
    0x39, 0x11, 0x01, 0x29, 0x39, 0x39, 0x39, 0xFF, // CD
    0x39, 0x19, 0x09, 0x21, 0x31, 0x39, 0x39, 0xFF, // CE
    0xC7, 0x93, 0x39, 0x39, 0x39, 0x93, 0xC7, 0xFF, // CF
    0x03, 0x99, 0x99, 0x83, 0x9F, 0x9F, 0x0F, 0xFF, // D0
    0x87, 0x33, 0x33, 0x33, 0x23, 0x87, 0xE3, 0xFF, // D1
    0x03, 0x99, 0x99, 0x83, 0x93, 0x99, 0x19, 0xFF, // D2
    0x83, 0x39, 0x0F, 0xC3, 0xF1, 0x39, 0x83, 0xFF, // D3
    0x03, 0x4B, 0xCF, 0xCF, 0xCF, 0xCF, 0x87, 0xFF, // D4
    0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x87, 0xFF, // D5
    0x33, 0x33, 0x33, 0x87, 0x87, 0xCF, 0xCF, 0xFF, // D6
    0x39, 0x39, 0x39, 0x29, 0x01, 0x11, 0x39, 0xFF, // D7
    0x39, 0x39, 0x93, 0xC7, 0x93, 0x39, 0x39, 0xFF, // D8
    0x33, 0x33, 0x33, 0x87, 0xCF, 0xCF, 0x87, 0xFF, // D9
    0x01, 0x39, 0x73, 0xE7, 0xCD, 0x99, 0x01, 0xFF, // DA
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // DB
    0xE7, 0xE7, 0xE7, 0xE7, 0xE7, 0xE7, 0xE7, 0xFF, // DC
    0xFF, 0x01, 0xF9, 0xF9, 0xFF, 0xFF, 0xFF, 0xFF, // DD
    0xEF, 0xC7, 0x93, 0x39, 0xFF, 0xFF, 0xFF, 0xFF, // DE
    0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x00, // DF
    0xC3, 0xBD, 0x66, 0x5E, 0x5E, 0x66, 0xBD, 0xC3, // E0
    0xFF, 0xFF, 0x87, 0xF3, 0x83, 0x33, 0x89, 0xFF, // E1
    0x1F, 0x9F, 0x83, 0x99, 0x99, 0x99, 0x23, 0xFF, // E2
    0xFF, 0xFF, 0x87, 0x33, 0x3F, 0x33, 0x87, 0xFF, // E3
    0xE3, 0xF3, 0x83, 0x33, 0x33, 0x33, 0x89, 0xFF, // E4
    0xFF, 0xFF, 0x87, 0x33, 0x03, 0x3F, 0x87, 0xFF, // E5
    0xC7, 0x93, 0x9F, 0x0F, 0x9F, 0x9F, 0x0F, 0xFF, // E6
    0xFF, 0xFF, 0x89, 0x33, 0x33, 0x83, 0xF3, 0x07, // E7
    0x1F, 0x9F, 0x93, 0x89, 0x99, 0x99, 0x19, 0xFF, // E8
    0xCF, 0xFF, 0x8F, 0xCF, 0xCF, 0xCF, 0x03, 0xFF, // E9
    0xF3, 0xFF, 0xE3, 0xF3, 0xF3, 0x33, 0x33, 0x87, // EA
    0x1F, 0x9F, 0x99, 0x93, 0x87, 0x93, 0x19, 0xFF, // EB
    0x8F, 0xCF, 0xCF, 0xCF, 0xCF, 0xCF, 0x03, 0xFF, // EC
    0xFF, 0xFF, 0x33, 0x01, 0x01, 0x29, 0x39, 0xFF, // ED
    0xFF, 0xFF, 0x07, 0x33, 0x33, 0x33, 0x33, 0xFF, // EE
    0xFF, 0xFF, 0x87, 0x33, 0x33, 0x33, 0x87, 0xFF, // EF
    0xFF, 0xFF, 0x23, 0x99, 0x99, 0x83, 0x9F, 0x0F, // F0
    0xFF, 0xFF, 0x89, 0x33, 0x33, 0x83, 0xF3, 0xE1, // F1
    0xFF, 0xFF, 0x23, 0x89, 0x99, 0x9F, 0x0F, 0xFF, // F2
    0xFF, 0xFF, 0x83, 0x3F, 0x87, 0xF3, 0x07, 0xFF, // F3
    0xEF, 0xCF, 0x83, 0xCF, 0xCF, 0xCB, 0xE7, 0xFF, // F4
    0xFF, 0xFF, 0x33, 0x33, 0x33, 0x33, 0x89, 0xFF, // F5
    0xFF, 0xFF, 0x33, 0x33, 0x33, 0x87, 0xCF, 0xFF, // F6
    0xFF, 0xFF, 0x39, 0x29, 0x01, 0x01, 0x93, 0xFF, // F7
    0xFF, 0xFF, 0x39, 0x93, 0xC7, 0x93, 0x39, 0xFF, // F8
    0xFF, 0xFF, 0x33, 0x33, 0x33, 0x83, 0xF3, 0x07, // F9
    0xFF, 0xFF, 0x03, 0x67, 0xCF, 0x9B, 0x03, 0xFF, // FA
    0x93, 0xFF, 0x87, 0xF3, 0x83, 0x33, 0x89, 0xFF, // FB
    0x33, 0xFF, 0x87, 0x33, 0x33, 0x33, 0x87, 0xFF, // FC
    0x33, 0xFF, 0x33, 0x33, 0x33, 0x33, 0x89, 0xFF, // FD
    0xC3, 0x99, 0x99, 0x93, 0x99, 0x99, 0x93, 0x0F, // FE
    0x00, 0x7E, 0x7E, 0x7E, 0x7E, 0x7E, 0x7E, 0x00, // FF
};
static const uint8_t _sdtx_font_z1013[2048] = {
    0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, // 00
    0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, // 01
    0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, // 02
    0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, // 03
    0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, // 04
    0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, // 05
    0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, // 06
    0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, // 07
    0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, // 08
    0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, // 09
    0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, // 0A
    0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, // 0B
    0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, // 0C
    0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, // 0D
    0x00, 0x00, 0x18, 0x24, 0x24, 0x18, 0x24, 0x42, // 0E
    0xDB, 0xA5, 0x81, 0xFF, 0x24, 0x24, 0x24, 0x42, // 0F
    0x08, 0x34, 0x42, 0x81, 0x91, 0x69, 0x09, 0x31, // 10
    0x42, 0x7E, 0x81, 0xFF, 0x00, 0x00, 0x00, 0x00, // 11
    0x18, 0x24, 0x42, 0x99, 0xBD, 0x99, 0x42, 0x24, // 12
    0x7E, 0x42, 0x99, 0xE7, 0x00, 0x00, 0x00, 0x00, // 13
    0x18, 0xDB, 0xC3, 0x18, 0x99, 0xE7, 0x81, 0x42, // 14
    0x18, 0x24, 0x18, 0xC3, 0xBD, 0x81, 0x81, 0x42, // 15
    0x24, 0x7E, 0x81, 0xFF, 0x00, 0x00, 0x00, 0x00, // 16
    0x00, 0x00, 0x18, 0x3C, 0x3C, 0x18, 0x3C, 0x7E, // 17
    0xDB, 0xFF, 0xFF, 0xFF, 0x3C, 0x3C, 0x3C, 0x7E, // 18
    0x08, 0x3C, 0x7E, 0xFF, 0xFF, 0x6F, 0x0F, 0x3F, // 19
    0x7E, 0x7E, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00, // 1A
    0x18, 0x3C, 0x7E, 0xE7, 0xC3, 0xE7, 0x7E, 0x3C, // 1B
    0x7E, 0x7E, 0xFF, 0xE7, 0x00, 0x00, 0x00, 0x00, // 1C
    0x18, 0xDB, 0xC3, 0x18, 0x99, 0xFF, 0xFF, 0x7E, // 1D
    0x18, 0x3C, 0x18, 0xC3, 0xFF, 0xFF, 0xFF, 0x7E, // 1E
    0x3C, 0x3C, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00, // 1F
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // 20
    0x10, 0x10, 0x10, 0x10, 0x00, 0x00, 0x10, 0x00, // 21
    0x28, 0x28, 0x28, 0x00, 0x00, 0x00, 0x00, 0x00, // 22
    0x24, 0x7E, 0x24, 0x24, 0x24, 0x7E, 0x24, 0x00, // 23
    0x10, 0x3C, 0x50, 0x38, 0x14, 0x78, 0x10, 0x00, // 24
    0x60, 0x64, 0x08, 0x10, 0x20, 0x4C, 0x0C, 0x00, // 25
    0x10, 0x28, 0x28, 0x30, 0x54, 0x48, 0x34, 0x00, // 26
    0x10, 0x10, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00, // 27
    0x08, 0x10, 0x20, 0x20, 0x20, 0x10, 0x08, 0x00, // 28
    0x20, 0x10, 0x08, 0x08, 0x08, 0x10, 0x20, 0x00, // 29
    0x00, 0x10, 0x54, 0x38, 0x54, 0x10, 0x00, 0x00, // 2A
    0x00, 0x10, 0x10, 0x7C, 0x10, 0x10, 0x00, 0x00, // 2B
    0x00, 0x00, 0x00, 0x00, 0x10, 0x10, 0x20, 0x00, // 2C
    0x00, 0x00, 0x00, 0x7C, 0x00, 0x00, 0x00, 0x00, // 2D
    0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x30, 0x00, // 2E
    0x00, 0x04, 0x08, 0x10, 0x20, 0x40, 0x00, 0x00, // 2F
    0x38, 0x44, 0x44, 0x54, 0x44, 0x44, 0x38, 0x00, // 30
    0x10, 0x30, 0x10, 0x10, 0x10, 0x10, 0x38, 0x00, // 31
    0x38, 0x44, 0x04, 0x08, 0x10, 0x20, 0x7C, 0x00, // 32
    0x7C, 0x08, 0x10, 0x08, 0x04, 0x44, 0x38, 0x00, // 33
    0x08, 0x18, 0x28, 0x48, 0x7C, 0x08, 0x08, 0x00, // 34
    0x7C, 0x40, 0x78, 0x04, 0x04, 0x44, 0x38, 0x00, // 35
    0x18, 0x20, 0x40, 0x78, 0x44, 0x44, 0x38, 0x00, // 36
    0x7C, 0x04, 0x08, 0x10, 0x20, 0x20, 0x20, 0x00, // 37
    0x38, 0x44, 0x44, 0x38, 0x44, 0x44, 0x38, 0x00, // 38
    0x38, 0x44, 0x44, 0x3C, 0x04, 0x08, 0x30, 0x00, // 39
    0x00, 0x30, 0x30, 0x00, 0x30, 0x30, 0x00, 0x00, // 3A
    0x00, 0x00, 0x10, 0x00, 0x10, 0x10, 0x20, 0x00, // 3B
    0x08, 0x10, 0x20, 0x40, 0x20, 0x10, 0x08, 0x00, // 3C
    0x00, 0x00, 0x7C, 0x00, 0x7C, 0x00, 0x00, 0x00, // 3D
    0x20, 0x10, 0x08, 0x04, 0x08, 0x10, 0x20, 0x00, // 3E
    0x38, 0x44, 0x04, 0x08, 0x10, 0x00, 0x10, 0x00, // 3F
    0x38, 0x44, 0x5C, 0x54, 0x5C, 0x40, 0x3C, 0x00, // 40
    0x38, 0x44, 0x44, 0x7C, 0x44, 0x44, 0x44, 0x00, // 41
    0x78, 0x24, 0x24, 0x38, 0x24, 0x24, 0x78, 0x00, // 42
    0x38, 0x44, 0x40, 0x40, 0x40, 0x44, 0x38, 0x00, // 43
    0x78, 0x24, 0x24, 0x24, 0x24, 0x24, 0x78, 0x00, // 44
    0x7C, 0x40, 0x40, 0x78, 0x40, 0x40, 0x7C, 0x00, // 45
    0x7C, 0x40, 0x40, 0x78, 0x40, 0x40, 0x40, 0x00, // 46
    0x38, 0x44, 0x40, 0x40, 0x4C, 0x44, 0x3C, 0x00, // 47
    0x44, 0x44, 0x44, 0x7C, 0x44, 0x44, 0x44, 0x00, // 48
    0x38, 0x10, 0x10, 0x10, 0x10, 0x10, 0x38, 0x00, // 49
    0x1C, 0x08, 0x08, 0x08, 0x08, 0x48, 0x30, 0x00, // 4A
    0x44, 0x48, 0x50, 0x60, 0x50, 0x48, 0x44, 0x00, // 4B
    0x40, 0x40, 0x40, 0x40, 0x40, 0x40, 0x7C, 0x00, // 4C
    0x44, 0x6C, 0x54, 0x54, 0x44, 0x44, 0x44, 0x00, // 4D
    0x44, 0x44, 0x64, 0x54, 0x4C, 0x44, 0x44, 0x00, // 4E
    0x38, 0x44, 0x44, 0x44, 0x44, 0x44, 0x38, 0x00, // 4F
    0x78, 0x44, 0x44, 0x78, 0x40, 0x40, 0x40, 0x00, // 50
    0x38, 0x44, 0x44, 0x44, 0x54, 0x48, 0x34, 0x00, // 51
    0x78, 0x44, 0x44, 0x78, 0x50, 0x48, 0x44, 0x00, // 52
    0x3C, 0x40, 0x40, 0x38, 0x04, 0x04, 0x78, 0x00, // 53
    0x7C, 0x10, 0x10, 0x10, 0x10, 0x10, 0x10, 0x00, // 54
    0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x38, 0x00, // 55
    0x44, 0x44, 0x44, 0x44, 0x44, 0x28, 0x10, 0x00, // 56
    0x44, 0x44, 0x44, 0x54, 0x54, 0x6C, 0x44, 0x00, // 57
    0x44, 0x44, 0x28, 0x10, 0x28, 0x44, 0x44, 0x00, // 58
    0x44, 0x44, 0x44, 0x28, 0x10, 0x10, 0x10, 0x00, // 59
    0x7C, 0x04, 0x08, 0x10, 0x20, 0x40, 0x7C, 0x00, // 5A
    0x38, 0x20, 0x20, 0x20, 0x20, 0x20, 0x38, 0x00, // 5B
    0x00, 0x40, 0x20, 0x10, 0x08, 0x04, 0x00, 0x00, // 5C
    0x38, 0x08, 0x08, 0x08, 0x08, 0x08, 0x38, 0x00, // 5D
    0x10, 0x28, 0x44, 0x00, 0x00, 0x00, 0x00, 0x00, // 5E
    0x00, 0x00, 0x00, 0x00, 0x00, 0x7C, 0x00, 0x00, // 5F
    0x00, 0x20, 0x10, 0x08, 0x00, 0x00, 0x00, 0x00, // 60
    0x00, 0x00, 0x34, 0x4C, 0x44, 0x44, 0x3A, 0x00, // 61
    0x40, 0x40, 0x58, 0x64, 0x44, 0x44, 0x78, 0x00, // 62
    0x00, 0x00, 0x38, 0x44, 0x40, 0x44, 0x38, 0x00, // 63
    0x04, 0x04, 0x34, 0x4C, 0x44, 0x44, 0x3A, 0x00, // 64
    0x00, 0x00, 0x38, 0x44, 0x7C, 0x40, 0x38, 0x00, // 65
    0x08, 0x10, 0x38, 0x10, 0x10, 0x10, 0x10, 0x00, // 66
    0x00, 0x00, 0x34, 0x4C, 0x44, 0x3C, 0x04, 0x38, // 67
    0x40, 0x40, 0x58, 0x64, 0x44, 0x44, 0x44, 0x00, // 68
    0x10, 0x00, 0x10, 0x10, 0x10, 0x10, 0x08, 0x00, // 69
    0x10, 0x00, 0x10, 0x10, 0x10, 0x10, 0x10, 0x20, // 6A
    0x40, 0x40, 0x48, 0x50, 0x70, 0x48, 0x44, 0x00, // 6B
    0x10, 0x10, 0x10, 0x10, 0x10, 0x10, 0x08, 0x00, // 6C
    0x00, 0x00, 0x68, 0x54, 0x54, 0x54, 0x54, 0x00, // 6D
    0x00, 0x00, 0x58, 0x64, 0x44, 0x44, 0x44, 0x00, // 6E
    0x00, 0x00, 0x38, 0x44, 0x44, 0x44, 0x38, 0x00, // 6F
    0x00, 0x00, 0x58, 0x64, 0x44, 0x78, 0x40, 0x40, // 70
    0x00, 0x00, 0x34, 0x4C, 0x44, 0x3C, 0x04, 0x04, // 71
    0x00, 0x00, 0x58, 0x64, 0x40, 0x40, 0x40, 0x00, // 72
    0x00, 0x00, 0x38, 0x40, 0x38, 0x04, 0x78, 0x00, // 73
    0x10, 0x10, 0x38, 0x10, 0x10, 0x10, 0x08, 0x00, // 74
    0x00, 0x00, 0x44, 0x44, 0x44, 0x4C, 0x34, 0x00, // 75
    0x00, 0x00, 0x44, 0x44, 0x44, 0x28, 0x10, 0x00, // 76
    0x00, 0x00, 0x54, 0x54, 0x54, 0x54, 0x28, 0x00, // 77
    0x00, 0x00, 0x44, 0x28, 0x10, 0x28, 0x44, 0x00, // 78
    0x00, 0x00, 0x44, 0x44, 0x44, 0x3C, 0x04, 0x38, // 79
    0x00, 0x00, 0x7C, 0x08, 0x10, 0x20, 0x7C, 0x00, // 7A
    0x08, 0x10, 0x10, 0x20, 0x10, 0x10, 0x08, 0x00, // 7B
    0x10, 0x10, 0x10, 0x10, 0x10, 0x10, 0x10, 0x00, // 7C
    0x20, 0x10, 0x10, 0x08, 0x10, 0x10, 0x20, 0x00, // 7D
    0x00, 0x00, 0x00, 0x32, 0x4C, 0x00, 0x00, 0x00, // 7E
    0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, // 7F
    0xC0, 0x20, 0x10, 0x10, 0x10, 0x10, 0x20, 0xC0, // 80
    0x03, 0x04, 0x08, 0x08, 0x08, 0x08, 0x04, 0x03, // 81
    0x81, 0x81, 0x42, 0x3C, 0x00, 0x00, 0x00, 0x00, // 82
    0x00, 0x00, 0x00, 0x00, 0x3C, 0x42, 0x81, 0x81, // 83
    0x10, 0x10, 0x20, 0xC0, 0x00, 0x00, 0x00, 0x00, // 84
    0x08, 0x08, 0x04, 0x03, 0x00, 0x00, 0x00, 0x00, // 85
    0x00, 0x00, 0x00, 0x00, 0x03, 0x04, 0x08, 0x08, // 86
    0x00, 0x00, 0x00, 0x00, 0xC0, 0x20, 0x10, 0x10, // 87
    0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0xFF, // 88
    0xFF, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, // 89
    0x00, 0x10, 0x28, 0x44, 0x82, 0x44, 0x28, 0x10, // 8A
    0xFF, 0xEF, 0xC7, 0x83, 0x01, 0x83, 0xC7, 0xEF, // 8B
    0x3C, 0x42, 0x81, 0x81, 0x81, 0x81, 0x42, 0x3C, // 8C
    0xC3, 0x81, 0x00, 0x00, 0x00, 0x00, 0x81, 0xC3, // 8D
    0xFF, 0xFE, 0xFC, 0xF8, 0xF0, 0xE0, 0xC0, 0x80, // 8E
    0x80, 0xC0, 0xE0, 0xF0, 0xF8, 0xFC, 0xFE, 0xFF, // 8F
    0x01, 0x02, 0x04, 0x08, 0x10, 0x20, 0x40, 0x80, // 90
    0x80, 0x40, 0x20, 0x10, 0x08, 0x04, 0x02, 0x01, // 91
    0x00, 0x00, 0x00, 0x00, 0x03, 0x0C, 0x30, 0xC0, // 92
    0x03, 0x0C, 0x30, 0xC0, 0x00, 0x00, 0x00, 0x00, // 93
    0x03, 0x0C, 0x30, 0xC0, 0xC0, 0x30, 0x0C, 0x03, // 94
    0x00, 0x00, 0x00, 0x00, 0xC0, 0x30, 0x0C, 0x03, // 95
    0xC0, 0x30, 0x0C, 0x03, 0x00, 0x00, 0x00, 0x00, // 96
    0xC0, 0x30, 0x0C, 0x03, 0x03, 0x0C, 0x30, 0xC0, // 97
    0x10, 0x10, 0x20, 0x20, 0x40, 0x40, 0x80, 0x80, // 98
    0x01, 0x01, 0x02, 0x02, 0x04, 0x04, 0x08, 0x08, // 99
    0x81, 0x81, 0x42, 0x42, 0x24, 0x24, 0x18, 0x18, // 9A
    0x80, 0x80, 0x40, 0x40, 0x20, 0x20, 0x10, 0x10, // 9B
    0x08, 0x08, 0x04, 0x04, 0x02, 0x02, 0x01, 0x01, // 9C
    0x18, 0x18, 0x24, 0x24, 0x42, 0x42, 0x81, 0x81, // 9D
    0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // 9E
    0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, // 9F
    0x00, 0x00, 0x00, 0xFF, 0xFF, 0x00, 0x00, 0x00, // A0
    0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, // A1
    0x18, 0x18, 0x18, 0xFF, 0xFF, 0x00, 0x00, 0x00, // A2
    0x18, 0x18, 0x18, 0x1F, 0x1F, 0x18, 0x18, 0x18, // A3
    0x00, 0x00, 0x00, 0xFF, 0xFF, 0x18, 0x18, 0x18, // A4
    0x18, 0x18, 0x18, 0xF8, 0xF8, 0x18, 0x18, 0x18, // A5
    0x18, 0x18, 0x18, 0xFF, 0xFF, 0x18, 0x18, 0x18, // A6
    0x18, 0x18, 0x18, 0x1F, 0x1F, 0x00, 0x00, 0x00, // A7
    0x00, 0x00, 0x00, 0x1F, 0x1F, 0x18, 0x18, 0x18, // A8
    0x00, 0x00, 0x00, 0xF8, 0xF8, 0x18, 0x18, 0x18, // A9
    0x18, 0x18, 0x18, 0xF8, 0xF8, 0x00, 0x00, 0x00, // AA
    0x80, 0x80, 0x80, 0x40, 0x40, 0x20, 0x18, 0x07, // AB
    0x01, 0x01, 0x01, 0x02, 0x02, 0x04, 0x18, 0xE0, // AC
    0xE0, 0x18, 0x04, 0x02, 0x02, 0x01, 0x01, 0x01, // AD
    0x07, 0x18, 0x20, 0x40, 0x40, 0x80, 0x80, 0x80, // AE
    0x81, 0x42, 0x24, 0x18, 0x18, 0x24, 0x42, 0x81, // AF
    0xF0, 0xF0, 0xF0, 0xF0, 0x00, 0x00, 0x00, 0x00, // B0
    0x0F, 0x0F, 0x0F, 0x0F, 0x00, 0x00, 0x00, 0x00, // B1
    0x00, 0x00, 0x00, 0x00, 0x0F, 0x0F, 0x0F, 0x0F, // B2
    0x00, 0x00, 0x00, 0x00, 0xF0, 0xF0, 0xF0, 0xF0, // B3
    0xF0, 0xF0, 0xF0, 0xF0, 0xF0, 0xF0, 0xF0, 0xF0, // B4
    0x0F, 0x0F, 0x0F, 0x0F, 0x0F, 0x0F, 0x0F, 0x0F, // B5
    0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00, // B6
    0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0xFF, 0xFF, // B7
    0xF0, 0xF0, 0xF0, 0xF0, 0x0F, 0x0F, 0x0F, 0x0F, // B8
    0x0F, 0x0F, 0x0F, 0x0F, 0xF0, 0xF0, 0xF0, 0xF0, // B9
    0x0F, 0x0F, 0x0F, 0x0F, 0xFF, 0xFF, 0xFF, 0xFF, // BA
    0xF0, 0xF0, 0xF0, 0xF0, 0xFF, 0xFF, 0xFF, 0xFF, // BB
    0xFF, 0xFF, 0xFF, 0xFF, 0xF0, 0xF0, 0xF0, 0xF0, // BC
    0xFF, 0xFF, 0xFF, 0xFF, 0x0F, 0x0F, 0x0F, 0x0F, // BD
    0x01, 0x03, 0x07, 0x0F, 0x1F, 0x3F, 0x7F, 0xFF, // BE
    0xFF, 0x7F, 0x3F, 0x1F, 0x0F, 0x07, 0x03, 0x01, // BF
    0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, // C0
    0xFF, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, // C1
    0xFF, 0x80, 0x80, 0x9C, 0x9C, 0x9C, 0x80, 0x80, // C2
    0xFF, 0xFF, 0xFF, 0xE3, 0xE3, 0xE3, 0xFF, 0xFF, // C3
    0x18, 0x3C, 0x7E, 0x3C, 0x18, 0x3C, 0x7E, 0xFF, // C4
    0xFF, 0x00, 0xFF, 0x00, 0xFF, 0x00, 0xFF, 0x00, // C5
    0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, // C6
    0x55, 0xAA, 0x55, 0xAA, 0x55, 0xAA, 0x55, 0xAA, // C7
    0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0xFF, // C8
    0x00, 0x10, 0x38, 0x7C, 0xFE, 0x7C, 0x38, 0x10, // C9
    0x38, 0x10, 0x92, 0xFE, 0x92, 0x10, 0x38, 0x7C, // CA
    0x00, 0x6C, 0xFE, 0xFE, 0xFE, 0x7C, 0x38, 0x10, // CB
    0x10, 0x38, 0x7C, 0xFE, 0xFE, 0x7C, 0x10, 0x7C, // CC
    0xE7, 0xE7, 0x42, 0xFF, 0xFF, 0x42, 0xE7, 0xE7, // CD
    0xDB, 0xFF, 0xDB, 0x18, 0x18, 0xDB, 0xFF, 0xDB, // CE
    0x3C, 0x7E, 0xFF, 0xFF, 0xFF, 0xFF, 0x7E, 0x3C, // CF
    0xC0, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // D0
    0x30, 0x30, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // D1
    0x0C, 0x0C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // D2
    0x03, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // D3
    0x00, 0x00, 0xC0, 0xC0, 0x00, 0x00, 0x00, 0x00, // D4
    0x00, 0x00, 0x30, 0x30, 0x00, 0x00, 0x00, 0x00, // D5
    0x00, 0x00, 0x0C, 0x0C, 0x00, 0x00, 0x00, 0x00, // D6
    0x00, 0x00, 0x03, 0x03, 0x00, 0x00, 0x00, 0x00, // D7
    0x00, 0x00, 0x00, 0x00, 0xC0, 0xC0, 0x00, 0x00, // D8
    0x00, 0x00, 0x00, 0x00, 0x30, 0x30, 0x00, 0x00, // D9
    0x00, 0x00, 0x00, 0x00, 0x0C, 0x0C, 0x00, 0x00, // DA
    0x00, 0x00, 0x00, 0x00, 0x03, 0x03, 0x00, 0x00, // DB
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xC0, 0xC0, // DC
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x30, // DD
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0C, 0x0C, // DE
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x03, // DF
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0F, 0x0F, // E0
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3F, 0x3F, // E1
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, // E2
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFC, 0xFC, // E3
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xF0, 0xF0, // E4
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xC0, 0xC0, // E5
    0x00, 0x00, 0x00, 0x00, 0xC0, 0xC0, 0xC0, 0xC0, // E6
    0x00, 0x00, 0xC0, 0xC0, 0xC0, 0xC0, 0xC0, 0xC0, // E7
    0xC0, 0xC0, 0xC0, 0xC0, 0xC0, 0xC0, 0xC0, 0xC0, // E8
    0xC0, 0xC0, 0xC0, 0xC0, 0xC0, 0xC0, 0x00, 0x00, // E9
    0xC0, 0xC0, 0xC0, 0xC0, 0x00, 0x00, 0x00, 0x00, // EA
    0xC0, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // EB
    0xF0, 0xF0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // EC
    0xFC, 0xFC, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // ED
    0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // EE
    0x3F, 0x3F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // EF
    0x0F, 0x0F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // F0
    0x03, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // F1
    0x03, 0x03, 0x03, 0x03, 0x00, 0x00, 0x00, 0x00, // F2
    0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x00, 0x00, // F3
    0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, // F4
    0x00, 0x00, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, // F5
    0x00, 0x00, 0x00, 0x00, 0x03, 0x03, 0x03, 0x03, // F6
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x03, // F7
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFF, // F8
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, // F9
    0x00, 0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0xFF, // FA
    0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0xFF, 0xFF, // FB
    0x00, 0x00, 0x00, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, // FC
    0x00, 0x00, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, // FD
    0x00, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, // FE
    0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, // FF
};
static const uint8_t _sdtx_font_cpc[2048] = {
    0xFF, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xFF, // 00
    0xFF, 0xC0, 0xC0, 0xC0, 0xC0, 0xC0, 0xC0, 0xC0, // 01
    0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0xFF, // 02
    0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0xFF, // 03
    0x0C, 0x18, 0x30, 0x7E, 0x0C, 0x18, 0x30, 0x00, // 04
    0xFF, 0xC3, 0xE7, 0xDB, 0xDB, 0xE7, 0xC3, 0xFF, // 05
    0x00, 0x01, 0x03, 0x06, 0xCC, 0x78, 0x30, 0x00, // 06
    0x3C, 0x66, 0xC3, 0xC3, 0xFF, 0x24, 0xE7, 0x00, // 07
    0x00, 0x00, 0x30, 0x60, 0xFF, 0x60, 0x30, 0x00, // 08
    0x00, 0x00, 0x0C, 0x06, 0xFF, 0x06, 0x0C, 0x00, // 09
    0x18, 0x18, 0x18, 0x18, 0xDB, 0x7E, 0x3C, 0x18, // 0A
    0x18, 0x3C, 0x7E, 0xDB, 0x18, 0x18, 0x18, 0x18, // 0B
    0x18, 0x5A, 0x3C, 0x99, 0xDB, 0x7E, 0x3C, 0x18, // 0C
    0x00, 0x03, 0x33, 0x63, 0xFE, 0x60, 0x30, 0x00, // 0D
    0x3C, 0x66, 0xFF, 0xDB, 0xDB, 0xFF, 0x66, 0x3C, // 0E
    0x3C, 0x66, 0xC3, 0xDB, 0xDB, 0xC3, 0x66, 0x3C, // 0F
    0xFF, 0xC3, 0xC3, 0xFF, 0xC3, 0xC3, 0xC3, 0xFF, // 10
    0x3C, 0x7E, 0xDB, 0xDB, 0xDF, 0xC3, 0x66, 0x3C, // 11
    0x3C, 0x66, 0xC3, 0xDF, 0xDB, 0xDB, 0x7E, 0x3C, // 12
    0x3C, 0x66, 0xC3, 0xFB, 0xDB, 0xDB, 0x7E, 0x3C, // 13
    0x3C, 0x7E, 0xDB, 0xDB, 0xFB, 0xC3, 0x66, 0x3C, // 14
    0x00, 0x01, 0x33, 0x1E, 0xCE, 0x7B, 0x31, 0x00, // 15
    0x7E, 0x66, 0x66, 0x66, 0x66, 0x66, 0x66, 0xE7, // 16
    0x03, 0x03, 0x03, 0xFF, 0x03, 0x03, 0x03, 0x00, // 17
    0xFF, 0x66, 0x3C, 0x18, 0x18, 0x3C, 0x66, 0xFF, // 18
    0x18, 0x18, 0x3C, 0x3C, 0x3C, 0x3C, 0x18, 0x18, // 19
    0x3C, 0x66, 0x66, 0x30, 0x18, 0x00, 0x18, 0x00, // 1A
    0x3C, 0x66, 0xC3, 0xFF, 0xC3, 0xC3, 0x66, 0x3C, // 1B
    0xFF, 0xDB, 0xDB, 0xDB, 0xFB, 0xC3, 0xC3, 0xFF, // 1C
    0xFF, 0xC3, 0xC3, 0xFB, 0xDB, 0xDB, 0xDB, 0xFF, // 1D
    0xFF, 0xC3, 0xC3, 0xDF, 0xDB, 0xDB, 0xDB, 0xFF, // 1E
    0xFF, 0xDB, 0xDB, 0xDB, 0xDF, 0xC3, 0xC3, 0xFF, // 1F
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // 20
    0x18, 0x18, 0x18, 0x18, 0x18, 0x00, 0x18, 0x00, // 21
    0x6C, 0x6C, 0x6C, 0x00, 0x00, 0x00, 0x00, 0x00, // 22
    0x6C, 0x6C, 0xFE, 0x6C, 0xFE, 0x6C, 0x6C, 0x00, // 23
    0x18, 0x3E, 0x58, 0x3C, 0x1A, 0x7C, 0x18, 0x00, // 24
    0x00, 0xC6, 0xCC, 0x18, 0x30, 0x66, 0xC6, 0x00, // 25
    0x38, 0x6C, 0x38, 0x76, 0xDC, 0xCC, 0x76, 0x00, // 26
    0x18, 0x18, 0x30, 0x00, 0x00, 0x00, 0x00, 0x00, // 27
    0x0C, 0x18, 0x30, 0x30, 0x30, 0x18, 0x0C, 0x00, // 28
    0x30, 0x18, 0x0C, 0x0C, 0x0C, 0x18, 0x30, 0x00, // 29
    0x00, 0x66, 0x3C, 0xFF, 0x3C, 0x66, 0x00, 0x00, // 2A
    0x00, 0x18, 0x18, 0x7E, 0x18, 0x18, 0x00, 0x00, // 2B
    0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0x18, 0x30, // 2C
    0x00, 0x00, 0x00, 0x7E, 0x00, 0x00, 0x00, 0x00, // 2D
    0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0x18, 0x00, // 2E
    0x06, 0x0C, 0x18, 0x30, 0x60, 0xC0, 0x80, 0x00, // 2F
    0x7C, 0xC6, 0xCE, 0xD6, 0xE6, 0xC6, 0x7C, 0x00, // 30
    0x18, 0x38, 0x18, 0x18, 0x18, 0x18, 0x7E, 0x00, // 31
    0x3C, 0x66, 0x06, 0x3C, 0x60, 0x66, 0x7E, 0x00, // 32
    0x3C, 0x66, 0x06, 0x1C, 0x06, 0x66, 0x3C, 0x00, // 33
    0x1C, 0x3C, 0x6C, 0xCC, 0xFE, 0x0C, 0x1E, 0x00, // 34
    0x7E, 0x62, 0x60, 0x7C, 0x06, 0x66, 0x3C, 0x00, // 35
    0x3C, 0x66, 0x60, 0x7C, 0x66, 0x66, 0x3C, 0x00, // 36
    0x7E, 0x66, 0x06, 0x0C, 0x18, 0x18, 0x18, 0x00, // 37
    0x3C, 0x66, 0x66, 0x3C, 0x66, 0x66, 0x3C, 0x00, // 38
    0x3C, 0x66, 0x66, 0x3E, 0x06, 0x66, 0x3C, 0x00, // 39
    0x00, 0x00, 0x18, 0x18, 0x00, 0x18, 0x18, 0x00, // 3A
    0x00, 0x00, 0x18, 0x18, 0x00, 0x18, 0x18, 0x30, // 3B
    0x0C, 0x18, 0x30, 0x60, 0x30, 0x18, 0x0C, 0x00, // 3C
    0x00, 0x00, 0x7E, 0x00, 0x00, 0x7E, 0x00, 0x00, // 3D
    0x60, 0x30, 0x18, 0x0C, 0x18, 0x30, 0x60, 0x00, // 3E
    0x3C, 0x66, 0x66, 0x0C, 0x18, 0x00, 0x18, 0x00, // 3F
    0x7C, 0xC6, 0xDE, 0xDE, 0xDE, 0xC0, 0x7C, 0x00, // 40
    0x18, 0x3C, 0x66, 0x66, 0x7E, 0x66, 0x66, 0x00, // 41
    0xFC, 0x66, 0x66, 0x7C, 0x66, 0x66, 0xFC, 0x00, // 42
    0x3C, 0x66, 0xC0, 0xC0, 0xC0, 0x66, 0x3C, 0x00, // 43
    0xF8, 0x6C, 0x66, 0x66, 0x66, 0x6C, 0xF8, 0x00, // 44
    0xFE, 0x62, 0x68, 0x78, 0x68, 0x62, 0xFE, 0x00, // 45
    0xFE, 0x62, 0x68, 0x78, 0x68, 0x60, 0xF0, 0x00, // 46
    0x3C, 0x66, 0xC0, 0xC0, 0xCE, 0x66, 0x3E, 0x00, // 47
    0x66, 0x66, 0x66, 0x7E, 0x66, 0x66, 0x66, 0x00, // 48
    0x7E, 0x18, 0x18, 0x18, 0x18, 0x18, 0x7E, 0x00, // 49
    0x1E, 0x0C, 0x0C, 0x0C, 0xCC, 0xCC, 0x78, 0x00, // 4A
    0xE6, 0x66, 0x6C, 0x78, 0x6C, 0x66, 0xE6, 0x00, // 4B
    0xF0, 0x60, 0x60, 0x60, 0x62, 0x66, 0xFE, 0x00, // 4C
    0xC6, 0xEE, 0xFE, 0xFE, 0xD6, 0xC6, 0xC6, 0x00, // 4D
    0xC6, 0xE6, 0xF6, 0xDE, 0xCE, 0xC6, 0xC6, 0x00, // 4E
    0x38, 0x6C, 0xC6, 0xC6, 0xC6, 0x6C, 0x38, 0x00, // 4F
    0xFC, 0x66, 0x66, 0x7C, 0x60, 0x60, 0xF0, 0x00, // 50
    0x38, 0x6C, 0xC6, 0xC6, 0xDA, 0xCC, 0x76, 0x00, // 51
    0xFC, 0x66, 0x66, 0x7C, 0x6C, 0x66, 0xE6, 0x00, // 52
    0x3C, 0x66, 0x60, 0x3C, 0x06, 0x66, 0x3C, 0x00, // 53
    0x7E, 0x5A, 0x18, 0x18, 0x18, 0x18, 0x3C, 0x00, // 54
    0x66, 0x66, 0x66, 0x66, 0x66, 0x66, 0x3C, 0x00, // 55
    0x66, 0x66, 0x66, 0x66, 0x66, 0x3C, 0x18, 0x00, // 56
    0xC6, 0xC6, 0xC6, 0xD6, 0xFE, 0xEE, 0xC6, 0x00, // 57
    0xC6, 0x6C, 0x38, 0x38, 0x6C, 0xC6, 0xC6, 0x00, // 58
    0x66, 0x66, 0x66, 0x3C, 0x18, 0x18, 0x3C, 0x00, // 59
    0xFE, 0xC6, 0x8C, 0x18, 0x32, 0x66, 0xFE, 0x00, // 5A
    0x3C, 0x30, 0x30, 0x30, 0x30, 0x30, 0x3C, 0x00, // 5B
    0xC0, 0x60, 0x30, 0x18, 0x0C, 0x06, 0x02, 0x00, // 5C
    0x3C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x3C, 0x00, // 5D
    0x18, 0x3C, 0x7E, 0x18, 0x18, 0x18, 0x18, 0x00, // 5E
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFF, // 5F
    0x30, 0x18, 0x0C, 0x00, 0x00, 0x00, 0x00, 0x00, // 60
    0x00, 0x00, 0x78, 0x0C, 0x7C, 0xCC, 0x76, 0x00, // 61
    0xE0, 0x60, 0x7C, 0x66, 0x66, 0x66, 0xDC, 0x00, // 62
    0x00, 0x00, 0x3C, 0x66, 0x60, 0x66, 0x3C, 0x00, // 63
    0x1C, 0x0C, 0x7C, 0xCC, 0xCC, 0xCC, 0x76, 0x00, // 64
    0x00, 0x00, 0x3C, 0x66, 0x7E, 0x60, 0x3C, 0x00, // 65
    0x1C, 0x36, 0x30, 0x78, 0x30, 0x30, 0x78, 0x00, // 66
    0x00, 0x00, 0x3E, 0x66, 0x66, 0x3E, 0x06, 0x7C, // 67
    0xE0, 0x60, 0x6C, 0x76, 0x66, 0x66, 0xE6, 0x00, // 68
    0x18, 0x00, 0x38, 0x18, 0x18, 0x18, 0x3C, 0x00, // 69
    0x06, 0x00, 0x0E, 0x06, 0x06, 0x66, 0x66, 0x3C, // 6A
    0xE0, 0x60, 0x66, 0x6C, 0x78, 0x6C, 0xE6, 0x00, // 6B
    0x38, 0x18, 0x18, 0x18, 0x18, 0x18, 0x3C, 0x00, // 6C
    0x00, 0x00, 0x6C, 0xFE, 0xD6, 0xD6, 0xC6, 0x00, // 6D
    0x00, 0x00, 0xDC, 0x66, 0x66, 0x66, 0x66, 0x00, // 6E
    0x00, 0x00, 0x3C, 0x66, 0x66, 0x66, 0x3C, 0x00, // 6F
    0x00, 0x00, 0xDC, 0x66, 0x66, 0x7C, 0x60, 0xF0, // 70
    0x00, 0x00, 0x76, 0xCC, 0xCC, 0x7C, 0x0C, 0x1E, // 71
    0x00, 0x00, 0xDC, 0x76, 0x60, 0x60, 0xF0, 0x00, // 72
    0x00, 0x00, 0x3C, 0x60, 0x3C, 0x06, 0x7C, 0x00, // 73
    0x30, 0x30, 0x7C, 0x30, 0x30, 0x36, 0x1C, 0x00, // 74
    0x00, 0x00, 0x66, 0x66, 0x66, 0x66, 0x3E, 0x00, // 75
    0x00, 0x00, 0x66, 0x66, 0x66, 0x3C, 0x18, 0x00, // 76
    0x00, 0x00, 0xC6, 0xD6, 0xD6, 0xFE, 0x6C, 0x00, // 77
    0x00, 0x00, 0xC6, 0x6C, 0x38, 0x6C, 0xC6, 0x00, // 78
    0x00, 0x00, 0x66, 0x66, 0x66, 0x3E, 0x06, 0x7C, // 79
    0x00, 0x00, 0x7E, 0x4C, 0x18, 0x32, 0x7E, 0x00, // 7A
    0x0E, 0x18, 0x18, 0x70, 0x18, 0x18, 0x0E, 0x00, // 7B
    0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x00, // 7C
    0x70, 0x18, 0x18, 0x0E, 0x18, 0x18, 0x70, 0x00, // 7D
    0x76, 0xDC, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // 7E
    0xCC, 0x33, 0xCC, 0x33, 0xCC, 0x33, 0xCC, 0x33, // 7F
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // 80
    0xF0, 0xF0, 0xF0, 0xF0, 0x00, 0x00, 0x00, 0x00, // 81
    0x0F, 0x0F, 0x0F, 0x0F, 0x00, 0x00, 0x00, 0x00, // 82
    0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00, // 83
    0x00, 0x00, 0x00, 0x00, 0xF0, 0xF0, 0xF0, 0xF0, // 84
    0xF0, 0xF0, 0xF0, 0xF0, 0xF0, 0xF0, 0xF0, 0xF0, // 85
    0x0F, 0x0F, 0x0F, 0x0F, 0xF0, 0xF0, 0xF0, 0xF0, // 86
    0xFF, 0xFF, 0xFF, 0xFF, 0xF0, 0xF0, 0xF0, 0xF0, // 87
    0x00, 0x00, 0x00, 0x00, 0x0F, 0x0F, 0x0F, 0x0F, // 88
    0xF0, 0xF0, 0xF0, 0xF0, 0x0F, 0x0F, 0x0F, 0x0F, // 89
    0x0F, 0x0F, 0x0F, 0x0F, 0x0F, 0x0F, 0x0F, 0x0F, // 8A
    0xFF, 0xFF, 0xFF, 0xFF, 0x0F, 0x0F, 0x0F, 0x0F, // 8B
    0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0xFF, 0xFF, // 8C
    0xF0, 0xF0, 0xF0, 0xF0, 0xFF, 0xFF, 0xFF, 0xFF, // 8D
    0x0F, 0x0F, 0x0F, 0x0F, 0xFF, 0xFF, 0xFF, 0xFF, // 8E
    0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, // 8F
    0x00, 0x00, 0x00, 0x18, 0x18, 0x00, 0x00, 0x00, // 90
    0x18, 0x18, 0x18, 0x18, 0x18, 0x00, 0x00, 0x00, // 91
    0x00, 0x00, 0x00, 0x1F, 0x1F, 0x00, 0x00, 0x00, // 92
    0x18, 0x18, 0x18, 0x1F, 0x0F, 0x00, 0x00, 0x00, // 93
    0x00, 0x00, 0x00, 0x18, 0x18, 0x18, 0x18, 0x18, // 94
    0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, // 95
    0x00, 0x00, 0x00, 0x0F, 0x1F, 0x18, 0x18, 0x18, // 96
    0x18, 0x18, 0x18, 0x1F, 0x1F, 0x18, 0x18, 0x18, // 97
    0x00, 0x00, 0x00, 0xF8, 0xF8, 0x00, 0x00, 0x00, // 98
    0x18, 0x18, 0x18, 0xF8, 0xF0, 0x00, 0x00, 0x00, // 99
    0x00, 0x00, 0x00, 0xFF, 0xFF, 0x00, 0x00, 0x00, // 9A
    0x18, 0x18, 0x18, 0xFF, 0xFF, 0x00, 0x00, 0x00, // 9B
    0x00, 0x00, 0x00, 0xF0, 0xF8, 0x18, 0x18, 0x18, // 9C
    0x18, 0x18, 0x18, 0xF8, 0xF8, 0x18, 0x18, 0x18, // 9D
    0x00, 0x00, 0x00, 0xFF, 0xFF, 0x18, 0x18, 0x18, // 9E
    0x18, 0x18, 0x18, 0xFF, 0xFF, 0x18, 0x18, 0x18, // 9F
    0x10, 0x38, 0x6C, 0xC6, 0x00, 0x00, 0x00, 0x00, // A0
    0x0C, 0x18, 0x30, 0x00, 0x00, 0x00, 0x00, 0x00, // A1
    0x66, 0x66, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // A2
    0x3C, 0x66, 0x60, 0xF8, 0x60, 0x66, 0xFE, 0x00, // A3
    0x38, 0x44, 0xBA, 0xA2, 0xBA, 0x44, 0x38, 0x00, // A4
    0x7E, 0xF4, 0xF4, 0x74, 0x34, 0x34, 0x34, 0x00, // A5
    0x1E, 0x30, 0x38, 0x6C, 0x38, 0x18, 0xF0, 0x00, // A6
    0x18, 0x18, 0x0C, 0x00, 0x00, 0x00, 0x00, 0x00, // A7
    0x40, 0xC0, 0x44, 0x4C, 0x54, 0x1E, 0x04, 0x00, // A8
    0x40, 0xC0, 0x4C, 0x52, 0x44, 0x08, 0x1E, 0x00, // A9
    0xE0, 0x10, 0x62, 0x16, 0xEA, 0x0F, 0x02, 0x00, // AA
    0x00, 0x18, 0x18, 0x7E, 0x18, 0x18, 0x7E, 0x00, // AB
    0x18, 0x18, 0x00, 0x7E, 0x00, 0x18, 0x18, 0x00, // AC
    0x00, 0x00, 0x00, 0x7E, 0x06, 0x06, 0x00, 0x00, // AD
    0x18, 0x00, 0x18, 0x30, 0x66, 0x66, 0x3C, 0x00, // AE
    0x18, 0x00, 0x18, 0x18, 0x18, 0x18, 0x18, 0x00, // AF
    0x00, 0x00, 0x73, 0xDE, 0xCC, 0xDE, 0x73, 0x00, // B0
    0x7C, 0xC6, 0xC6, 0xFC, 0xC6, 0xC6, 0xF8, 0xC0, // B1
    0x00, 0x66, 0x66, 0x3C, 0x66, 0x66, 0x3C, 0x00, // B2
    0x3C, 0x60, 0x60, 0x3C, 0x66, 0x66, 0x3C, 0x00, // B3
    0x00, 0x00, 0x1E, 0x30, 0x7C, 0x30, 0x1E, 0x00, // B4
    0x38, 0x6C, 0xC6, 0xFE, 0xC6, 0x6C, 0x38, 0x00, // B5
    0x00, 0xC0, 0x60, 0x30, 0x38, 0x6C, 0xC6, 0x00, // B6
    0x00, 0x00, 0x66, 0x66, 0x66, 0x7C, 0x60, 0x60, // B7
    0x00, 0x00, 0x00, 0xFE, 0x6C, 0x6C, 0x6C, 0x00, // B8
    0x00, 0x00, 0x00, 0x7E, 0xD8, 0xD8, 0x70, 0x00, // B9
    0x03, 0x06, 0x0C, 0x3C, 0x66, 0x3C, 0x60, 0xC0, // BA
    0x03, 0x06, 0x0C, 0x66, 0x66, 0x3C, 0x60, 0xC0, // BB
    0x00, 0xE6, 0x3C, 0x18, 0x38, 0x6C, 0xC7, 0x00, // BC
    0x00, 0x00, 0x66, 0xC3, 0xDB, 0xDB, 0x7E, 0x00, // BD
    0xFE, 0xC6, 0x60, 0x30, 0x60, 0xC6, 0xFE, 0x00, // BE
    0x00, 0x7C, 0xC6, 0xC6, 0xC6, 0x6C, 0xEE, 0x00, // BF
    0x18, 0x30, 0x60, 0xC0, 0x80, 0x00, 0x00, 0x00, // C0
    0x18, 0x0C, 0x06, 0x03, 0x01, 0x00, 0x00, 0x00, // C1
    0x00, 0x00, 0x00, 0x01, 0x03, 0x06, 0x0C, 0x18, // C2
    0x00, 0x00, 0x00, 0x80, 0xC0, 0x60, 0x30, 0x18, // C3
    0x18, 0x3C, 0x66, 0xC3, 0x81, 0x00, 0x00, 0x00, // C4
    0x18, 0x0C, 0x06, 0x03, 0x03, 0x06, 0x0C, 0x18, // C5
    0x00, 0x00, 0x00, 0x81, 0xC3, 0x66, 0x3C, 0x18, // C6
    0x18, 0x30, 0x60, 0xC0, 0xC0, 0x60, 0x30, 0x18, // C7
    0x18, 0x30, 0x60, 0xC1, 0x83, 0x06, 0x0C, 0x18, // C8
    0x18, 0x0C, 0x06, 0x83, 0xC1, 0x60, 0x30, 0x18, // C9
    0x18, 0x3C, 0x66, 0xC3, 0xC3, 0x66, 0x3C, 0x18, // CA
    0xC3, 0xE7, 0x7E, 0x3C, 0x3C, 0x7E, 0xE7, 0xC3, // CB
    0x03, 0x07, 0x0E, 0x1C, 0x38, 0x70, 0xE0, 0xC0, // CC
    0xC0, 0xE0, 0x70, 0x38, 0x1C, 0x0E, 0x07, 0x03, // CD
    0xCC, 0xCC, 0x33, 0x33, 0xCC, 0xCC, 0x33, 0x33, // CE
    0xAA, 0x55, 0xAA, 0x55, 0xAA, 0x55, 0xAA, 0x55, // CF
    0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // D0
    0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, // D1
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, // D2
    0xC0, 0xC0, 0xC0, 0xC0, 0xC0, 0xC0, 0xC0, 0xC0, // D3
    0xFF, 0xFE, 0xFC, 0xF8, 0xF0, 0xE0, 0xC0, 0x80, // D4
    0xFF, 0x7F, 0x3F, 0x1F, 0x0F, 0x07, 0x03, 0x01, // D5
    0x01, 0x03, 0x07, 0x0F, 0x1F, 0x3F, 0x7F, 0xFF, // D6
    0x80, 0xC0, 0xE0, 0xF0, 0xF8, 0xFC, 0xFE, 0xFF, // D7
    0xAA, 0x55, 0xAA, 0x55, 0x00, 0x00, 0x00, 0x00, // D8
    0x0A, 0x05, 0x0A, 0x05, 0x0A, 0x05, 0x0A, 0x05, // D9
    0x00, 0x00, 0x00, 0x00, 0xAA, 0x55, 0xAA, 0x55, // DA
    0xA0, 0x50, 0xA0, 0x50, 0xA0, 0x50, 0xA0, 0x50, // DB
    0xAA, 0x54, 0xA8, 0x50, 0xA0, 0x40, 0x80, 0x00, // DC
    0xAA, 0x55, 0x2A, 0x15, 0x0A, 0x05, 0x02, 0x01, // DD
    0x01, 0x02, 0x05, 0x0A, 0x15, 0x2A, 0x55, 0xAA, // DE
    0x00, 0x80, 0x40, 0xA0, 0x50, 0xA8, 0x54, 0xAA, // DF
    0x7E, 0xFF, 0x99, 0xFF, 0xBD, 0xC3, 0xFF, 0x7E, // E0
    0x7E, 0xFF, 0x99, 0xFF, 0xC3, 0xBD, 0xFF, 0x7E, // E1
    0x38, 0x38, 0xFE, 0xFE, 0xFE, 0x10, 0x38, 0x00, // E2
    0x10, 0x38, 0x7C, 0xFE, 0x7C, 0x38, 0x10, 0x00, // E3
    0x6C, 0xFE, 0xFE, 0xFE, 0x7C, 0x38, 0x10, 0x00, // E4
    0x10, 0x38, 0x7C, 0xFE, 0xFE, 0x10, 0x38, 0x00, // E5
    0x00, 0x3C, 0x66, 0xC3, 0xC3, 0x66, 0x3C, 0x00, // E6
    0x00, 0x3C, 0x7E, 0xFF, 0xFF, 0x7E, 0x3C, 0x00, // E7
    0x00, 0x7E, 0x66, 0x66, 0x66, 0x66, 0x7E, 0x00, // E8
    0x00, 0x7E, 0x7E, 0x7E, 0x7E, 0x7E, 0x7E, 0x00, // E9
    0x0F, 0x07, 0x0D, 0x78, 0xCC, 0xCC, 0xCC, 0x78, // EA
    0x3C, 0x66, 0x66, 0x66, 0x3C, 0x18, 0x7E, 0x18, // EB
    0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x3C, 0x7C, 0x38, // EC
    0x18, 0x1C, 0x1E, 0x1B, 0x18, 0x78, 0xF8, 0x70, // ED
    0x99, 0x5A, 0x24, 0xC3, 0xC3, 0x24, 0x5A, 0x99, // EE
    0x10, 0x38, 0x38, 0x38, 0x38, 0x38, 0x7C, 0xD6, // EF
    0x18, 0x3C, 0x7E, 0xFF, 0x18, 0x18, 0x18, 0x18, // F0
    0x18, 0x18, 0x18, 0x18, 0xFF, 0x7E, 0x3C, 0x18, // F1
    0x10, 0x30, 0x70, 0xFF, 0xFF, 0x70, 0x30, 0x10, // F2
    0x08, 0x0C, 0x0E, 0xFF, 0xFF, 0x0E, 0x0C, 0x08, // F3
    0x00, 0x00, 0x18, 0x3C, 0x7E, 0xFF, 0xFF, 0x00, // F4
    0x00, 0x00, 0xFF, 0xFF, 0x7E, 0x3C, 0x18, 0x00, // F5
    0x80, 0xE0, 0xF8, 0xFE, 0xF8, 0xE0, 0x80, 0x00, // F6
    0x02, 0x0E, 0x3E, 0xFE, 0x3E, 0x0E, 0x02, 0x00, // F7
    0x38, 0x38, 0x92, 0x7C, 0x10, 0x28, 0x28, 0x28, // F8
    0x38, 0x38, 0x10, 0xFE, 0x10, 0x28, 0x44, 0x82, // F9
    0x38, 0x38, 0x12, 0x7C, 0x90, 0x28, 0x24, 0x22, // FA
    0x38, 0x38, 0x90, 0x7C, 0x12, 0x28, 0x48, 0x88, // FB
    0x00, 0x3C, 0x18, 0x3C, 0x3C, 0x3C, 0x18, 0x00, // FC
    0x3C, 0xFF, 0xFF, 0x18, 0x0C, 0x18, 0x30, 0x18, // FD
    0x18, 0x3C, 0x7E, 0x18, 0x18, 0x7E, 0x3C, 0x18, // FE
    0x00, 0x24, 0x66, 0xFF, 0x66, 0x24, 0x00, 0x00, // FF
};
static const uint8_t _sdtx_font_c64[2048] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // 00
    0xF0, 0xF0, 0xF0, 0xF0, 0xF0, 0xF0, 0xF0, 0xF0, // 01
    0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0xFF, 0xFF, // 02
    0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // 03
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFF, // 04
    0xC0, 0xC0, 0xC0, 0xC0, 0xC0, 0xC0, 0xC0, 0xC0, // 05
    0xCC, 0xCC, 0x33, 0x33, 0xCC, 0xCC, 0x33, 0x33, // 06
    0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, // 07
    0x00, 0x00, 0x00, 0x00, 0xCC, 0xCC, 0x33, 0x33, // 08
    0xCC, 0x99, 0x33, 0x66, 0xCC, 0x99, 0x33, 0x66, // 09
    0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, // 0A
    0x18, 0x18, 0x18, 0x1F, 0x1F, 0x18, 0x18, 0x18, // 0B
    0x00, 0x00, 0x00, 0x00, 0x0F, 0x0F, 0x0F, 0x0F, // 0C
    0x18, 0x18, 0x18, 0x1F, 0x1F, 0x00, 0x00, 0x00, // 0D
    0x00, 0x00, 0x00, 0xF8, 0xF8, 0x18, 0x18, 0x18, // 0E
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, // 0F
    0x00, 0x00, 0x00, 0x1F, 0x1F, 0x18, 0x18, 0x18, // 10
    0x18, 0x18, 0x18, 0xFF, 0xFF, 0x00, 0x00, 0x00, // 11
    0x00, 0x00, 0x00, 0xFF, 0xFF, 0x18, 0x18, 0x18, // 12
    0x18, 0x18, 0x18, 0xF8, 0xF8, 0x18, 0x18, 0x18, // 13
    0xC0, 0xC0, 0xC0, 0xC0, 0xC0, 0xC0, 0xC0, 0xC0, // 14
    0xE0, 0xE0, 0xE0, 0xE0, 0xE0, 0xE0, 0xE0, 0xE0, // 15
    0x07, 0x07, 0x07, 0x07, 0x07, 0x07, 0x07, 0x07, // 16
    0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // 17
    0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, // 18
    0x00, 0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0xFF, // 19
    0x01, 0x03, 0x06, 0x6C, 0x78, 0x70, 0x60, 0x00, // 1A
    0x00, 0x00, 0x00, 0x00, 0xF0, 0xF0, 0xF0, 0xF0, // 1B
    0x0F, 0x0F, 0x0F, 0x0F, 0x00, 0x00, 0x00, 0x00, // 1C
    0x18, 0x18, 0x18, 0xF8, 0xF8, 0x00, 0x00, 0x00, // 1D
    0xF0, 0xF0, 0xF0, 0xF0, 0x00, 0x00, 0x00, 0x00, // 1E
    0xF0, 0xF0, 0xF0, 0xF0, 0x0F, 0x0F, 0x0F, 0x0F, // 1F
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // 20
    0x18, 0x18, 0x18, 0x18, 0x00, 0x00, 0x18, 0x00, // 21
    0x66, 0x66, 0x66, 0x00, 0x00, 0x00, 0x00, 0x00, // 22
    0x66, 0x66, 0xFF, 0x66, 0xFF, 0x66, 0x66, 0x00, // 23
    0x18, 0x3E, 0x60, 0x3C, 0x06, 0x7C, 0x18, 0x00, // 24
    0x62, 0x66, 0x0C, 0x18, 0x30, 0x66, 0x46, 0x00, // 25
    0x3C, 0x66, 0x3C, 0x38, 0x67, 0x66, 0x3F, 0x00, // 26
    0x06, 0x0C, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00, // 27
    0x0C, 0x18, 0x30, 0x30, 0x30, 0x18, 0x0C, 0x00, // 28
    0x30, 0x18, 0x0C, 0x0C, 0x0C, 0x18, 0x30, 0x00, // 29
    0x00, 0x66, 0x3C, 0xFF, 0x3C, 0x66, 0x00, 0x00, // 2A
    0x00, 0x18, 0x18, 0x7E, 0x18, 0x18, 0x00, 0x00, // 2B
    0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0x18, 0x30, // 2C
    0x00, 0x00, 0x00, 0x7E, 0x00, 0x00, 0x00, 0x00, // 2D
    0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0x18, 0x00, // 2E
    0x00, 0x03, 0x06, 0x0C, 0x18, 0x30, 0x60, 0x00, // 2F
    0x3C, 0x66, 0x6E, 0x76, 0x66, 0x66, 0x3C, 0x00, // 30
    0x18, 0x18, 0x38, 0x18, 0x18, 0x18, 0x7E, 0x00, // 31
    0x3C, 0x66, 0x06, 0x0C, 0x30, 0x60, 0x7E, 0x00, // 32
    0x3C, 0x66, 0x06, 0x1C, 0x06, 0x66, 0x3C, 0x00, // 33
    0x06, 0x0E, 0x1E, 0x66, 0x7F, 0x06, 0x06, 0x00, // 34
    0x7E, 0x60, 0x7C, 0x06, 0x06, 0x66, 0x3C, 0x00, // 35
    0x3C, 0x66, 0x60, 0x7C, 0x66, 0x66, 0x3C, 0x00, // 36
    0x7E, 0x66, 0x0C, 0x18, 0x18, 0x18, 0x18, 0x00, // 37
    0x3C, 0x66, 0x66, 0x3C, 0x66, 0x66, 0x3C, 0x00, // 38
    0x3C, 0x66, 0x66, 0x3E, 0x06, 0x66, 0x3C, 0x00, // 39
    0x00, 0x00, 0x18, 0x00, 0x00, 0x18, 0x00, 0x00, // 3A
    0x00, 0x00, 0x18, 0x00, 0x00, 0x18, 0x18, 0x30, // 3B
    0x0E, 0x18, 0x30, 0x60, 0x30, 0x18, 0x0E, 0x00, // 3C
    0x00, 0x00, 0x7E, 0x00, 0x7E, 0x00, 0x00, 0x00, // 3D
    0x70, 0x18, 0x0C, 0x06, 0x0C, 0x18, 0x70, 0x00, // 3E
    0x3C, 0x66, 0x06, 0x0C, 0x18, 0x00, 0x18, 0x00, // 3F
    0x3C, 0x66, 0x6E, 0x6E, 0x60, 0x62, 0x3C, 0x00, // 40
    0x18, 0x3C, 0x66, 0x7E, 0x66, 0x66, 0x66, 0x00, // 41
    0x7C, 0x66, 0x66, 0x7C, 0x66, 0x66, 0x7C, 0x00, // 42
    0x3C, 0x66, 0x60, 0x60, 0x60, 0x66, 0x3C, 0x00, // 43
    0x78, 0x6C, 0x66, 0x66, 0x66, 0x6C, 0x78, 0x00, // 44
    0x7E, 0x60, 0x60, 0x78, 0x60, 0x60, 0x7E, 0x00, // 45
    0x7E, 0x60, 0x60, 0x78, 0x60, 0x60, 0x60, 0x00, // 46
    0x3C, 0x66, 0x60, 0x6E, 0x66, 0x66, 0x3C, 0x00, // 47
    0x66, 0x66, 0x66, 0x7E, 0x66, 0x66, 0x66, 0x00, // 48
    0x3C, 0x18, 0x18, 0x18, 0x18, 0x18, 0x3C, 0x00, // 49
    0x1E, 0x0C, 0x0C, 0x0C, 0x0C, 0x6C, 0x38, 0x00, // 4A
    0x66, 0x6C, 0x78, 0x70, 0x78, 0x6C, 0x66, 0x00, // 4B
    0x60, 0x60, 0x60, 0x60, 0x60, 0x60, 0x7E, 0x00, // 4C
    0x63, 0x77, 0x7F, 0x6B, 0x63, 0x63, 0x63, 0x00, // 4D
    0x66, 0x76, 0x7E, 0x7E, 0x6E, 0x66, 0x66, 0x00, // 4E
    0x3C, 0x66, 0x66, 0x66, 0x66, 0x66, 0x3C, 0x00, // 4F
    0x7C, 0x66, 0x66, 0x7C, 0x60, 0x60, 0x60, 0x00, // 50
    0x3C, 0x66, 0x66, 0x66, 0x66, 0x3C, 0x0E, 0x00, // 51
    0x7C, 0x66, 0x66, 0x7C, 0x78, 0x6C, 0x66, 0x00, // 52
    0x3C, 0x66, 0x60, 0x3C, 0x06, 0x66, 0x3C, 0x00, // 53
    0x7E, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x00, // 54
    0x66, 0x66, 0x66, 0x66, 0x66, 0x66, 0x3C, 0x00, // 55
    0x66, 0x66, 0x66, 0x66, 0x66, 0x3C, 0x18, 0x00, // 56
    0x63, 0x63, 0x63, 0x6B, 0x7F, 0x77, 0x63, 0x00, // 57
    0x66, 0x66, 0x3C, 0x18, 0x3C, 0x66, 0x66, 0x00, // 58
    0x66, 0x66, 0x66, 0x3C, 0x18, 0x18, 0x18, 0x00, // 59
    0x7E, 0x06, 0x0C, 0x18, 0x30, 0x60, 0x7E, 0x00, // 5A
    0x3C, 0x30, 0x30, 0x30, 0x30, 0x30, 0x3C, 0x00, // 5B
    0x0C, 0x12, 0x30, 0x7C, 0x30, 0x62, 0xFC, 0x00, // 5C
    0x3C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x3C, 0x00, // 5D
    0x00, 0x18, 0x3C, 0x7E, 0x18, 0x18, 0x18, 0x18, // 5E
    0x00, 0x10, 0x30, 0x7F, 0x7F, 0x30, 0x10, 0x00, // 5F
    0x3C, 0x66, 0x6E, 0x6E, 0x60, 0x62, 0x3C, 0x00, // 60
    0x00, 0x00, 0x3C, 0x06, 0x3E, 0x66, 0x3E, 0x00, // 61
    0x00, 0x60, 0x60, 0x7C, 0x66, 0x66, 0x7C, 0x00, // 62
    0x00, 0x00, 0x3C, 0x60, 0x60, 0x60, 0x3C, 0x00, // 63
    0x00, 0x06, 0x06, 0x3E, 0x66, 0x66, 0x3E, 0x00, // 64
    0x00, 0x00, 0x3C, 0x66, 0x7E, 0x60, 0x3C, 0x00, // 65
    0x00, 0x0E, 0x18, 0x3E, 0x18, 0x18, 0x18, 0x00, // 66
    0x00, 0x00, 0x3E, 0x66, 0x66, 0x3E, 0x06, 0x7C, // 67
    0x00, 0x60, 0x60, 0x7C, 0x66, 0x66, 0x66, 0x00, // 68
    0x00, 0x18, 0x00, 0x38, 0x18, 0x18, 0x3C, 0x00, // 69
    0x00, 0x06, 0x00, 0x06, 0x06, 0x06, 0x06, 0x3C, // 6A
    0x00, 0x60, 0x60, 0x6C, 0x78, 0x6C, 0x66, 0x00, // 6B
    0x00, 0x38, 0x18, 0x18, 0x18, 0x18, 0x3C, 0x00, // 6C
    0x00, 0x00, 0x66, 0x7F, 0x7F, 0x6B, 0x63, 0x00, // 6D
    0x00, 0x00, 0x7C, 0x66, 0x66, 0x66, 0x66, 0x00, // 6E
    0x00, 0x00, 0x3C, 0x66, 0x66, 0x66, 0x3C, 0x00, // 6F
    0x00, 0x00, 0x7C, 0x66, 0x66, 0x7C, 0x60, 0x60, // 70
    0x00, 0x00, 0x3E, 0x66, 0x66, 0x3E, 0x06, 0x06, // 71
    0x00, 0x00, 0x7C, 0x66, 0x60, 0x60, 0x60, 0x00, // 72
    0x00, 0x00, 0x3E, 0x60, 0x3C, 0x06, 0x7C, 0x00, // 73
    0x00, 0x18, 0x7E, 0x18, 0x18, 0x18, 0x0E, 0x00, // 74
    0x00, 0x00, 0x66, 0x66, 0x66, 0x66, 0x3E, 0x00, // 75
    0x00, 0x00, 0x66, 0x66, 0x66, 0x3C, 0x18, 0x00, // 76
    0x00, 0x00, 0x63, 0x6B, 0x7F, 0x3E, 0x36, 0x00, // 77
    0x00, 0x00, 0x66, 0x3C, 0x18, 0x3C, 0x66, 0x00, // 78
    0x00, 0x00, 0x66, 0x66, 0x66, 0x3E, 0x0C, 0x78, // 79
    0x00, 0x00, 0x7E, 0x0C, 0x18, 0x30, 0x7E, 0x00, // 7A
    0x3C, 0x30, 0x30, 0x30, 0x30, 0x30, 0x3C, 0x00, // 7B
    0x0C, 0x12, 0x30, 0x7C, 0x30, 0x62, 0xFC, 0x00, // 7C
    0x3C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x3C, 0x00, // 7D
    0x00, 0x18, 0x3C, 0x7E, 0x18, 0x18, 0x18, 0x18, // 7E
    0x00, 0x10, 0x30, 0x7F, 0x7F, 0x30, 0x10, 0x00, // 7F
    0x00, 0x00, 0x00, 0xFF, 0xFF, 0x00, 0x00, 0x00, // 80
    0x08, 0x1C, 0x3E, 0x7F, 0x7F, 0x1C, 0x3E, 0x00, // 81
    0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, // 82
    0x00, 0x00, 0x00, 0xFF, 0xFF, 0x00, 0x00, 0x00, // 83
    0x00, 0x00, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00, // 84
    0x00, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, // 85
    0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0x00, 0x00, // 86
    0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, // 87
    0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, // 88
    0x00, 0x00, 0x00, 0xE0, 0xF0, 0x38, 0x18, 0x18, // 89
    0x18, 0x18, 0x1C, 0x0F, 0x07, 0x00, 0x00, 0x00, // 8A
    0x18, 0x18, 0x38, 0xF0, 0xE0, 0x00, 0x00, 0x00, // 8B
    0xC0, 0xC0, 0xC0, 0xC0, 0xC0, 0xC0, 0xFF, 0xFF, // 8C
    0xC0, 0xE0, 0x70, 0x38, 0x1C, 0x0E, 0x07, 0x03, // 8D
    0x03, 0x07, 0x0E, 0x1C, 0x38, 0x70, 0xE0, 0xC0, // 8E
    0xFF, 0xFF, 0xC0, 0xC0, 0xC0, 0xC0, 0xC0, 0xC0, // 8F
    0xFF, 0xFF, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, // 90
    0x00, 0x3C, 0x7E, 0x7E, 0x7E, 0x7E, 0x3C, 0x00, // 91
    0x00, 0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0x00, // 92
    0x36, 0x7F, 0x7F, 0x7F, 0x3E, 0x1C, 0x08, 0x00, // 93
    0x60, 0x60, 0x60, 0x60, 0x60, 0x60, 0x60, 0x60, // 94
    0x00, 0x00, 0x00, 0x07, 0x0F, 0x1C, 0x18, 0x18, // 95
    0xC3, 0xE7, 0x7E, 0x3C, 0x3C, 0x7E, 0xE7, 0xC3, // 96
    0x00, 0x3C, 0x7E, 0x66, 0x66, 0x7E, 0x3C, 0x00, // 97
    0x18, 0x18, 0x66, 0x66, 0x18, 0x18, 0x3C, 0x00, // 98
    0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, // 99
    0x08, 0x1C, 0x3E, 0x7F, 0x3E, 0x1C, 0x08, 0x00, // 9A
    0x18, 0x18, 0x18, 0xFF, 0xFF, 0x18, 0x18, 0x18, // 9B
    0xC0, 0xC0, 0x30, 0x30, 0xC0, 0xC0, 0x30, 0x30, // 9C
    0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, // 9D
    0x00, 0x00, 0x03, 0x3E, 0x76, 0x36, 0x36, 0x00, // 9E
    0xFF, 0x7F, 0x3F, 0x1F, 0x0F, 0x07, 0x03, 0x01, // 9F
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // A0
    0xF0, 0xF0, 0xF0, 0xF0, 0xF0, 0xF0, 0xF0, 0xF0, // A1
    0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0xFF, 0xFF, // A2
    0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // A3
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFF, // A4
    0xC0, 0xC0, 0xC0, 0xC0, 0xC0, 0xC0, 0xC0, 0xC0, // A5
    0xCC, 0xCC, 0x33, 0x33, 0xCC, 0xCC, 0x33, 0x33, // A6
    0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, // A7
    0x00, 0x00, 0x00, 0x00, 0xCC, 0xCC, 0x33, 0x33, // A8
    0xFF, 0xFE, 0xFC, 0xF8, 0xF0, 0xE0, 0xC0, 0x80, // A9
    0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, // AA
    0x18, 0x18, 0x18, 0x1F, 0x1F, 0x18, 0x18, 0x18, // AB
    0x00, 0x00, 0x00, 0x00, 0x0F, 0x0F, 0x0F, 0x0F, // AC
    0x18, 0x18, 0x18, 0x1F, 0x1F, 0x00, 0x00, 0x00, // AD
    0x00, 0x00, 0x00, 0xF8, 0xF8, 0x18, 0x18, 0x18, // AE
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, // AF
    0x00, 0x00, 0x00, 0x1F, 0x1F, 0x18, 0x18, 0x18, // B0
    0x18, 0x18, 0x18, 0xFF, 0xFF, 0x00, 0x00, 0x00, // B1
    0x00, 0x00, 0x00, 0xFF, 0xFF, 0x18, 0x18, 0x18, // B2
    0x18, 0x18, 0x18, 0xF8, 0xF8, 0x18, 0x18, 0x18, // B3
    0xC0, 0xC0, 0xC0, 0xC0, 0xC0, 0xC0, 0xC0, 0xC0, // B4
    0xE0, 0xE0, 0xE0, 0xE0, 0xE0, 0xE0, 0xE0, 0xE0, // B5
    0x07, 0x07, 0x07, 0x07, 0x07, 0x07, 0x07, 0x07, // B6
    0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // B7
    0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, // B8
    0x00, 0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0xFF, // B9
    0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0xFF, 0xFF, // BA
    0x00, 0x00, 0x00, 0x00, 0xF0, 0xF0, 0xF0, 0xF0, // BB
    0x0F, 0x0F, 0x0F, 0x0F, 0x00, 0x00, 0x00, 0x00, // BC
    0x18, 0x18, 0x18, 0xF8, 0xF8, 0x00, 0x00, 0x00, // BD
    0xF0, 0xF0, 0xF0, 0xF0, 0x00, 0x00, 0x00, 0x00, // BE
    0xF0, 0xF0, 0xF0, 0xF0, 0x0F, 0x0F, 0x0F, 0x0F, // BF
    0xFF, 0xFF, 0xFF, 0x00, 0x00, 0xFF, 0xFF, 0xFF, // C0
    0xF7, 0xE3, 0xC1, 0x80, 0x80, 0xE3, 0xC1, 0xFF, // C1
    0xE7, 0xE7, 0xE7, 0xE7, 0xE7, 0xE7, 0xE7, 0xE7, // C2
    0xFF, 0xFF, 0xFF, 0x00, 0x00, 0xFF, 0xFF, 0xFF, // C3
    0xFF, 0xFF, 0x00, 0x00, 0xFF, 0xFF, 0xFF, 0xFF, // C4
    0xFF, 0x00, 0x00, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, // C5
    0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0xFF, 0xFF, // C6
    0xCF, 0xCF, 0xCF, 0xCF, 0xCF, 0xCF, 0xCF, 0xCF, // C7
    0xF3, 0xF3, 0xF3, 0xF3, 0xF3, 0xF3, 0xF3, 0xF3, // C8
    0xFF, 0xFF, 0xFF, 0x1F, 0x0F, 0xC7, 0xE7, 0xE7, // C9
    0xE7, 0xE7, 0xE3, 0xF0, 0xF8, 0xFF, 0xFF, 0xFF, // CA
    0xE7, 0xE7, 0xC7, 0x0F, 0x1F, 0xFF, 0xFF, 0xFF, // CB
    0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x00, 0x00, // CC
    0x3F, 0x1F, 0x8F, 0xC7, 0xE3, 0xF1, 0xF8, 0xFC, // CD
    0xFC, 0xF8, 0xF1, 0xE3, 0xC7, 0x8F, 0x1F, 0x3F, // CE
    0x00, 0x00, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, // CF
    0x00, 0x00, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, // D0
    0xFF, 0xC3, 0x81, 0x81, 0x81, 0x81, 0xC3, 0xFF, // D1
    0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0xFF, // D2
    0xC9, 0x80, 0x80, 0x80, 0xC1, 0xE3, 0xF7, 0xFF, // D3
    0x9F, 0x9F, 0x9F, 0x9F, 0x9F, 0x9F, 0x9F, 0x9F, // D4
    0xFF, 0xFF, 0xFF, 0xF8, 0xF0, 0xE3, 0xE7, 0xE7, // D5
    0x3C, 0x18, 0x81, 0xC3, 0xC3, 0x81, 0x18, 0x3C, // D6
    0xFF, 0xC3, 0x81, 0x99, 0x99, 0x81, 0xC3, 0xFF, // D7
    0xE7, 0xE7, 0x99, 0x99, 0xE7, 0xE7, 0xC3, 0xFF, // D8
    0xF9, 0xF9, 0xF9, 0xF9, 0xF9, 0xF9, 0xF9, 0xF9, // D9
    0xF7, 0xE3, 0xC1, 0x80, 0xC1, 0xE3, 0xF7, 0xFF, // DA
    0xE7, 0xE7, 0xE7, 0x00, 0x00, 0xE7, 0xE7, 0xE7, // DB
    0x3F, 0x3F, 0xCF, 0xCF, 0x3F, 0x3F, 0xCF, 0xCF, // DC
    0xE7, 0xE7, 0xE7, 0xE7, 0xE7, 0xE7, 0xE7, 0xE7, // DD
    0xFF, 0xFF, 0xFC, 0xC1, 0x89, 0xC9, 0xC9, 0xFF, // DE
    0x00, 0x80, 0xC0, 0xE0, 0xF0, 0xF8, 0xFC, 0xFE, // DF
    0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, // E0
    0x0F, 0x0F, 0x0F, 0x0F, 0x0F, 0x0F, 0x0F, 0x0F, // E1
    0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00, // E2
    0x00, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, // E3
    0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x00, // E4
    0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, // E5
    0x33, 0x33, 0xCC, 0xCC, 0x33, 0x33, 0xCC, 0xCC, // E6
    0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, // E7
    0xFF, 0xFF, 0xFF, 0xFF, 0x33, 0x33, 0xCC, 0xCC, // E8
    0x00, 0x01, 0x03, 0x07, 0x0F, 0x1F, 0x3F, 0x7F, // E9
    0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, // EA
    0xE7, 0xE7, 0xE7, 0xE0, 0xE0, 0xE7, 0xE7, 0xE7, // EB
    0xFF, 0xFF, 0xFF, 0xFF, 0xF0, 0xF0, 0xF0, 0xF0, // EC
    0xE7, 0xE7, 0xE7, 0xE0, 0xE0, 0xFF, 0xFF, 0xFF, // ED
    0xFF, 0xFF, 0xFF, 0x07, 0x07, 0xE7, 0xE7, 0xE7, // EE
    0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, // EF
    0xFF, 0xFF, 0xFF, 0xE0, 0xE0, 0xE7, 0xE7, 0xE7, // F0
    0xE7, 0xE7, 0xE7, 0x00, 0x00, 0xFF, 0xFF, 0xFF, // F1
    0xFF, 0xFF, 0xFF, 0x00, 0x00, 0xE7, 0xE7, 0xE7, // F2
    0xE7, 0xE7, 0xE7, 0x07, 0x07, 0xE7, 0xE7, 0xE7, // F3
    0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, // F4
    0x1F, 0x1F, 0x1F, 0x1F, 0x1F, 0x1F, 0x1F, 0x1F, // F5
    0xF8, 0xF8, 0xF8, 0xF8, 0xF8, 0xF8, 0xF8, 0xF8, // F6
    0x00, 0x00, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, // F7
    0x00, 0x00, 0x00, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, // F8
    0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, // F9
    0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0x00, 0x00, // FA
    0xFF, 0xFF, 0xFF, 0xFF, 0x0F, 0x0F, 0x0F, 0x0F, // FB
    0xF0, 0xF0, 0xF0, 0xF0, 0xFF, 0xFF, 0xFF, 0xFF, // FC
    0xE7, 0xE7, 0xE7, 0x07, 0x07, 0xFF, 0xFF, 0xFF, // FD
    0x0F, 0x0F, 0x0F, 0x0F, 0xFF, 0xFF, 0xFF, 0xFF, // FE
    0x0F, 0x0F, 0x0F, 0x0F, 0xF0, 0xF0, 0xF0, 0xF0, // FF
};
static const uint8_t _sdtx_font_oric[2048] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // 00
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // 01
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // 02
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // 03
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // 04
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // 05
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // 06
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // 07
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // 08
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // 09
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // 0A
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // 0B
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // 0C
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // 0D
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // 0E
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // 0F
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // 10
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // 11
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // 12
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // 13
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // 14
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // 15
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // 16
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // 17
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // 18
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // 19
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // 1A
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // 1B
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // 1C
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // 1D
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // 1E
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // 1F
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // 20
    0x08, 0x08, 0x08, 0x08, 0x08, 0x00, 0x08, 0x00, // 21
    0x14, 0x14, 0x14, 0x00, 0x00, 0x00, 0x00, 0x00, // 22
    0x14, 0x14, 0x3E, 0x14, 0x3E, 0x14, 0x14, 0x00, // 23
    0x08, 0x1E, 0x28, 0x1C, 0x0A, 0x3C, 0x08, 0x00, // 24
    0x30, 0x32, 0x04, 0x08, 0x10, 0x26, 0x06, 0x00, // 25
    0x10, 0x28, 0x28, 0x10, 0x2A, 0x24, 0x1A, 0x00, // 26
    0x08, 0x08, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, // 27
    0x08, 0x10, 0x20, 0x20, 0x20, 0x10, 0x08, 0x00, // 28
    0x08, 0x04, 0x02, 0x02, 0x02, 0x04, 0x08, 0x00, // 29
    0x08, 0x2A, 0x1C, 0x08, 0x1C, 0x2A, 0x08, 0x00, // 2A
    0x00, 0x08, 0x08, 0x3E, 0x08, 0x08, 0x00, 0x00, // 2B
    0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x08, 0x10, // 2C
    0x00, 0x00, 0x00, 0x3E, 0x00, 0x00, 0x00, 0x00, // 2D
    0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x00, 0x00, // 2E
    0x00, 0x02, 0x04, 0x08, 0x10, 0x20, 0x00, 0x00, // 2F
    0x1C, 0x22, 0x26, 0x2A, 0x32, 0x22, 0x1C, 0x00, // 30
    0x08, 0x18, 0x08, 0x08, 0x08, 0x08, 0x1C, 0x00, // 31
    0x1C, 0x22, 0x02, 0x04, 0x08, 0x10, 0x3E, 0x00, // 32
    0x3E, 0x02, 0x04, 0x0C, 0x02, 0x22, 0x1C, 0x00, // 33
    0x04, 0x0C, 0x14, 0x24, 0x3E, 0x04, 0x04, 0x00, // 34
    0x3E, 0x20, 0x3C, 0x02, 0x02, 0x22, 0x1C, 0x00, // 35
    0x0C, 0x10, 0x20, 0x3C, 0x22, 0x22, 0x1C, 0x00, // 36
    0x3E, 0x02, 0x04, 0x08, 0x10, 0x10, 0x10, 0x00, // 37
    0x1C, 0x22, 0x22, 0x1C, 0x22, 0x22, 0x1C, 0x00, // 38
    0x1C, 0x22, 0x22, 0x1E, 0x02, 0x04, 0x18, 0x00, // 39
    0x00, 0x00, 0x08, 0x00, 0x00, 0x08, 0x00, 0x00, // 3A
    0x00, 0x00, 0x08, 0x00, 0x00, 0x08, 0x08, 0x10, // 3B
    0x04, 0x08, 0x10, 0x20, 0x10, 0x08, 0x04, 0x00, // 3C
    0x00, 0x00, 0x3E, 0x00, 0x3E, 0x00, 0x00, 0x00, // 3D
    0x10, 0x08, 0x04, 0x02, 0x04, 0x08, 0x10, 0x00, // 3E
    0x1C, 0x22, 0x04, 0x08, 0x08, 0x00, 0x08, 0x00, // 3F
    0x1C, 0x22, 0x2A, 0x2E, 0x2C, 0x20, 0x1E, 0x00, // 40
    0x08, 0x14, 0x22, 0x22, 0x3E, 0x22, 0x22, 0x00, // 41
    0x3C, 0x22, 0x22, 0x3C, 0x22, 0x22, 0x3C, 0x00, // 42
    0x1C, 0x22, 0x20, 0x20, 0x20, 0x22, 0x1C, 0x00, // 43
    0x3C, 0x22, 0x22, 0x22, 0x22, 0x22, 0x3C, 0x00, // 44
    0x3E, 0x20, 0x20, 0x3C, 0x20, 0x20, 0x3E, 0x00, // 45
    0x3E, 0x20, 0x20, 0x3C, 0x20, 0x20, 0x20, 0x00, // 46
    0x1E, 0x20, 0x20, 0x20, 0x26, 0x22, 0x1E, 0x00, // 47
    0x22, 0x22, 0x22, 0x3E, 0x22, 0x22, 0x22, 0x00, // 48
    0x1C, 0x08, 0x08, 0x08, 0x08, 0x08, 0x1C, 0x00, // 49
    0x02, 0x02, 0x02, 0x02, 0x02, 0x22, 0x1C, 0x00, // 4A
    0x22, 0x24, 0x28, 0x30, 0x28, 0x24, 0x22, 0x00, // 4B
    0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x3E, 0x00, // 4C
    0x22, 0x36, 0x2A, 0x2A, 0x22, 0x22, 0x22, 0x00, // 4D
    0x22, 0x22, 0x32, 0x2A, 0x26, 0x22, 0x22, 0x00, // 4E
    0x1C, 0x22, 0x22, 0x22, 0x22, 0x22, 0x1C, 0x00, // 4F
    0x3C, 0x22, 0x22, 0x3C, 0x20, 0x20, 0x20, 0x00, // 50
    0x1C, 0x22, 0x22, 0x22, 0x2A, 0x24, 0x1A, 0x00, // 51
    0x3C, 0x22, 0x22, 0x3C, 0x28, 0x24, 0x22, 0x00, // 52
    0x1C, 0x22, 0x20, 0x1C, 0x02, 0x22, 0x1C, 0x00, // 53
    0x3E, 0x08, 0x08, 0x08, 0x08, 0x08, 0x08, 0x00, // 54
    0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x1C, 0x00, // 55
    0x22, 0x22, 0x22, 0x22, 0x22, 0x14, 0x08, 0x00, // 56
    0x22, 0x22, 0x22, 0x2A, 0x2A, 0x36, 0x22, 0x00, // 57
    0x22, 0x22, 0x14, 0x08, 0x14, 0x22, 0x22, 0x00, // 58
    0x22, 0x22, 0x14, 0x08, 0x08, 0x08, 0x08, 0x00, // 59
    0x3E, 0x02, 0x04, 0x08, 0x10, 0x20, 0x3E, 0x00, // 5A
    0x1E, 0x10, 0x10, 0x10, 0x10, 0x10, 0x1E, 0x00, // 5B
    0x00, 0x20, 0x10, 0x08, 0x04, 0x02, 0x00, 0x00, // 5C
    0x3C, 0x04, 0x04, 0x04, 0x04, 0x04, 0x3C, 0x00, // 5D
    0x08, 0x14, 0x2A, 0x08, 0x08, 0x08, 0x08, 0x00, // 5E
    0x0E, 0x11, 0x3C, 0x10, 0x3C, 0x11, 0x0E, 0x00, // 5F
    0x0C, 0x12, 0x2D, 0x29, 0x29, 0x2D, 0x12, 0x0C, // 60
    0x00, 0x00, 0x1C, 0x02, 0x1E, 0x22, 0x1E, 0x00, // 61
    0x20, 0x20, 0x3C, 0x22, 0x22, 0x22, 0x3C, 0x00, // 62
    0x00, 0x00, 0x1E, 0x20, 0x20, 0x20, 0x1E, 0x00, // 63
    0x02, 0x02, 0x1E, 0x22, 0x22, 0x22, 0x1E, 0x00, // 64
    0x00, 0x00, 0x1C, 0x22, 0x3E, 0x20, 0x1E, 0x00, // 65
    0x0C, 0x12, 0x10, 0x3C, 0x10, 0x10, 0x10, 0x00, // 66
    0x00, 0x00, 0x1C, 0x22, 0x22, 0x1E, 0x02, 0x1C, // 67
    0x20, 0x20, 0x3C, 0x22, 0x22, 0x22, 0x22, 0x00, // 68
    0x08, 0x00, 0x18, 0x08, 0x08, 0x08, 0x1C, 0x00, // 69
    0x04, 0x00, 0x0C, 0x04, 0x04, 0x04, 0x24, 0x18, // 6A
    0x20, 0x20, 0x22, 0x24, 0x38, 0x24, 0x22, 0x00, // 6B
    0x18, 0x08, 0x08, 0x08, 0x08, 0x08, 0x1C, 0x00, // 6C
    0x00, 0x00, 0x36, 0x2A, 0x2A, 0x2A, 0x22, 0x00, // 6D
    0x00, 0x00, 0x3C, 0x22, 0x22, 0x22, 0x22, 0x00, // 6E
    0x00, 0x00, 0x1C, 0x22, 0x22, 0x22, 0x1C, 0x00, // 6F
    0x00, 0x00, 0x3C, 0x22, 0x22, 0x3C, 0x20, 0x20, // 70
    0x00, 0x00, 0x1E, 0x22, 0x22, 0x1E, 0x02, 0x02, // 71
    0x00, 0x00, 0x2E, 0x30, 0x20, 0x20, 0x20, 0x00, // 72
    0x00, 0x00, 0x1E, 0x20, 0x1C, 0x02, 0x3C, 0x00, // 73
    0x10, 0x10, 0x3C, 0x10, 0x10, 0x12, 0x0C, 0x00, // 74
    0x00, 0x00, 0x22, 0x22, 0x22, 0x26, 0x1A, 0x00, // 75
    0x00, 0x00, 0x22, 0x22, 0x22, 0x14, 0x08, 0x00, // 76
    0x00, 0x00, 0x22, 0x22, 0x2A, 0x2A, 0x36, 0x00, // 77
    0x00, 0x00, 0x22, 0x14, 0x08, 0x14, 0x22, 0x00, // 78
    0x00, 0x00, 0x22, 0x22, 0x22, 0x1E, 0x02, 0x1C, // 79
    0x00, 0x00, 0x3E, 0x04, 0x08, 0x10, 0x3E, 0x00, // 7A
    0x0E, 0x18, 0x18, 0x30, 0x18, 0x18, 0x0E, 0x00, // 7B
    0x08, 0x08, 0x08, 0x08, 0x08, 0x08, 0x08, 0x08, // 7C
    0x38, 0x0C, 0x0C, 0x06, 0x0C, 0x0C, 0x38, 0x00, // 7D
    0x2A, 0x15, 0x2A, 0x15, 0x2A, 0x15, 0x2A, 0x15, // 7E
    0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, 0x3F, // 7F
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // 80
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // 81
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // 82
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // 83
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // 84
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // 85
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // 86
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // 87
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // 88
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // 89
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // 8A
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // 8B
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // 8C
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // 8D
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // 8E
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // 8F
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // 90
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // 91
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // 92
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // 93
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // 94
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // 95
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // 96
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // 97
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // 98
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // 99
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // 9A
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // 9B
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // 9C
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // 9D
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // 9E
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // 9F
    0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, // A0
    0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xFF, 0xF7, 0xFF, // A1
    0xEB, 0xEB, 0xEB, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, // A2
    0xEB, 0xEB, 0xC1, 0xEB, 0xC1, 0xEB, 0xEB, 0xFF, // A3
    0xF7, 0xE1, 0xD7, 0xE3, 0xF5, 0xC3, 0xF7, 0xFF, // A4
    0xCF, 0xCD, 0xFB, 0xF7, 0xEF, 0xD9, 0xF9, 0xFF, // A5
    0xEF, 0xD7, 0xD7, 0xEF, 0xD5, 0xDB, 0xE5, 0xFF, // A6
    0xF7, 0xF7, 0xF7, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, // A7
    0xF7, 0xEF, 0xDF, 0xDF, 0xDF, 0xEF, 0xF7, 0xFF, // A8
    0xF7, 0xFB, 0xFD, 0xFD, 0xFD, 0xFB, 0xF7, 0xFF, // A9
    0xF7, 0xD5, 0xE3, 0xF7, 0xE3, 0xD5, 0xF7, 0xFF, // AA
    0xFF, 0xF7, 0xF7, 0xC1, 0xF7, 0xF7, 0xFF, 0xFF, // AB
    0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF7, 0xF7, 0xEF, // AC
    0xFF, 0xFF, 0xFF, 0xC1, 0xFF, 0xFF, 0xFF, 0xFF, // AD
    0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFB, 0xFF, 0xFF, // AE
    0xFF, 0xFD, 0xFB, 0xF7, 0xEF, 0xDF, 0xFF, 0xFF, // AF
    0xE3, 0xDD, 0xD9, 0xD5, 0xCD, 0xDD, 0xE3, 0xFF, // B0
    0xF7, 0xE7, 0xF7, 0xF7, 0xF7, 0xF7, 0xE3, 0xFF, // B1
    0xE3, 0xDD, 0xFD, 0xFB, 0xF7, 0xEF, 0xC1, 0xFF, // B2
    0xC1, 0xFD, 0xFB, 0xF3, 0xFD, 0xDD, 0xE3, 0xFF, // B3
    0xFB, 0xF3, 0xEB, 0xDB, 0xC1, 0xFB, 0xFB, 0xFF, // B4
    0xC1, 0xDF, 0xC3, 0xFD, 0xFD, 0xDD, 0xE3, 0xFF, // B5
    0xF3, 0xEF, 0xDF, 0xC3, 0xDD, 0xDD, 0xE3, 0xFF, // B6
    0xC1, 0xFD, 0xFB, 0xF7, 0xEF, 0xEF, 0xEF, 0xFF, // B7
    0xE3, 0xDD, 0xDD, 0xE3, 0xDD, 0xDD, 0xE3, 0xFF, // B8
    0xE3, 0xDD, 0xDD, 0xE1, 0xFD, 0xFB, 0xE7, 0xFF, // B9
    0xFF, 0xFF, 0xF7, 0xFF, 0xFF, 0xF7, 0xFF, 0xFF, // BA
    0xFF, 0xFF, 0xF7, 0xFF, 0xFF, 0xF7, 0xF7, 0xEF, // BB
    0xFB, 0xF7, 0xEF, 0xDF, 0xEF, 0xF7, 0xFB, 0xFF, // BC
    0xFF, 0xFF, 0xC1, 0xFF, 0xC1, 0xFF, 0xFF, 0xFF, // BD
    0xEF, 0xF7, 0xFB, 0xFD, 0xFB, 0xF7, 0xEF, 0xFF, // BE
    0xE3, 0xDD, 0xFB, 0xF7, 0xF7, 0xFF, 0xF7, 0xFF, // BF
    0xE3, 0xDD, 0xD5, 0xD1, 0xD3, 0xDF, 0xE1, 0xFF, // C0
    0xF7, 0xEB, 0xDD, 0xDD, 0xC1, 0xDD, 0xDD, 0xFF, // C1
    0xC3, 0xDD, 0xDD, 0xC3, 0xDD, 0xDD, 0xC3, 0xFF, // C2
    0xE3, 0xDD, 0xDF, 0xDF, 0xDF, 0xDD, 0xE3, 0xFF, // C3
    0xC3, 0xDD, 0xDD, 0xDD, 0xDD, 0xDD, 0xC3, 0xFF, // C4
    0xC1, 0xDF, 0xDF, 0xC3, 0xDF, 0xDF, 0xC1, 0xFF, // C5
    0xC1, 0xDF, 0xDF, 0xC3, 0xDF, 0xDF, 0xDF, 0xFF, // C6
    0xE1, 0xDF, 0xDF, 0xDF, 0xD9, 0xDD, 0xE1, 0xFF, // C7
    0xDD, 0xDD, 0xDD, 0xC1, 0xDD, 0xDD, 0xDD, 0xFF, // C8
    0xE3, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xE3, 0xFF, // C9
    0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xDD, 0xE3, 0xFF, // CA
    0xDD, 0xDB, 0xD7, 0xCF, 0xD7, 0xDB, 0xDD, 0xFF, // CB
    0xDF, 0xDF, 0xDF, 0xDF, 0xDF, 0xDF, 0xC1, 0xFF, // CC
    0xDD, 0xC9, 0xD5, 0xD5, 0xDD, 0xDD, 0xDD, 0xFF, // CD
    0xDD, 0xDD, 0xCD, 0xD5, 0xD9, 0xDD, 0xDD, 0xFF, // CE
    0xE3, 0xDD, 0xDD, 0xDD, 0xDD, 0xDD, 0xE3, 0xFF, // CF
    0xC3, 0xDD, 0xDD, 0xC3, 0xDF, 0xDF, 0xDF, 0xFF, // D0
    0xE3, 0xDD, 0xDD, 0xDD, 0xD5, 0xDB, 0xE5, 0xFF, // D1
    0xC3, 0xDD, 0xDD, 0xC3, 0xD7, 0xDB, 0xDD, 0xFF, // D2
    0xE3, 0xDD, 0xDF, 0xE3, 0xFD, 0xDD, 0xE3, 0xFF, // D3
    0xC1, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xFF, // D4
    0xDD, 0xDD, 0xDD, 0xDD, 0xDD, 0xDD, 0xE3, 0xFF, // D5
    0xDD, 0xDD, 0xDD, 0xDD, 0xDD, 0xEB, 0xF7, 0xFF, // D6
    0xDD, 0xDD, 0xDD, 0xD5, 0xD5, 0xC9, 0xDD, 0xFF, // D7
    0xDD, 0xDD, 0xEB, 0xF7, 0xEB, 0xDD, 0xDD, 0xFF, // D8
    0xDD, 0xDD, 0xEB, 0xF7, 0xF7, 0xF7, 0xF7, 0xFF, // D9
    0xC1, 0xFD, 0xFB, 0xF7, 0xEF, 0xDF, 0xC1, 0xFF, // DA
    0xE1, 0xEF, 0xEF, 0xEF, 0xEF, 0xEF, 0xE1, 0xFF, // DB
    0xFF, 0xDF, 0xEF, 0xF7, 0xFB, 0xFD, 0xFF, 0xFF, // DC
    0xC3, 0xFB, 0xFB, 0xFB, 0xFB, 0xFB, 0xC3, 0xFF, // DD
    0xF7, 0xEB, 0xD5, 0xF7, 0xF7, 0xF7, 0xF7, 0xFF, // DE
    0xF1, 0xEE, 0xC3, 0xEF, 0xC3, 0xEE, 0xF1, 0xFF, // DF
    0xF3, 0xED, 0xD2, 0xD6, 0xD6, 0xD2, 0xED, 0xF3, // E0
    0xFF, 0xFF, 0xE3, 0xFD, 0xE1, 0xDD, 0xE1, 0xFF, // E1
    0xDF, 0xDF, 0xC3, 0xDD, 0xDD, 0xDD, 0xC3, 0xFF, // E2
    0xFF, 0xFF, 0xE1, 0xDF, 0xDF, 0xDF, 0xE1, 0xFF, // E3
    0xFD, 0xFD, 0xE1, 0xDD, 0xDD, 0xDD, 0xE1, 0xFF, // E4
    0xFF, 0xFF, 0xE3, 0xDD, 0xC1, 0xDF, 0xE1, 0xFF, // E5
    0xF3, 0xED, 0xEF, 0xC3, 0xEF, 0xEF, 0xEF, 0xFF, // E6
    0xFF, 0xFF, 0xE3, 0xDD, 0xDD, 0xE1, 0xFD, 0xE3, // E7
    0xDF, 0xDF, 0xC3, 0xDD, 0xDD, 0xDD, 0xDD, 0xFF, // E8
    0xF7, 0xFF, 0xE7, 0xF7, 0xF7, 0xF7, 0xE3, 0xFF, // E9
    0xFB, 0xFF, 0xF3, 0xFB, 0xFB, 0xFB, 0xDB, 0xE7, // EA
    0xDF, 0xDF, 0xDD, 0xDB, 0xC7, 0xDB, 0xDD, 0xFF, // EB
    0xE7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xE3, 0xFF, // EC
    0xFF, 0xFF, 0xC9, 0xD5, 0xD5, 0xD5, 0xDD, 0xFF, // ED
    0xFF, 0xFF, 0xC3, 0xDD, 0xDD, 0xDD, 0xDD, 0xFF, // EE
    0xFF, 0xFF, 0xE3, 0xDD, 0xDD, 0xDD, 0xE3, 0xFF, // EF
    0xFF, 0xFF, 0xC3, 0xDD, 0xDD, 0xC3, 0xDF, 0xDF, // F0
    0xFF, 0xFF, 0xE1, 0xDD, 0xDD, 0xE1, 0xFD, 0xFD, // F1
    0xFF, 0xFF, 0xD1, 0xCF, 0xDF, 0xDF, 0xDF, 0xFF, // F2
    0xFF, 0xFF, 0xE1, 0xDF, 0xE3, 0xFD, 0xC3, 0xFF, // F3
    0xEF, 0xEF, 0xC3, 0xEF, 0xEF, 0xED, 0xF3, 0xFF, // F4
    0xFF, 0xFF, 0xDD, 0xDD, 0xDD, 0xD9, 0xE5, 0xFF, // F5
    0xFF, 0xFF, 0xDD, 0xDD, 0xDD, 0xEB, 0xF7, 0xFF, // F6
    0xFF, 0xFF, 0xDD, 0xDD, 0xD5, 0xD5, 0xC9, 0xFF, // F7
    0xFF, 0xFF, 0xDD, 0xEB, 0xF7, 0xEB, 0xDD, 0xFF, // F8
    0xFF, 0xFF, 0xDD, 0xDD, 0xDD, 0xE1, 0xFD, 0xE3, // F9
    0xFF, 0xFF, 0xC1, 0xFB, 0xF7, 0xEF, 0xC1, 0xFF, // FA
    0xF1, 0xE7, 0xE7, 0xCF, 0xE7, 0xE7, 0xF1, 0xFF, // FB
    0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, // FC
    0xC7, 0xF3, 0xF3, 0xF9, 0xF3, 0xF3, 0xC7, 0xFF, // FD
    0xD5, 0xEA, 0xD5, 0xEA, 0xD5, 0xEA, 0xD5, 0xEA, // FE
    0xC0, 0xC0, 0xC0, 0xC0, 0xC0, 0xC0, 0xC0, 0xC0, // FF
};

/*
    Embedded source code compiled with:

    sokol-shdc -i debugtext.glsl -o debugtext.h -l glsl410:glsl300es:hlsl4:metal_macos:metal_ios:metal_sim:wgsl -b

    (not that for Metal and D3D11 byte code, sokol-shdc must be run
    on macOS and Windows)

        @vs vs
        in vec2 position;
        in vec2 texcoord0;
        in vec4 color0;
        out vec2 uv;
        out vec4 color;
        void main() {
          gl_Position = vec4(position * vec2(2.0, -2.0) + vec2(-1.0, +1.0), 0.0, 1.0);
          uv = texcoord0;
          color = color0;
        }
        @end

        @fs fs
        layout(binding=0) uniform texture2D tex;
        layout(binding=0) uniform sampler smp;
        in vec2 uv;
        in vec4 color;
        out vec4 frag_color;
        void main() {
          frag_color = texture(sampler2D(tex, smp), uv).xxxx * color;
        }
        @end

        @program debugtext vs fs
*/
#if defined(SOKOL_GLCORE)
/*
    #version 410

    layout(location = 0) in vec2 position;
    layout(location = 0) out vec2 uv;
    layout(location = 1) in vec2 texcoord0;
    layout(location = 1) out vec4 color;
    layout(location = 2) in vec4 color0;

    void main()
    {
        gl_Position = vec4(fma(position, vec2(2.0, -2.0), vec2(-1.0, 1.0)), 0.0, 1.0);
        uv = texcoord0;
        color = color0;
    }
*/
static const uint8_t _sdtx_vs_source_glsl410[343] = {
    0x23,0x76,0x65,0x72,0x73,0x69,0x6f,0x6e,0x20,0x34,0x31,0x30,0x0a,0x0a,0x6c,0x61,
    0x79,0x6f,0x75,0x74,0x28,0x6c,0x6f,0x63,0x61,0x74,0x69,0x6f,0x6e,0x20,0x3d,0x20,
    0x30,0x29,0x20,0x69,0x6e,0x20,0x76,0x65,0x63,0x32,0x20,0x70,0x6f,0x73,0x69,0x74,
    0x69,0x6f,0x6e,0x3b,0x0a,0x6c,0x61,0x79,0x6f,0x75,0x74,0x28,0x6c,0x6f,0x63,0x61,
    0x74,0x69,0x6f,0x6e,0x20,0x3d,0x20,0x30,0x29,0x20,0x6f,0x75,0x74,0x20,0x76,0x65,
    0x63,0x32,0x20,0x75,0x76,0x3b,0x0a,0x6c,0x61,0x79,0x6f,0x75,0x74,0x28,0x6c,0x6f,
    0x63,0x61,0x74,0x69,0x6f,0x6e,0x20,0x3d,0x20,0x31,0x29,0x20,0x69,0x6e,0x20,0x76,
    0x65,0x63,0x32,0x20,0x74,0x65,0x78,0x63,0x6f,0x6f,0x72,0x64,0x30,0x3b,0x0a,0x6c,
    0x61,0x79,0x6f,0x75,0x74,0x28,0x6c,0x6f,0x63,0x61,0x74,0x69,0x6f,0x6e,0x20,0x3d,
    0x20,0x31,0x29,0x20,0x6f,0x75,0x74,0x20,0x76,0x65,0x63,0x34,0x20,0x63,0x6f,0x6c,
    0x6f,0x72,0x3b,0x0a,0x6c,0x61,0x79,0x6f,0x75,0x74,0x28,0x6c,0x6f,0x63,0x61,0x74,
    0x69,0x6f,0x6e,0x20,0x3d,0x20,0x32,0x29,0x20,0x69,0x6e,0x20,0x76,0x65,0x63,0x34,
    0x20,0x63,0x6f,0x6c,0x6f,0x72,0x30,0x3b,0x0a,0x0a,0x76,0x6f,0x69,0x64,0x20,0x6d,
    0x61,0x69,0x6e,0x28,0x29,0x0a,0x7b,0x0a,0x20,0x20,0x20,0x20,0x67,0x6c,0x5f,0x50,
    0x6f,0x73,0x69,0x74,0x69,0x6f,0x6e,0x20,0x3d,0x20,0x76,0x65,0x63,0x34,0x28,0x66,
    0x6d,0x61,0x28,0x70,0x6f,0x73,0x69,0x74,0x69,0x6f,0x6e,0x2c,0x20,0x76,0x65,0x63,
    0x32,0x28,0x32,0x2e,0x30,0x2c,0x20,0x2d,0x32,0x2e,0x30,0x29,0x2c,0x20,0x76,0x65,
    0x63,0x32,0x28,0x2d,0x31,0x2e,0x30,0x2c,0x20,0x31,0x2e,0x30,0x29,0x29,0x2c,0x20,
    0x30,0x2e,0x30,0x2c,0x20,0x31,0x2e,0x30,0x29,0x3b,0x0a,0x20,0x20,0x20,0x20,0x75,
    0x76,0x20,0x3d,0x20,0x74,0x65,0x78,0x63,0x6f,0x6f,0x72,0x64,0x30,0x3b,0x0a,0x20,
    0x20,0x20,0x20,0x63,0x6f,0x6c,0x6f,0x72,0x20,0x3d,0x20,0x63,0x6f,0x6c,0x6f,0x72,
    0x30,0x3b,0x0a,0x7d,0x0a,0x0a,0x00,
};
/*
    #version 410

    uniform sampler2D tex_smp;

    layout(location = 0) out vec4 frag_color;
    layout(location = 0) in vec2 uv;
    layout(location = 1) in vec4 color;

    void main()
    {
        frag_color = texture(tex_smp, uv).xxxx * color;
    }
*/
static const uint8_t _sdtx_fs_source_glsl410[224] = {
    0x23,0x76,0x65,0x72,0x73,0x69,0x6f,0x6e,0x20,0x34,0x31,0x30,0x0a,0x0a,0x75,0x6e,
    0x69,0x66,0x6f,0x72,0x6d,0x20,0x73,0x61,0x6d,0x70,0x6c,0x65,0x72,0x32,0x44,0x20,
    0x74,0x65,0x78,0x5f,0x73,0x6d,0x70,0x3b,0x0a,0x0a,0x6c,0x61,0x79,0x6f,0x75,0x74,
    0x28,0x6c,0x6f,0x63,0x61,0x74,0x69,0x6f,0x6e,0x20,0x3d,0x20,0x30,0x29,0x20,0x6f,
    0x75,0x74,0x20,0x76,0x65,0x63,0x34,0x20,0x66,0x72,0x61,0x67,0x5f,0x63,0x6f,0x6c,
    0x6f,0x72,0x3b,0x0a,0x6c,0x61,0x79,0x6f,0x75,0x74,0x28,0x6c,0x6f,0x63,0x61,0x74,
    0x69,0x6f,0x6e,0x20,0x3d,0x20,0x30,0x29,0x20,0x69,0x6e,0x20,0x76,0x65,0x63,0x32,
    0x20,0x75,0x76,0x3b,0x0a,0x6c,0x61,0x79,0x6f,0x75,0x74,0x28,0x6c,0x6f,0x63,0x61,
    0x74,0x69,0x6f,0x6e,0x20,0x3d,0x20,0x31,0x29,0x20,0x69,0x6e,0x20,0x76,0x65,0x63,
    0x34,0x20,0x63,0x6f,0x6c,0x6f,0x72,0x3b,0x0a,0x0a,0x76,0x6f,0x69,0x64,0x20,0x6d,
    0x61,0x69,0x6e,0x28,0x29,0x0a,0x7b,0x0a,0x20,0x20,0x20,0x20,0x66,0x72,0x61,0x67,
    0x5f,0x63,0x6f,0x6c,0x6f,0x72,0x20,0x3d,0x20,0x74,0x65,0x78,0x74,0x75,0x72,0x65,
    0x28,0x74,0x65,0x78,0x5f,0x73,0x6d,0x70,0x2c,0x20,0x75,0x76,0x29,0x2e,0x78,0x78,
    0x78,0x78,0x20,0x2a,0x20,0x63,0x6f,0x6c,0x6f,0x72,0x3b,0x0a,0x7d,0x0a,0x0a,0x00,

};
#elif defined(SOKOL_GLES3)
/*
    #version 300 es

    layout(location = 0) in vec2 position;
    out vec2 uv;
    layout(location = 1) in vec2 texcoord0;
    out vec4 color;
    layout(location = 2) in vec4 color0;

    void main()
    {
        gl_Position = vec4(position * vec2(2.0, -2.0) + vec2(-1.0, 1.0), 0.0, 1.0);
        uv = texcoord0;
        color = color0;
    }
*/
static const uint8_t _sdtx_vs_source_glsl300es[301] = {
    0x23,0x76,0x65,0x72,0x73,0x69,0x6f,0x6e,0x20,0x33,0x30,0x30,0x20,0x65,0x73,0x0a,
    0x0a,0x6c,0x61,0x79,0x6f,0x75,0x74,0x28,0x6c,0x6f,0x63,0x61,0x74,0x69,0x6f,0x6e,
    0x20,0x3d,0x20,0x30,0x29,0x20,0x69,0x6e,0x20,0x76,0x65,0x63,0x32,0x20,0x70,0x6f,
    0x73,0x69,0x74,0x69,0x6f,0x6e,0x3b,0x0a,0x6f,0x75,0x74,0x20,0x76,0x65,0x63,0x32,
    0x20,0x75,0x76,0x3b,0x0a,0x6c,0x61,0x79,0x6f,0x75,0x74,0x28,0x6c,0x6f,0x63,0x61,
    0x74,0x69,0x6f,0x6e,0x20,0x3d,0x20,0x31,0x29,0x20,0x69,0x6e,0x20,0x76,0x65,0x63,
    0x32,0x20,0x74,0x65,0x78,0x63,0x6f,0x6f,0x72,0x64,0x30,0x3b,0x0a,0x6f,0x75,0x74,
    0x20,0x76,0x65,0x63,0x34,0x20,0x63,0x6f,0x6c,0x6f,0x72,0x3b,0x0a,0x6c,0x61,0x79,
    0x6f,0x75,0x74,0x28,0x6c,0x6f,0x63,0x61,0x74,0x69,0x6f,0x6e,0x20,0x3d,0x20,0x32,
    0x29,0x20,0x69,0x6e,0x20,0x76,0x65,0x63,0x34,0x20,0x63,0x6f,0x6c,0x6f,0x72,0x30,
    0x3b,0x0a,0x0a,0x76,0x6f,0x69,0x64,0x20,0x6d,0x61,0x69,0x6e,0x28,0x29,0x0a,0x7b,
    0x0a,0x20,0x20,0x20,0x20,0x67,0x6c,0x5f,0x50,0x6f,0x73,0x69,0x74,0x69,0x6f,0x6e,
    0x20,0x3d,0x20,0x76,0x65,0x63,0x34,0x28,0x70,0x6f,0x73,0x69,0x74,0x69,0x6f,0x6e,
    0x20,0x2a,0x20,0x76,0x65,0x63,0x32,0x28,0x32,0x2e,0x30,0x2c,0x20,0x2d,0x32,0x2e,
    0x30,0x29,0x20,0x2b,0x20,0x76,0x65,0x63,0x32,0x28,0x2d,0x31,0x2e,0x30,0x2c,0x20,
    0x31,0x2e,0x30,0x29,0x2c,0x20,0x30,0x2e,0x30,0x2c,0x20,0x31,0x2e,0x30,0x29,0x3b,
    0x0a,0x20,0x20,0x20,0x20,0x75,0x76,0x20,0x3d,0x20,0x74,0x65,0x78,0x63,0x6f,0x6f,
    0x72,0x64,0x30,0x3b,0x0a,0x20,0x20,0x20,0x20,0x63,0x6f,0x6c,0x6f,0x72,0x20,0x3d,
    0x20,0x63,0x6f,0x6c,0x6f,0x72,0x30,0x3b,0x0a,0x7d,0x0a,0x0a,0x00,
};
/*
    #version 300 es
    precision mediump float;
    precision highp int;

    uniform highp sampler2D tex_smp;

    layout(location = 0) out highp vec4 frag_color;
    in highp vec2 uv;
    in highp vec4 color;

    void main()
    {
        frag_color = texture(tex_smp, uv).xxxx * color;
    }
*/
static const uint8_t _sdtx_fs_source_glsl300es[255] = {
    0x23,0x76,0x65,0x72,0x73,0x69,0x6f,0x6e,0x20,0x33,0x30,0x30,0x20,0x65,0x73,0x0a,
    0x70,0x72,0x65,0x63,0x69,0x73,0x69,0x6f,0x6e,0x20,0x6d,0x65,0x64,0x69,0x75,0x6d,
    0x70,0x20,0x66,0x6c,0x6f,0x61,0x74,0x3b,0x0a,0x70,0x72,0x65,0x63,0x69,0x73,0x69,
    0x6f,0x6e,0x20,0x68,0x69,0x67,0x68,0x70,0x20,0x69,0x6e,0x74,0x3b,0x0a,0x0a,0x75,
    0x6e,0x69,0x66,0x6f,0x72,0x6d,0x20,0x68,0x69,0x67,0x68,0x70,0x20,0x73,0x61,0x6d,
    0x70,0x6c,0x65,0x72,0x32,0x44,0x20,0x74,0x65,0x78,0x5f,0x73,0x6d,0x70,0x3b,0x0a,
    0x0a,0x6c,0x61,0x79,0x6f,0x75,0x74,0x28,0x6c,0x6f,0x63,0x61,0x74,0x69,0x6f,0x6e,
    0x20,0x3d,0x20,0x30,0x29,0x20,0x6f,0x75,0x74,0x20,0x68,0x69,0x67,0x68,0x70,0x20,
    0x76,0x65,0x63,0x34,0x20,0x66,0x72,0x61,0x67,0x5f,0x63,0x6f,0x6c,0x6f,0x72,0x3b,
    0x0a,0x69,0x6e,0x20,0x68,0x69,0x67,0x68,0x70,0x20,0x76,0x65,0x63,0x32,0x20,0x75,
    0x76,0x3b,0x0a,0x69,0x6e,0x20,0x68,0x69,0x67,0x68,0x70,0x20,0x76,0x65,0x63,0x34,
    0x20,0x63,0x6f,0x6c,0x6f,0x72,0x3b,0x0a,0x0a,0x76,0x6f,0x69,0x64,0x20,0x6d,0x61,
    0x69,0x6e,0x28,0x29,0x0a,0x7b,0x0a,0x20,0x20,0x20,0x20,0x66,0x72,0x61,0x67,0x5f,
    0x63,0x6f,0x6c,0x6f,0x72,0x20,0x3d,0x20,0x74,0x65,0x78,0x74,0x75,0x72,0x65,0x28,
    0x74,0x65,0x78,0x5f,0x73,0x6d,0x70,0x2c,0x20,0x75,0x76,0x29,0x2e,0x78,0x78,0x78,
    0x78,0x20,0x2a,0x20,0x63,0x6f,0x6c,0x6f,0x72,0x3b,0x0a,0x7d,0x0a,0x0a,0x00,
};
#elif defined(SOKOL_METAL)
/*
    #include <metal_stdlib>
    #include <simd/simd.h>

    using namespace metal;

    struct main0_out
    {
        float2 uv [[user(locn0)]];
        float4 color [[user(locn1)]];
        float4 gl_Position [[position]];
    };

    struct main0_in
    {
        float2 position [[attribute(0)]];
        float2 texcoord0 [[attribute(1)]];
        float4 color0 [[attribute(2)]];
    };

    vertex main0_out main0(main0_in in [[stage_in]])
    {
        main0_out out = {};
        out.gl_Position = float4(fma(in.position, float2(2.0, -2.0), float2(-1.0, 1.0)), 0.0, 1.0);
        out.uv = in.texcoord0;
        out.color = in.color0;
        return out;
    }
*/
static const uint8_t _sdtx_vs_bytecode_metal_macos[2892] = {
    0x4d,0x54,0x4c,0x42,0x01,0x80,0x02,0x00,0x02,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x4c,0x0b,0x00,0x00,0x00,0x00,0x00,0x00,0x58,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x6d,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xc9,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x3b,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x04,0x01,0x00,0x00,0x00,0x00,0x00,0x00,
    0x08,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0c,0x01,0x00,0x00,0x00,0x00,0x00,0x00,
    0x40,0x0a,0x00,0x00,0x00,0x00,0x00,0x00,0x01,0x00,0x00,0x00,0x6d,0x00,0x00,0x00,
    0x4e,0x41,0x4d,0x45,0x06,0x00,0x6d,0x61,0x69,0x6e,0x30,0x00,0x54,0x59,0x50,0x45,
    0x01,0x00,0x00,0x48,0x41,0x53,0x48,0x20,0x00,0x54,0x27,0xd2,0x9c,0x99,0x28,0x3d,
    0x28,0xcf,0x09,0x1c,0x55,0xac,0x1d,0xfa,0x23,0x34,0x9b,0xc3,0x82,0x58,0xe9,0xeb,
    0xdc,0x0c,0xa5,0xd3,0x0c,0xa1,0xbf,0xa0,0xe7,0x4f,0x46,0x46,0x54,0x18,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x56,0x45,0x52,0x53,0x08,0x00,0x01,0x00,0x08,
    0x00,0x01,0x00,0x01,0x00,0x45,0x4e,0x44,0x54,0x37,0x00,0x00,0x00,0x56,0x41,0x54,
    0x54,0x22,0x00,0x03,0x00,0x70,0x6f,0x73,0x69,0x74,0x69,0x6f,0x6e,0x00,0x00,0x80,
    0x74,0x65,0x78,0x63,0x6f,0x6f,0x72,0x64,0x30,0x00,0x01,0x80,0x63,0x6f,0x6c,0x6f,
    0x72,0x30,0x00,0x02,0x80,0x56,0x41,0x54,0x59,0x05,0x00,0x03,0x00,0x04,0x04,0x06,
    0x45,0x4e,0x44,0x54,0x04,0x00,0x00,0x00,0x45,0x4e,0x44,0x54,0xde,0xc0,0x17,0x0b,
    0x00,0x00,0x00,0x00,0x14,0x00,0x00,0x00,0x20,0x0a,0x00,0x00,0xff,0xff,0xff,0xff,
    0x42,0x43,0xc0,0xde,0x21,0x0c,0x00,0x00,0x85,0x02,0x00,0x00,0x0b,0x82,0x20,0x00,
    0x02,0x00,0x00,0x00,0x12,0x00,0x00,0x00,0x07,0x81,0x23,0x91,0x41,0xc8,0x04,0x49,
    0x06,0x10,0x32,0x39,0x92,0x01,0x84,0x0c,0x25,0x05,0x08,0x19,0x1e,0x04,0x8b,0x62,
    0x80,0x10,0x45,0x02,0x42,0x92,0x0b,0x42,0x84,0x10,0x32,0x14,0x38,0x08,0x18,0x49,
    0x0a,0x32,0x44,0x24,0x48,0x0a,0x90,0x21,0x23,0xc4,0x52,0x80,0x0c,0x19,0x21,0x72,
    0x24,0x07,0xc8,0x08,0x11,0x62,0xa8,0xa0,0xa8,0x40,0xc6,0xf0,0x01,0x00,0x00,0x00,
    0x51,0x18,0x00,0x00,0x6c,0x00,0x00,0x00,0x1b,0x7e,0x24,0xf8,0xff,0xff,0xff,0xff,
    0x01,0x90,0x00,0x8a,0x08,0x07,0x78,0x80,0x07,0x79,0x78,0x07,0x7c,0x68,0x03,0x73,
    0xa8,0x07,0x77,0x18,0x87,0x36,0x30,0x07,0x78,0x68,0x83,0x76,0x08,0x07,0x7a,0x40,
    0x07,0x80,0x1e,0xe4,0xa1,0x1e,0xca,0x01,0x20,0xda,0x21,0x1d,0xdc,0xa1,0x0d,0xd8,
    0xa1,0x1c,0xce,0x21,0x1c,0xd8,0xa1,0x0d,0xec,0xa1,0x1c,0xc6,0x81,0x1e,0xde,0x41,
    0x1e,0xda,0xe0,0x1e,0xd2,0x81,0x1c,0xe8,0x01,0x1d,0x80,0x38,0x90,0x03,0x3c,0x00,
    0x06,0x77,0x78,0x87,0x36,0x10,0x87,0x7a,0x48,0x07,0x76,0xa0,0x87,0x74,0x70,0x87,
    0x79,0x00,0x08,0x77,0x78,0x87,0x36,0x30,0x07,0x79,0x08,0x87,0x76,0x28,0x87,0x36,
    0x80,0x87,0x77,0x48,0x07,0x77,0xa0,0x87,0x72,0x90,0x87,0x36,0x28,0x07,0x76,0x48,
    0x87,0x76,0x00,0xe8,0x41,0x1e,0xea,0xa1,0x1c,0x00,0xc2,0x1d,0xde,0xa1,0x0d,0xcc,
    0x41,0x1e,0xc2,0xa1,0x1d,0xca,0xa1,0x0d,0xe0,0xe1,0x1d,0xd2,0xc1,0x1d,0xe8,0xa1,
    0x1c,0xe4,0xa1,0x0d,0xca,0x81,0x1d,0xd2,0xa1,0x1d,0xda,0xc0,0x1d,0xde,0xc1,0x1d,
    0xda,0x80,0x1d,0xca,0x21,0x1c,0xcc,0x01,0xa0,0x07,0x79,0xa8,0x87,0x72,0x00,0x08,
    0x77,0x78,0x87,0x36,0x48,0x07,0x77,0x30,0x87,0x79,0x68,0x03,0x73,0x80,0x87,0x36,
    0x68,0x87,0x70,0xa0,0x07,0x74,0x00,0xe8,0x41,0x1e,0xea,0xa1,0x1c,0x00,0xc2,0x1d,
    0xde,0xa1,0x0d,0xdc,0x21,0x1c,0xdc,0x61,0x1e,0xda,0xc0,0x1c,0xe0,0xa1,0x0d,0xda,
    0x21,0x1c,0xe8,0x01,0x1d,0x00,0x7a,0x90,0x87,0x7a,0x28,0x07,0x80,0x70,0x87,0x77,
    0x68,0x83,0x79,0x48,0x87,0x73,0x70,0x87,0x72,0x20,0x87,0x36,0xd0,0x87,0x72,0x90,
    0x87,0x77,0x98,0x87,0x36,0x30,0x07,0x78,0x68,0x83,0x76,0x08,0x07,0x7a,0x40,0x07,
    0x80,0x1e,0xe4,0xa1,0x1e,0xca,0x01,0x20,0xdc,0xe1,0x1d,0xda,0x80,0x1e,0xe4,0x21,
    0x1c,0xe0,0x01,0x1e,0xd2,0xc1,0x1d,0xce,0xa1,0x0d,0xda,0x21,0x1c,0xe8,0x01,0x1d,
    0x00,0x7a,0x90,0x87,0x7a,0x28,0x07,0x80,0x98,0x07,0x7a,0x08,0x87,0x71,0x58,0x87,
    0x36,0x80,0x07,0x79,0x78,0x07,0x7a,0x28,0x87,0x71,0xa0,0x87,0x77,0x90,0x87,0x36,
    0x10,0x87,0x7a,0x30,0x07,0x73,0x28,0x07,0x79,0x68,0x83,0x79,0x48,0x07,0x7d,0x28,
    0x07,0x00,0x0f,0x00,0xa2,0x1e,0xdc,0x61,0x1e,0xc2,0xc1,0x1c,0xca,0xa1,0x0d,0xcc,
    0x01,0x1e,0xda,0xa0,0x1d,0xc2,0x81,0x1e,0xd0,0x01,0xa0,0x07,0x79,0xa8,0x87,0x72,
    0x00,0x36,0x18,0xc2,0xff,0xff,0xff,0xff,0x0f,0x80,0x04,0x50,0x1b,0x8c,0xe1,0xff,
    0xff,0xff,0xff,0x07,0x40,0x02,0x28,0x00,0x49,0x18,0x00,0x00,0x03,0x00,0x00,0x00,
    0x13,0x82,0x60,0x42,0x20,0x4c,0x08,0x06,0x00,0x00,0x00,0x00,0x89,0x20,0x00,0x00,
    0x11,0x00,0x00,0x00,0x32,0x22,0x08,0x09,0x20,0x64,0x85,0x04,0x13,0x22,0xa4,0x84,
    0x04,0x13,0x22,0xe3,0x84,0xa1,0x90,0x14,0x12,0x4c,0x88,0x8c,0x0b,0x84,0x84,0x4c,
    0x10,0x34,0x33,0x00,0xc3,0x08,0x02,0x30,0x8c,0x40,0x00,0x76,0x08,0x91,0x42,0x4c,
    0x84,0x10,0x15,0x22,0x22,0x82,0x6c,0x20,0x60,0x8e,0x00,0x0c,0x52,0x20,0x87,0x11,
    0x88,0x64,0x04,0x00,0x00,0x00,0x00,0x00,0x13,0xb2,0x70,0x48,0x07,0x79,0xb0,0x03,
    0x3a,0x68,0x83,0x70,0x80,0x07,0x78,0x60,0x87,0x72,0x68,0x83,0x76,0x08,0x87,0x71,
    0x78,0x87,0x79,0xc0,0x87,0x38,0x80,0x03,0x37,0x88,0x83,0x38,0x70,0x03,0x38,0xd8,
    0x70,0x1b,0xe5,0xd0,0x06,0xf0,0xa0,0x07,0x76,0x40,0x07,0x7a,0x60,0x07,0x74,0xa0,
    0x07,0x76,0x40,0x07,0x6d,0x90,0x0e,0x71,0xa0,0x07,0x78,0xa0,0x07,0x78,0xd0,0x06,
    0xe9,0x80,0x07,0x7a,0x80,0x07,0x7a,0x80,0x07,0x6d,0x90,0x0e,0x71,0x60,0x07,0x7a,
    0x10,0x07,0x76,0xa0,0x07,0x71,0x60,0x07,0x6d,0x90,0x0e,0x73,0x20,0x07,0x7a,0x30,
    0x07,0x72,0xa0,0x07,0x73,0x20,0x07,0x6d,0x90,0x0e,0x76,0x40,0x07,0x7a,0x60,0x07,
    0x74,0xa0,0x07,0x76,0x40,0x07,0x6d,0x60,0x0e,0x73,0x20,0x07,0x7a,0x30,0x07,0x72,
    0xa0,0x07,0x73,0x20,0x07,0x6d,0x60,0x0e,0x76,0x40,0x07,0x7a,0x60,0x07,0x74,0xa0,
    0x07,0x76,0x40,0x07,0x6d,0x60,0x0f,0x71,0x60,0x07,0x7a,0x10,0x07,0x76,0xa0,0x07,
    0x71,0x60,0x07,0x6d,0x60,0x0f,0x72,0x40,0x07,0x7a,0x30,0x07,0x72,0xa0,0x07,0x73,
    0x20,0x07,0x6d,0x60,0x0f,0x73,0x20,0x07,0x7a,0x30,0x07,0x72,0xa0,0x07,0x73,0x20,
    0x07,0x6d,0x60,0x0f,0x74,0x80,0x07,0x7a,0x60,0x07,0x74,0xa0,0x07,0x76,0x40,0x07,
    0x6d,0x60,0x0f,0x76,0x40,0x07,0x7a,0x60,0x07,0x74,0xa0,0x07,0x76,0x40,0x07,0x6d,
    0x60,0x0f,0x79,0x60,0x07,0x7a,0x10,0x07,0x72,0x80,0x07,0x7a,0x10,0x07,0x72,0x80,
    0x07,0x6d,0x60,0x0f,0x71,0x20,0x07,0x78,0xa0,0x07,0x71,0x20,0x07,0x78,0xa0,0x07,
    0x71,0x20,0x07,0x78,0xd0,0x06,0xf6,0x10,0x07,0x79,0x20,0x07,0x7a,0x20,0x07,0x75,
    0x60,0x07,0x7a,0x20,0x07,0x75,0x60,0x07,0x6d,0x60,0x0f,0x72,0x50,0x07,0x76,0xa0,
    0x07,0x72,0x50,0x07,0x76,0xa0,0x07,0x72,0x50,0x07,0x76,0xd0,0x06,0xf6,0x50,0x07,
    0x71,0x20,0x07,0x7a,0x50,0x07,0x71,0x20,0x07,0x7a,0x50,0x07,0x71,0x20,0x07,0x6d,
    0x60,0x0f,0x71,0x00,0x07,0x72,0x40,0x07,0x7a,0x10,0x07,0x70,0x20,0x07,0x74,0xa0,
    0x07,0x71,0x00,0x07,0x72,0x40,0x07,0x6d,0xe0,0x0e,0x78,0xa0,0x07,0x71,0x60,0x07,
    0x7a,0x30,0x07,0x72,0x30,0x84,0x29,0x00,0x00,0x08,0x00,0x00,0x00,0x00,0x00,0x18,
    0xc2,0x1c,0x40,0x00,0x08,0x00,0x00,0x00,0x00,0x00,0x64,0x81,0x00,0x00,0x00,0x00,
    0x08,0x00,0x00,0x00,0x32,0x1e,0x98,0x10,0x19,0x11,0x4c,0x90,0x8c,0x09,0x26,0x47,
    0xc6,0x04,0x43,0xca,0x12,0x18,0x01,0x28,0x82,0x42,0x28,0x08,0xd2,0xb1,0x84,0x07,
    0x00,0x00,0x00,0x00,0x79,0x18,0x00,0x00,0xb1,0x00,0x00,0x00,0x1a,0x03,0x4c,0x10,
    0x97,0x29,0xa2,0x25,0x10,0xab,0x32,0xb9,0xb9,0xb4,0x37,0xb7,0x21,0x46,0x42,0x20,
    0x80,0x72,0x50,0xb9,0x1b,0x43,0x0b,0x93,0xfb,0x9a,0x4b,0xd3,0x2b,0x1b,0x62,0x24,
    0x02,0x22,0x24,0x05,0xe3,0x20,0x08,0x0e,0x8e,0xad,0x0c,0xa4,0xad,0x8c,0x2e,0x8c,
    0x0d,0xc4,0xae,0x4c,0x6e,0x2e,0xed,0xcd,0x0d,0x64,0x46,0x06,0x46,0x66,0xc6,0x65,
    0x66,0xa6,0x06,0x04,0xa5,0xad,0x8c,0x2e,0x8c,0xcd,0xac,0xac,0x65,0x46,0x06,0x46,
    0x66,0xc6,0x65,0x66,0xa6,0x26,0x65,0x88,0x80,0x10,0x43,0x8c,0x44,0x48,0x8c,0x64,
    0x60,0xd1,0x54,0x46,0x17,0xc6,0x36,0x04,0x41,0x8e,0x44,0x48,0x84,0x64,0xe0,0x16,
    0x96,0x26,0xe7,0x32,0xf6,0xd6,0x06,0x97,0xc6,0x56,0xe6,0x42,0x56,0xe6,0xf6,0x26,
    0xd7,0x36,0xf7,0x45,0x96,0x36,0x17,0x26,0xc6,0x56,0x36,0x44,0x40,0x12,0x72,0x61,
    0x69,0x72,0x2e,0x63,0x6f,0x6d,0x70,0x69,0x6c,0x65,0x2e,0x66,0x61,0x73,0x74,0x5f,
    0x6d,0x61,0x74,0x68,0x5f,0x65,0x6e,0x61,0x62,0x6c,0x65,0x43,0x04,0x64,0x61,0x19,
    0x84,0xa5,0xc9,0xb9,0x8c,0xbd,0xb5,0xc1,0xa5,0xb1,0x95,0xb9,0x98,0xc9,0x85,0xb5,
    0x95,0x89,0xd5,0x99,0x99,0x95,0xc9,0x7d,0x99,0x95,0xd1,0x8d,0xa1,0x7d,0x91,0xa5,
    0xcd,0x85,0x89,0xb1,0x95,0x0d,0x11,0x90,0x86,0x51,0x58,0x9a,0x9c,0x8b,0x5d,0x99,
    0x1c,0x5d,0x19,0xde,0xd7,0x5b,0x1d,0x1d,0x5c,0x1d,0x1d,0x97,0xba,0xb9,0x32,0x39,
    0x14,0xb6,0xb7,0x31,0x37,0x98,0x14,0x46,0x61,0x69,0x72,0x2e,0x61,0x72,0x67,0x5f,
    0x74,0x79,0x70,0x65,0x5f,0x6e,0x61,0x6d,0x65,0x34,0xcc,0xd8,0xde,0xc2,0xe8,0x64,
    0xc8,0x84,0xa5,0xc9,0xb9,0x84,0xc9,0x9d,0x7d,0xb9,0x85,0xb5,0x95,0x51,0xa8,0xb3,
    0x1b,0xc2,0x20,0x0f,0x02,0x21,0x11,0x22,0x21,0x13,0x42,0x71,0xa9,0x9b,0x2b,0x93,
    0x43,0x61,0x7b,0x1b,0x73,0x8b,0x49,0xa1,0x61,0xc6,0xf6,0x16,0x46,0x47,0xc3,0x62,
    0xec,0x8d,0xed,0x4d,0x6e,0x08,0x83,0x3c,0x88,0x85,0x44,0xc8,0x85,0x4c,0x08,0x46,
    0x26,0x2c,0x4d,0xce,0x05,0xee,0x6d,0x2e,0x8d,0x2e,0xed,0xcd,0x8d,0xcb,0x19,0xdb,
    0x17,0xd4,0xdb,0x5c,0x1a,0x5d,0xda,0x9b,0xdb,0x10,0x05,0xd1,0x90,0x08,0xb9,0x90,
    0x09,0xd9,0x86,0x18,0x48,0x85,0x64,0x08,0x47,0x28,0x2c,0x4d,0xce,0xc5,0xae,0x4c,
    0x8e,0xae,0x0c,0xef,0x2b,0xcd,0x0d,0xae,0x8e,0x8e,0x52,0x58,0x9a,0x9c,0x0b,0xdb,
    0xdb,0x58,0x18,0x5d,0xda,0x9b,0xdb,0x57,0x9a,0x1b,0x59,0x19,0x1e,0xbd,0xb3,0x32,
    0xb7,0x32,0xb9,0x30,0xba,0x32,0x32,0x94,0xaf,0xaf,0xb0,0x34,0xb9,0x2f,0x38,0xb6,
    0xb0,0xb1,0x32,0xb4,0x37,0x36,0xb2,0x32,0xb9,0xaf,0xaf,0x14,0x22,0x70,0x6f,0x73,
    0x69,0x74,0x69,0x6f,0x6e,0x43,0xa8,0x64,0x40,0x3c,0xe4,0x4b,0x86,0x44,0x40,0xc0,
    0x00,0x89,0x10,0x09,0x99,0x90,0x30,0x60,0x42,0x57,0x86,0x37,0xf6,0xf6,0x26,0x47,
    0x06,0x33,0x84,0x4a,0x04,0xc4,0x43,0xbe,0x44,0x48,0x04,0x04,0x0c,0x90,0x08,0x91,
    0x90,0x09,0x19,0x03,0x1a,0x63,0x6f,0x6c,0x6f,0x72,0x30,0x43,0xa8,0x84,0x40,0x3c,
    0xe4,0x4b,0x88,0x44,0x40,0xc0,0x00,0x89,0x90,0x0b,0x99,0x90,0x32,0x18,0x62,0x20,
    0x62,0x80,0x90,0x01,0x62,0x06,0x43,0x8c,0x02,0x40,0x3a,0xe4,0x0c,0x46,0x44,0xec,
    0xc0,0x0e,0xf6,0xd0,0x0e,0x6e,0xd0,0x0e,0xef,0x40,0x0e,0xf5,0xc0,0x0e,0xe5,0xe0,
    0x06,0xe6,0xc0,0x0e,0xe1,0x70,0x0e,0xf3,0x30,0x45,0x08,0x86,0x11,0x0a,0x3b,0xb0,
    0x83,0x3d,0xb4,0x83,0x1b,0xa4,0x03,0x39,0x94,0x83,0x3b,0xd0,0xc3,0x94,0xa0,0x18,
    0xb1,0x84,0x43,0x3a,0xc8,0x83,0x1b,0xd8,0x43,0x39,0xc8,0xc3,0x3c,0xa4,0xc3,0x3b,
    0xb8,0xc3,0x94,0xc0,0x18,0x41,0x85,0x43,0x3a,0xc8,0x83,0x1b,0xb0,0x43,0x38,0xb8,
    0xc3,0x39,0xd4,0x43,0x38,0x9c,0x43,0x39,0xfc,0x82,0x3d,0x94,0x83,0x3c,0xcc,0x43,
    0x3a,0xbc,0x83,0x3b,0x4c,0x09,0x90,0x11,0x53,0x38,0xa4,0x83,0x3c,0xb8,0xc1,0x38,
    0xbc,0x43,0x3b,0xc0,0x43,0x3a,0xb0,0x43,0x39,0xfc,0xc2,0x3b,0xc0,0x03,0x3d,0xa4,
    0xc3,0x3b,0xb8,0xc3,0x3c,0x4c,0x19,0x14,0xc6,0x19,0xa1,0x84,0x43,0x3a,0xc8,0x83,
    0x1b,0xd8,0x43,0x39,0xc8,0x03,0x3d,0x94,0x03,0x3e,0x4c,0x09,0xd0,0x00,0x00,0x00,
    0x79,0x18,0x00,0x00,0xa5,0x00,0x00,0x00,0x33,0x08,0x80,0x1c,0xc4,0xe1,0x1c,0x66,
    0x14,0x01,0x3d,0x88,0x43,0x38,0x84,0xc3,0x8c,0x42,0x80,0x07,0x79,0x78,0x07,0x73,
    0x98,0x71,0x0c,0xe6,0x00,0x0f,0xed,0x10,0x0e,0xf4,0x80,0x0e,0x33,0x0c,0x42,0x1e,
    0xc2,0xc1,0x1d,0xce,0xa1,0x1c,0x66,0x30,0x05,0x3d,0x88,0x43,0x38,0x84,0x83,0x1b,
    0xcc,0x03,0x3d,0xc8,0x43,0x3d,0x8c,0x03,0x3d,0xcc,0x78,0x8c,0x74,0x70,0x07,0x7b,
    0x08,0x07,0x79,0x48,0x87,0x70,0x70,0x07,0x7a,0x70,0x03,0x76,0x78,0x87,0x70,0x20,
    0x87,0x19,0xcc,0x11,0x0e,0xec,0x90,0x0e,0xe1,0x30,0x0f,0x6e,0x30,0x0f,0xe3,0xf0,
    0x0e,0xf0,0x50,0x0e,0x33,0x10,0xc4,0x1d,0xde,0x21,0x1c,0xd8,0x21,0x1d,0xc2,0x61,
    0x1e,0x66,0x30,0x89,0x3b,0xbc,0x83,0x3b,0xd0,0x43,0x39,0xb4,0x03,0x3c,0xbc,0x83,
    0x3c,0x84,0x03,0x3b,0xcc,0xf0,0x14,0x76,0x60,0x07,0x7b,0x68,0x07,0x37,0x68,0x87,
    0x72,0x68,0x07,0x37,0x80,0x87,0x70,0x90,0x87,0x70,0x60,0x07,0x76,0x28,0x07,0x76,
    0xf8,0x05,0x76,0x78,0x87,0x77,0x80,0x87,0x5f,0x08,0x87,0x71,0x18,0x87,0x72,0x98,
    0x87,0x79,0x98,0x81,0x2c,0xee,0xf0,0x0e,0xee,0xe0,0x0e,0xf5,0xc0,0x0e,0xec,0x30,
    0x03,0x62,0xc8,0xa1,0x1c,0xe4,0xa1,0x1c,0xcc,0xa1,0x1c,0xe4,0xa1,0x1c,0xdc,0x61,
    0x1c,0xca,0x21,0x1c,0xc4,0x81,0x1d,0xca,0x61,0x06,0xd6,0x90,0x43,0x39,0xc8,0x43,
    0x39,0x98,0x43,0x39,0xc8,0x43,0x39,0xb8,0xc3,0x38,0x94,0x43,0x38,0x88,0x03,0x3b,
    0x94,0xc3,0x2f,0xbc,0x83,0x3c,0xfc,0x82,0x3b,0xd4,0x03,0x3b,0xb0,0xc3,0x0c,0xc7,
    0x69,0x87,0x70,0x58,0x87,0x72,0x70,0x83,0x74,0x68,0x07,0x78,0x60,0x87,0x74,0x18,
    0x87,0x74,0xa0,0x87,0x19,0xce,0x53,0x0f,0xee,0x00,0x0f,0xf2,0x50,0x0e,0xe4,0x90,
    0x0e,0xe3,0x40,0x0f,0xe1,0x20,0x0e,0xec,0x50,0x0e,0x33,0x20,0x28,0x1d,0xdc,0xc1,
    0x1e,0xc2,0x41,0x1e,0xd2,0x21,0x1c,0xdc,0x81,0x1e,0xdc,0xe0,0x1c,0xe4,0xe1,0x1d,
    0xea,0x01,0x1e,0x66,0x18,0x51,0x38,0xb0,0x43,0x3a,0x9c,0x83,0x3b,0xcc,0x50,0x24,
    0x76,0x60,0x07,0x7b,0x68,0x07,0x37,0x60,0x87,0x77,0x78,0x07,0x78,0x98,0x51,0x4c,
    0xf4,0x90,0x0f,0xf0,0x50,0x0e,0x33,0x1e,0x6a,0x1e,0xca,0x61,0x1c,0xe8,0x21,0x1d,
    0xde,0xc1,0x1d,0x7e,0x01,0x1e,0xe4,0xa1,0x1c,0xcc,0x21,0x1d,0xf0,0x61,0x06,0x54,
    0x85,0x83,0x38,0xcc,0xc3,0x3b,0xb0,0x43,0x3d,0xd0,0x43,0x39,0xfc,0xc2,0x3c,0xe4,
    0x43,0x3b,0x88,0xc3,0x3b,0xb0,0xc3,0x8c,0xc5,0x0a,0x87,0x79,0x98,0x87,0x77,0x18,
    0x87,0x74,0x08,0x07,0x7a,0x28,0x07,0x72,0x98,0x81,0x5c,0xe3,0x10,0x0e,0xec,0xc0,
    0x0e,0xe5,0x50,0x0e,0xf3,0x30,0x23,0xc1,0xd2,0x41,0x1e,0xe4,0xe1,0x17,0xd8,0xe1,
    0x1d,0xde,0x01,0x1e,0x66,0x48,0x19,0x3b,0xb0,0x83,0x3d,0xb4,0x83,0x1b,0x84,0xc3,
    0x38,0x8c,0x43,0x39,0xcc,0xc3,0x3c,0xb8,0xc1,0x39,0xc8,0xc3,0x3b,0xd4,0x03,0x3c,
    0xcc,0x48,0xb4,0x71,0x08,0x07,0x76,0x60,0x07,0x71,0x08,0x87,0x71,0x58,0x87,0x19,
    0xdb,0xc6,0x0e,0xec,0x60,0x0f,0xed,0xe0,0x06,0xf0,0x20,0x0f,0xe5,0x30,0x0f,0xe5,
    0x20,0x0f,0xf6,0x50,0x0e,0x6e,0x10,0x0e,0xe3,0x30,0x0e,0xe5,0x30,0x0f,0xf3,0xe0,
    0x06,0xe9,0xe0,0x0e,0xe4,0x50,0x0e,0xf8,0x30,0x23,0xe2,0xec,0x61,0x1c,0xc2,0x81,
    0x1d,0xd8,0xe1,0x17,0xec,0x21,0x1d,0xe6,0x21,0x1d,0xc4,0x21,0x1d,0xd8,0x21,0x1d,
    0xe8,0x21,0x1f,0x66,0x20,0x9d,0x3b,0xbc,0x43,0x3d,0xb8,0x03,0x39,0x94,0x83,0x39,
    0xcc,0x58,0xbc,0x70,0x70,0x07,0x77,0x78,0x07,0x7a,0x08,0x07,0x7a,0x48,0x87,0x77,
    0x70,0x87,0x19,0xce,0x87,0x0e,0xe5,0x10,0x0e,0xf0,0x10,0x0e,0xec,0xc0,0x0e,0xef,
    0x30,0x0e,0xf3,0x90,0x0e,0xf4,0x50,0x0e,0x33,0x28,0x30,0x08,0x87,0x74,0x90,0x07,
    0x37,0x30,0x87,0x7a,0x70,0x87,0x71,0xa0,0x87,0x74,0x78,0x07,0x77,0xf8,0x85,0x73,
    0x90,0x87,0x77,0xa8,0x07,0x78,0x98,0x07,0x00,0x00,0x00,0x00,0x71,0x20,0x00,0x00,
    0x05,0x00,0x00,0x00,0x06,0x50,0x30,0x00,0xd2,0xd0,0x16,0xd0,0x00,0x48,0xe4,0x17,
    0x0c,0xe0,0x57,0x76,0x71,0xdb,0x00,0x00,0x61,0x20,0x00,0x00,0x1b,0x00,0x00,0x00,
    0x13,0x04,0x41,0x2c,0x10,0x00,0x00,0x00,0x10,0x00,0x00,0x00,0xb4,0x63,0x11,0x40,
    0x60,0x1c,0x73,0x10,0x83,0xd0,0x34,0x94,0x33,0x00,0x14,0x63,0x09,0x20,0x08,0x82,
    0x20,0x18,0x80,0x20,0x08,0x82,0xe0,0x30,0x96,0x00,0x82,0x20,0x88,0xff,0x02,0x08,
    0x82,0x20,0xfe,0xcd,0x00,0x90,0xcc,0x41,0x54,0x15,0x35,0xd1,0xcc,0x00,0x10,0x8c,
    0x11,0x80,0x20,0x08,0xe2,0xdf,0x08,0xc0,0x0c,0x00,0x00,0x00,0x23,0x06,0xc6,0x10,
    0x54,0x0e,0x72,0x0c,0x32,0x04,0xc7,0x32,0xc8,0x10,0x1c,0xcd,0x6c,0xc3,0x01,0x01,
    0xb3,0x0d,0x01,0x14,0xcc,0x36,0x04,0x83,0x90,0x01,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
/*
    #include <metal_stdlib>
    #include <simd/simd.h>

    using namespace metal;

    struct main0_out
    {
        float4 frag_color [[color(0)]];
    };

    struct main0_in
    {
        float2 uv [[user(locn0)]];
        float4 color [[user(locn1)]];
    };

    fragment main0_out main0(main0_in in [[stage_in]], texture2d<float> tex [[texture(0)]], sampler smp [[sampler(0)]])
    {
        main0_out out = {};
        out.frag_color = tex.sample(smp, in.uv).xxxx * in.color;
        return out;
    }
*/
static const uint8_t _sdtx_fs_bytecode_metal_macos[3033] = {
    0x4d,0x54,0x4c,0x42,0x01,0x80,0x02,0x00,0x02,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0xd9,0x0b,0x00,0x00,0x00,0x00,0x00,0x00,0x58,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x6d,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xc9,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x08,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xd1,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x08,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xd9,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x0b,0x00,0x00,0x00,0x00,0x00,0x00,0x01,0x00,0x00,0x00,0x6d,0x00,0x00,0x00,
    0x4e,0x41,0x4d,0x45,0x06,0x00,0x6d,0x61,0x69,0x6e,0x30,0x00,0x54,0x59,0x50,0x45,
    0x01,0x00,0x01,0x48,0x41,0x53,0x48,0x20,0x00,0x60,0xb0,0xd5,0x97,0xc2,0xac,0x24,
    0x52,0x1f,0x4a,0x56,0x12,0x1a,0x52,0xd7,0xb5,0xc0,0x14,0xee,0xce,0x96,0x55,0xcf,
    0x7d,0x2c,0x16,0x4c,0x83,0x17,0xaf,0xc3,0x9d,0x4f,0x46,0x46,0x54,0x18,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x56,0x45,0x52,0x53,0x08,0x00,0x01,0x00,0x08,
    0x00,0x01,0x00,0x01,0x00,0x45,0x4e,0x44,0x54,0x04,0x00,0x00,0x00,0x45,0x4e,0x44,
    0x54,0x04,0x00,0x00,0x00,0x45,0x4e,0x44,0x54,0xde,0xc0,0x17,0x0b,0x00,0x00,0x00,
    0x00,0x14,0x00,0x00,0x00,0xec,0x0a,0x00,0x00,0xff,0xff,0xff,0xff,0x42,0x43,0xc0,
    0xde,0x21,0x0c,0x00,0x00,0xb8,0x02,0x00,0x00,0x0b,0x82,0x20,0x00,0x02,0x00,0x00,
    0x00,0x12,0x00,0x00,0x00,0x07,0x81,0x23,0x91,0x41,0xc8,0x04,0x49,0x06,0x10,0x32,
    0x39,0x92,0x01,0x84,0x0c,0x25,0x05,0x08,0x19,0x1e,0x04,0x8b,0x62,0x80,0x14,0x45,
    0x02,0x42,0x92,0x0b,0x42,0xa4,0x10,0x32,0x14,0x38,0x08,0x18,0x49,0x0a,0x32,0x44,
    0x24,0x48,0x0a,0x90,0x21,0x23,0xc4,0x52,0x80,0x0c,0x19,0x21,0x72,0x24,0x07,0xc8,
    0x48,0x11,0x62,0xa8,0xa0,0xa8,0x40,0xc6,0xf0,0x01,0x00,0x00,0x00,0x51,0x18,0x00,
    0x00,0x74,0x00,0x00,0x00,0x1b,0xc2,0x24,0xf8,0xff,0xff,0xff,0xff,0x01,0x60,0x00,
    0x09,0xa8,0x88,0x70,0x80,0x07,0x78,0x90,0x87,0x77,0xc0,0x87,0x36,0x30,0x87,0x7a,
    0x70,0x87,0x71,0x68,0x03,0x73,0x80,0x87,0x36,0x68,0x87,0x70,0xa0,0x07,0x74,0x00,
    0xe8,0x41,0x1e,0xea,0xa1,0x1c,0x00,0xa2,0x1d,0xd2,0xc1,0x1d,0xda,0x80,0x1d,0xca,
    0xe1,0x1c,0xc2,0x81,0x1d,0xda,0xc0,0x1e,0xca,0x61,0x1c,0xe8,0xe1,0x1d,0xe4,0xa1,
    0x0d,0xee,0x21,0x1d,0xc8,0x81,0x1e,0xd0,0x01,0x88,0x03,0x39,0xc0,0x03,0x60,0x70,
    0x87,0x77,0x68,0x03,0x71,0xa8,0x87,0x74,0x60,0x07,0x7a,0x48,0x07,0x77,0x98,0x07,
    0x80,0x70,0x87,0x77,0x68,0x03,0x73,0x90,0x87,0x70,0x68,0x87,0x72,0x68,0x03,0x78,
    0x78,0x87,0x74,0x70,0x07,0x7a,0x28,0x07,0x79,0x68,0x83,0x72,0x60,0x87,0x74,0x68,
    0x07,0x80,0x1e,0xe4,0xa1,0x1e,0xca,0x01,0x20,0xdc,0xe1,0x1d,0xda,0xc0,0x1c,0xe4,
    0x21,0x1c,0xda,0xa1,0x1c,0xda,0x00,0x1e,0xde,0x21,0x1d,0xdc,0x81,0x1e,0xca,0x41,
    0x1e,0xda,0xa0,0x1c,0xd8,0x21,0x1d,0xda,0xa1,0x0d,0xdc,0xe1,0x1d,0xdc,0xa1,0x0d,
    0xd8,0xa1,0x1c,0xc2,0xc1,0x1c,0x00,0x7a,0x90,0x87,0x7a,0x28,0x07,0x80,0x70,0x87,
    0x77,0x68,0x83,0x74,0x70,0x07,0x73,0x98,0x87,0x36,0x30,0x07,0x78,0x68,0x83,0x76,
    0x08,0x07,0x7a,0x40,0x07,0x80,0x1e,0xe4,0xa1,0x1e,0xca,0x01,0x20,0xdc,0xe1,0x1d,
    0xda,0xc0,0x1d,0xc2,0xc1,0x1d,0xe6,0xa1,0x0d,0xcc,0x01,0x1e,0xda,0xa0,0x1d,0xc2,
    0x81,0x1e,0xd0,0x01,0xa0,0x07,0x79,0xa8,0x87,0x72,0x00,0x08,0x77,0x78,0x87,0x36,
    0x98,0x87,0x74,0x38,0x07,0x77,0x28,0x07,0x72,0x68,0x03,0x7d,0x28,0x07,0x79,0x78,
    0x87,0x79,0x68,0x03,0x73,0x80,0x87,0x36,0x68,0x87,0x70,0xa0,0x07,0x74,0x00,0xe8,
    0x41,0x1e,0xea,0xa1,0x1c,0x00,0xc2,0x1d,0xde,0xa1,0x0d,0xe8,0x41,0x1e,0xc2,0x01,
    0x1e,0xe0,0x21,0x1d,0xdc,0xe1,0x1c,0xda,0xa0,0x1d,0xc2,0x81,0x1e,0xd0,0x01,0xa0,
    0x07,0x79,0xa8,0x87,0x72,0x00,0x88,0x79,0xa0,0x87,0x70,0x18,0x87,0x75,0x68,0x03,
    0x78,0x90,0x87,0x77,0xa0,0x87,0x72,0x18,0x07,0x7a,0x78,0x07,0x79,0x68,0x03,0x71,
    0xa8,0x07,0x73,0x30,0x87,0x72,0x90,0x87,0x36,0x98,0x87,0x74,0xd0,0x87,0x72,0x00,
    0xf0,0x00,0x20,0xea,0xc1,0x1d,0xe6,0x21,0x1c,0xcc,0xa1,0x1c,0xda,0xc0,0x1c,0xe0,
    0xa1,0x0d,0xda,0x21,0x1c,0xe8,0x01,0x1d,0x00,0x7a,0x90,0x87,0x7a,0x28,0x07,0x60,
    0x83,0x21,0x0c,0xc0,0x02,0x54,0x1b,0x8c,0x81,0x00,0x16,0xa0,0xda,0x80,0x10,0xff,
    0xff,0xff,0xff,0x3f,0x00,0x0c,0x20,0x01,0xd5,0x06,0xa3,0x08,0x80,0x05,0xa8,0x36,
    0x18,0x86,0x00,0x2c,0x40,0xb5,0x01,0x39,0xfe,0xff,0xff,0xff,0x7f,0x00,0x18,0x40,
    0x02,0x2a,0x00,0x00,0x00,0x49,0x18,0x00,0x00,0x04,0x00,0x00,0x00,0x13,0x86,0x40,
    0x18,0x26,0x0c,0x44,0x61,0x4c,0x18,0x8e,0xc2,0x00,0x00,0x00,0x00,0x89,0x20,0x00,
    0x00,0x1e,0x00,0x00,0x00,0x32,0x22,0x48,0x09,0x20,0x64,0x85,0x04,0x93,0x22,0xa4,
    0x84,0x04,0x93,0x22,0xe3,0x84,0xa1,0x90,0x14,0x12,0x4c,0x8a,0x8c,0x0b,0x84,0xa4,
    0x4c,0x10,0x4c,0x33,0x00,0xc3,0x08,0x04,0x60,0x83,0x30,0x8c,0x20,0x00,0x47,0x49,
    0x53,0x44,0x09,0x93,0xff,0x4f,0xc4,0x35,0x51,0x11,0xf1,0xdb,0xc3,0x3f,0x8d,0x11,
    0x00,0x83,0x08,0x44,0x70,0x91,0x34,0x45,0x94,0x30,0xf9,0xbf,0x04,0x30,0xcf,0x42,
    0x44,0xff,0x34,0x46,0x00,0x0c,0x22,0x18,0x42,0x29,0xc4,0x08,0xe5,0x10,0x9a,0x23,
    0x08,0xe6,0x08,0xc0,0x60,0x18,0x41,0x58,0x0a,0x12,0xca,0x19,0x8a,0x29,0x40,0x6d,
    0x20,0x20,0x05,0xd6,0x30,0x02,0xb1,0x8c,0x00,0x00,0x00,0x00,0x00,0x13,0xb2,0x70,
    0x48,0x07,0x79,0xb0,0x03,0x3a,0x68,0x83,0x70,0x80,0x07,0x78,0x60,0x87,0x72,0x68,
    0x83,0x76,0x08,0x87,0x71,0x78,0x87,0x79,0xc0,0x87,0x38,0x80,0x03,0x37,0x88,0x83,
    0x38,0x70,0x03,0x38,0xd8,0x70,0x1b,0xe5,0xd0,0x06,0xf0,0xa0,0x07,0x76,0x40,0x07,
    0x7a,0x60,0x07,0x74,0xa0,0x07,0x76,0x40,0x07,0x6d,0x90,0x0e,0x71,0xa0,0x07,0x78,
    0xa0,0x07,0x78,0xd0,0x06,0xe9,0x80,0x07,0x7a,0x80,0x07,0x7a,0x80,0x07,0x6d,0x90,
    0x0e,0x71,0x60,0x07,0x7a,0x10,0x07,0x76,0xa0,0x07,0x71,0x60,0x07,0x6d,0x90,0x0e,
    0x73,0x20,0x07,0x7a,0x30,0x07,0x72,0xa0,0x07,0x73,0x20,0x07,0x6d,0x90,0x0e,0x76,
    0x40,0x07,0x7a,0x60,0x07,0x74,0xa0,0x07,0x76,0x40,0x07,0x6d,0x60,0x0e,0x73,0x20,
    0x07,0x7a,0x30,0x07,0x72,0xa0,0x07,0x73,0x20,0x07,0x6d,0x60,0x0e,0x76,0x40,0x07,
    0x7a,0x60,0x07,0x74,0xa0,0x07,0x76,0x40,0x07,0x6d,0x60,0x0f,0x71,0x60,0x07,0x7a,
    0x10,0x07,0x76,0xa0,0x07,0x71,0x60,0x07,0x6d,0x60,0x0f,0x72,0x40,0x07,0x7a,0x30,
    0x07,0x72,0xa0,0x07,0x73,0x20,0x07,0x6d,0x60,0x0f,0x73,0x20,0x07,0x7a,0x30,0x07,
    0x72,0xa0,0x07,0x73,0x20,0x07,0x6d,0x60,0x0f,0x74,0x80,0x07,0x7a,0x60,0x07,0x74,
    0xa0,0x07,0x76,0x40,0x07,0x6d,0x60,0x0f,0x76,0x40,0x07,0x7a,0x60,0x07,0x74,0xa0,
    0x07,0x76,0x40,0x07,0x6d,0x60,0x0f,0x79,0x60,0x07,0x7a,0x10,0x07,0x72,0x80,0x07,
    0x7a,0x10,0x07,0x72,0x80,0x07,0x6d,0x60,0x0f,0x71,0x20,0x07,0x78,0xa0,0x07,0x71,
    0x20,0x07,0x78,0xa0,0x07,0x71,0x20,0x07,0x78,0xd0,0x06,0xf6,0x10,0x07,0x79,0x20,
    0x07,0x7a,0x20,0x07,0x75,0x60,0x07,0x7a,0x20,0x07,0x75,0x60,0x07,0x6d,0x60,0x0f,
    0x72,0x50,0x07,0x76,0xa0,0x07,0x72,0x50,0x07,0x76,0xa0,0x07,0x72,0x50,0x07,0x76,
    0xd0,0x06,0xf6,0x50,0x07,0x71,0x20,0x07,0x7a,0x50,0x07,0x71,0x20,0x07,0x7a,0x50,
    0x07,0x71,0x20,0x07,0x6d,0x60,0x0f,0x71,0x00,0x07,0x72,0x40,0x07,0x7a,0x10,0x07,
    0x70,0x20,0x07,0x74,0xa0,0x07,0x71,0x00,0x07,0x72,0x40,0x07,0x6d,0xe0,0x0e,0x78,
    0xa0,0x07,0x71,0x60,0x07,0x7a,0x30,0x07,0x72,0x30,0x84,0x49,0x00,0x00,0x08,0x00,
    0x00,0x00,0x00,0x00,0x18,0xc2,0x38,0x40,0x00,0x08,0x00,0x00,0x00,0x00,0x00,0x64,
    0x81,0x00,0x00,0x00,0x00,0x08,0x00,0x00,0x00,0x32,0x1e,0x98,0x10,0x19,0x11,0x4c,
    0x90,0x8c,0x09,0x26,0x47,0xc6,0x04,0x43,0x5a,0x25,0x30,0x02,0x50,0x04,0x85,0x50,
    0x10,0x65,0x40,0x70,0x2c,0xe1,0x01,0x00,0x00,0x79,0x18,0x00,0x00,0xd2,0x00,0x00,
    0x00,0x1a,0x03,0x4c,0x10,0x97,0x29,0xa2,0x25,0x10,0xab,0x32,0xb9,0xb9,0xb4,0x37,
    0xb7,0x21,0xc6,0x42,0x3c,0x00,0x84,0x50,0xb9,0x1b,0x43,0x0b,0x93,0xfb,0x9a,0x4b,
    0xd3,0x2b,0x1b,0x62,0x2c,0xc2,0x23,0x2c,0x05,0xe3,0x20,0x08,0x0e,0x8e,0xad,0x0c,
    0xa4,0xad,0x8c,0x2e,0x8c,0x0d,0xc4,0xae,0x4c,0x6e,0x2e,0xed,0xcd,0x0d,0x64,0x46,
    0x06,0x46,0x66,0xc6,0x65,0x66,0xa6,0x06,0x04,0xa5,0xad,0x8c,0x2e,0x8c,0xcd,0xac,
    0xac,0x65,0x46,0x06,0x46,0x66,0xc6,0x65,0x66,0xa6,0x26,0x65,0x88,0xf0,0x10,0x43,
    0x8c,0x45,0x58,0x8c,0x65,0x60,0xd1,0x54,0x46,0x17,0xc6,0x36,0x04,0x79,0x8e,0x45,
    0x58,0x84,0x65,0xe0,0x16,0x96,0x26,0xe7,0x32,0xf6,0xd6,0x06,0x97,0xc6,0x56,0xe6,
    0x42,0x56,0xe6,0xf6,0x26,0xd7,0x36,0xf7,0x45,0x96,0x36,0x17,0x26,0xc6,0x56,0x36,
    0x44,0x78,0x12,0x72,0x61,0x69,0x72,0x2e,0x63,0x6f,0x6d,0x70,0x69,0x6c,0x65,0x2e,
    0x66,0x61,0x73,0x74,0x5f,0x6d,0x61,0x74,0x68,0x5f,0x65,0x6e,0x61,0x62,0x6c,0x65,
    0x43,0x84,0x67,0x61,0x19,0x84,0xa5,0xc9,0xb9,0x8c,0xbd,0xb5,0xc1,0xa5,0xb1,0x95,
    0xb9,0x98,0xc9,0x85,0xb5,0x95,0x89,0xd5,0x99,0x99,0x95,0xc9,0x7d,0x99,0x95,0xd1,
    0x8d,0xa1,0x7d,0x91,0xa5,0xcd,0x85,0x89,0xb1,0x95,0x0d,0x11,0x9e,0x86,0x51,0x58,
    0x9a,0x9c,0x8b,0x5c,0x99,0x1b,0x59,0x99,0xdc,0x17,0x5d,0x98,0xdc,0x59,0x19,0x1d,
    0xa3,0xb0,0x34,0x39,0x97,0x30,0xb9,0xb3,0x2f,0xba,0x3c,0xb8,0xb2,0x2f,0xb7,0xb0,
    0xb6,0x32,0x1a,0x66,0x6c,0x6f,0x61,0x74,0x34,0x64,0xc2,0xd2,0xe4,0x5c,0xc2,0xe4,
    0xce,0xbe,0xdc,0xc2,0xda,0xca,0xa8,0x98,0xc9,0x85,0x9d,0x7d,0x8d,0xbd,0xb1,0xbd,
    0xc9,0x0d,0x61,0x9e,0x67,0x19,0x1e,0xe8,0x89,0x1e,0xe9,0x99,0x86,0x08,0x0f,0x45,
    0x29,0x2c,0x4d,0xce,0xc5,0x4c,0x2e,0xec,0xac,0xad,0xcc,0x8d,0xee,0x2b,0xcd,0x0d,
    0xae,0x8e,0x8e,0x4b,0xdd,0x5c,0x99,0x1c,0x0a,0xdb,0xdb,0x98,0x1b,0x4c,0x0a,0x95,
    0xb0,0x34,0x39,0x97,0xb1,0x32,0x37,0xba,0x32,0x39,0x3e,0x61,0x69,0x72,0x2e,0x70,
    0x65,0x72,0x73,0x70,0x65,0x63,0x74,0x69,0x76,0x65,0x34,0xcc,0xd8,0xde,0xc2,0xe8,
    0x64,0x28,0xd4,0xd9,0x0d,0x91,0x96,0xe1,0xb1,0x9e,0xeb,0xc1,0x9e,0xec,0x81,0x1e,
    0xed,0x91,0x9e,0x8d,0x4b,0xdd,0x5c,0x99,0x1c,0x0a,0xdb,0xdb,0x98,0x5b,0x4c,0x0a,
    0x8b,0xb1,0x37,0xb6,0x37,0xb9,0x21,0xd2,0x22,0x3c,0xd6,0xd3,0x3d,0xd8,0x93,0x3d,
    0xd0,0x13,0x3d,0xd2,0xe3,0x71,0x09,0x4b,0x93,0x73,0xa1,0x2b,0xc3,0xa3,0xab,0x93,
    0x2b,0xa3,0x14,0x96,0x26,0xe7,0xc2,0xf6,0x36,0x16,0x46,0x97,0xf6,0xe6,0xf6,0x95,
    0xe6,0x46,0x56,0x86,0x47,0x25,0x2c,0x4d,0xce,0x65,0x2e,0xac,0x0d,0x8e,0xad,0x8c,
    0x18,0x5d,0x19,0x1e,0x5d,0x9d,0x5c,0x99,0x0c,0x19,0x8f,0x19,0xdb,0x5b,0x18,0x1d,
    0x0b,0xc8,0x5c,0x58,0x1b,0x1c,0x5b,0x99,0x0f,0x07,0xba,0x32,0xbc,0x21,0xd4,0x42,
    0x3c,0x60,0xf0,0x84,0xc1,0x32,0x2c,0xc2,0x23,0x06,0x0f,0xf4,0x8c,0xc1,0x23,0x3d,
    0x64,0xc0,0x25,0x2c,0x4d,0xce,0x65,0x2e,0xac,0x0d,0x8e,0xad,0x4c,0x8e,0xc7,0x5c,
    0x58,0x1b,0x1c,0x5b,0x99,0x1c,0x87,0xb9,0x36,0xb8,0x21,0xd2,0x72,0x3c,0x66,0xf0,
    0x84,0xc1,0x32,0x2c,0xc2,0x03,0x3d,0x67,0xf0,0x48,0x0f,0x1a,0x0c,0x41,0x1e,0xee,
    0xf9,0x9e,0x32,0x78,0xd2,0x60,0x88,0x91,0x00,0x4f,0xf5,0xa8,0x01,0xaf,0xb0,0x34,
    0xb9,0x96,0x30,0xb6,0xb4,0xb0,0xb9,0x96,0xb9,0xb1,0x37,0xb8,0xb2,0x39,0x94,0xb6,
    0xb0,0x34,0x37,0x98,0x94,0x21,0xc4,0xd3,0x06,0x0f,0x1b,0x10,0x0b,0x4b,0x93,0x6b,
    0x09,0x63,0x4b,0x0b,0x9b,0x6b,0x99,0x1b,0x7b,0x83,0x2b,0x6b,0xa1,0x2b,0xc3,0xa3,
    0xab,0x93,0x2b,0x9b,0x1b,0x62,0x3c,0x6f,0xf0,0xb4,0xc1,0xe3,0x06,0xc4,0xc2,0xd2,
    0xe4,0x5a,0xc2,0xd8,0xd2,0xc2,0xe6,0x5a,0xe6,0xc6,0xde,0xe0,0xca,0x5a,0xe6,0xc2,
    0xda,0xe0,0xd8,0xca,0xe4,0xe6,0x86,0x18,0x4f,0x1c,0x3c,0x6d,0xf0,0xc0,0xc1,0x10,
    0xe2,0x79,0x83,0x27,0x0e,0x46,0x44,0xec,0xc0,0x0e,0xf6,0xd0,0x0e,0x6e,0xd0,0x0e,
    0xef,0x40,0x0e,0xf5,0xc0,0x0e,0xe5,0xe0,0x06,0xe6,0xc0,0x0e,0xe1,0x70,0x0e,0xf3,
    0x30,0x45,0x08,0x86,0x11,0x0a,0x3b,0xb0,0x83,0x3d,0xb4,0x83,0x1b,0xa4,0x03,0x39,
    0x94,0x83,0x3b,0xd0,0xc3,0x94,0xa0,0x18,0xb1,0x84,0x43,0x3a,0xc8,0x83,0x1b,0xd8,
    0x43,0x39,0xc8,0xc3,0x3c,0xa4,0xc3,0x3b,0xb8,0xc3,0x94,0xc0,0x18,0x41,0x85,0x43,
    0x3a,0xc8,0x83,0x1b,0xb0,0x43,0x38,0xb8,0xc3,0x39,0xd4,0x43,0x38,0x9c,0x43,0x39,
    0xfc,0x82,0x3d,0x94,0x83,0x3c,0xcc,0x43,0x3a,0xbc,0x83,0x3b,0x4c,0x09,0x90,0x11,
    0x53,0x38,0xa4,0x83,0x3c,0xb8,0xc1,0x38,0xbc,0x43,0x3b,0xc0,0x43,0x3a,0xb0,0x43,
    0x39,0xfc,0xc2,0x3b,0xc0,0x03,0x3d,0xa4,0xc3,0x3b,0xb8,0xc3,0x3c,0x4c,0x19,0x14,
    0xc6,0x19,0xc1,0x84,0x43,0x3a,0xc8,0x83,0x1b,0x98,0x83,0x3c,0x84,0xc3,0x39,0xb4,
    0x43,0x39,0xb8,0x03,0x3d,0x4c,0x09,0xd6,0x00,0x79,0x18,0x00,0x00,0xa5,0x00,0x00,
    0x00,0x33,0x08,0x80,0x1c,0xc4,0xe1,0x1c,0x66,0x14,0x01,0x3d,0x88,0x43,0x38,0x84,
    0xc3,0x8c,0x42,0x80,0x07,0x79,0x78,0x07,0x73,0x98,0x71,0x0c,0xe6,0x00,0x0f,0xed,
    0x10,0x0e,0xf4,0x80,0x0e,0x33,0x0c,0x42,0x1e,0xc2,0xc1,0x1d,0xce,0xa1,0x1c,0x66,
    0x30,0x05,0x3d,0x88,0x43,0x38,0x84,0x83,0x1b,0xcc,0x03,0x3d,0xc8,0x43,0x3d,0x8c,
    0x03,0x3d,0xcc,0x78,0x8c,0x74,0x70,0x07,0x7b,0x08,0x07,0x79,0x48,0x87,0x70,0x70,
    0x07,0x7a,0x70,0x03,0x76,0x78,0x87,0x70,0x20,0x87,0x19,0xcc,0x11,0x0e,0xec,0x90,
    0x0e,0xe1,0x30,0x0f,0x6e,0x30,0x0f,0xe3,0xf0,0x0e,0xf0,0x50,0x0e,0x33,0x10,0xc4,
    0x1d,0xde,0x21,0x1c,0xd8,0x21,0x1d,0xc2,0x61,0x1e,0x66,0x30,0x89,0x3b,0xbc,0x83,
    0x3b,0xd0,0x43,0x39,0xb4,0x03,0x3c,0xbc,0x83,0x3c,0x84,0x03,0x3b,0xcc,0xf0,0x14,
    0x76,0x60,0x07,0x7b,0x68,0x07,0x37,0x68,0x87,0x72,0x68,0x07,0x37,0x80,0x87,0x70,
    0x90,0x87,0x70,0x60,0x07,0x76,0x28,0x07,0x76,0xf8,0x05,0x76,0x78,0x87,0x77,0x80,
    0x87,0x5f,0x08,0x87,0x71,0x18,0x87,0x72,0x98,0x87,0x79,0x98,0x81,0x2c,0xee,0xf0,
    0x0e,0xee,0xe0,0x0e,0xf5,0xc0,0x0e,0xec,0x30,0x03,0x62,0xc8,0xa1,0x1c,0xe4,0xa1,
    0x1c,0xcc,0xa1,0x1c,0xe4,0xa1,0x1c,0xdc,0x61,0x1c,0xca,0x21,0x1c,0xc4,0x81,0x1d,
    0xca,0x61,0x06,0xd6,0x90,0x43,0x39,0xc8,0x43,0x39,0x98,0x43,0x39,0xc8,0x43,0x39,
    0xb8,0xc3,0x38,0x94,0x43,0x38,0x88,0x03,0x3b,0x94,0xc3,0x2f,0xbc,0x83,0x3c,0xfc,
    0x82,0x3b,0xd4,0x03,0x3b,0xb0,0xc3,0x0c,0xc7,0x69,0x87,0x70,0x58,0x87,0x72,0x70,
    0x83,0x74,0x68,0x07,0x78,0x60,0x87,0x74,0x18,0x87,0x74,0xa0,0x87,0x19,0xce,0x53,
    0x0f,0xee,0x00,0x0f,0xf2,0x50,0x0e,0xe4,0x90,0x0e,0xe3,0x40,0x0f,0xe1,0x20,0x0e,
    0xec,0x50,0x0e,0x33,0x20,0x28,0x1d,0xdc,0xc1,0x1e,0xc2,0x41,0x1e,0xd2,0x21,0x1c,
    0xdc,0x81,0x1e,0xdc,0xe0,0x1c,0xe4,0xe1,0x1d,0xea,0x01,0x1e,0x66,0x18,0x51,0x38,
    0xb0,0x43,0x3a,0x9c,0x83,0x3b,0xcc,0x50,0x24,0x76,0x60,0x07,0x7b,0x68,0x07,0x37,
    0x60,0x87,0x77,0x78,0x07,0x78,0x98,0x51,0x4c,0xf4,0x90,0x0f,0xf0,0x50,0x0e,0x33,
    0x1e,0x6a,0x1e,0xca,0x61,0x1c,0xe8,0x21,0x1d,0xde,0xc1,0x1d,0x7e,0x01,0x1e,0xe4,
    0xa1,0x1c,0xcc,0x21,0x1d,0xf0,0x61,0x06,0x54,0x85,0x83,0x38,0xcc,0xc3,0x3b,0xb0,
    0x43,0x3d,0xd0,0x43,0x39,0xfc,0xc2,0x3c,0xe4,0x43,0x3b,0x88,0xc3,0x3b,0xb0,0xc3,
    0x8c,0xc5,0x0a,0x87,0x79,0x98,0x87,0x77,0x18,0x87,0x74,0x08,0x07,0x7a,0x28,0x07,
    0x72,0x98,0x81,0x5c,0xe3,0x10,0x0e,0xec,0xc0,0x0e,0xe5,0x50,0x0e,0xf3,0x30,0x23,
    0xc1,0xd2,0x41,0x1e,0xe4,0xe1,0x17,0xd8,0xe1,0x1d,0xde,0x01,0x1e,0x66,0x48,0x19,
    0x3b,0xb0,0x83,0x3d,0xb4,0x83,0x1b,0x84,0xc3,0x38,0x8c,0x43,0x39,0xcc,0xc3,0x3c,
    0xb8,0xc1,0x39,0xc8,0xc3,0x3b,0xd4,0x03,0x3c,0xcc,0x48,0xb4,0x71,0x08,0x07,0x76,
    0x60,0x07,0x71,0x08,0x87,0x71,0x58,0x87,0x19,0xdb,0xc6,0x0e,0xec,0x60,0x0f,0xed,
    0xe0,0x06,0xf0,0x20,0x0f,0xe5,0x30,0x0f,0xe5,0x20,0x0f,0xf6,0x50,0x0e,0x6e,0x10,
    0x0e,0xe3,0x30,0x0e,0xe5,0x30,0x0f,0xf3,0xe0,0x06,0xe9,0xe0,0x0e,0xe4,0x50,0x0e,
    0xf8,0x30,0x23,0xe2,0xec,0x61,0x1c,0xc2,0x81,0x1d,0xd8,0xe1,0x17,0xec,0x21,0x1d,
    0xe6,0x21,0x1d,0xc4,0x21,0x1d,0xd8,0x21,0x1d,0xe8,0x21,0x1f,0x66,0x20,0x9d,0x3b,
    0xbc,0x43,0x3d,0xb8,0x03,0x39,0x94,0x83,0x39,0xcc,0x58,0xbc,0x70,0x70,0x07,0x77,
    0x78,0x07,0x7a,0x08,0x07,0x7a,0x48,0x87,0x77,0x70,0x87,0x19,0xce,0x87,0x0e,0xe5,
    0x10,0x0e,0xf0,0x10,0x0e,0xec,0xc0,0x0e,0xef,0x30,0x0e,0xf3,0x90,0x0e,0xf4,0x50,
    0x0e,0x33,0x28,0x30,0x08,0x87,0x74,0x90,0x07,0x37,0x30,0x87,0x7a,0x70,0x87,0x71,
    0xa0,0x87,0x74,0x78,0x07,0x77,0xf8,0x85,0x73,0x90,0x87,0x77,0xa8,0x07,0x78,0x98,
    0x07,0x00,0x00,0x00,0x00,0x71,0x20,0x00,0x00,0x08,0x00,0x00,0x00,0x16,0xb0,0x01,
    0x48,0xe4,0x4b,0x00,0xf3,0x2c,0xc4,0x3f,0x11,0xd7,0x44,0x45,0xc4,0x6f,0x0f,0x7e,
    0x85,0x17,0xb7,0x6d,0x00,0x05,0x03,0x20,0x0d,0x0d,0x00,0x00,0x00,0x61,0x20,0x00,
    0x00,0x14,0x00,0x00,0x00,0x13,0x04,0x41,0x2c,0x10,0x00,0x00,0x00,0x06,0x00,0x00,
    0x00,0x14,0x47,0x00,0x88,0x8d,0x00,0x90,0x1a,0x01,0xa8,0x01,0x12,0x33,0x00,0x14,
    0x66,0x00,0x08,0x8c,0x00,0x00,0x00,0x00,0x00,0x23,0x06,0xca,0x10,0x4c,0x09,0xb2,
    0x10,0x46,0x11,0x0c,0x32,0x04,0x03,0x62,0x01,0x23,0x9f,0xd9,0x06,0x23,0x00,0x32,
    0x08,0x88,0x01,0x00,0x00,0x02,0x00,0x00,0x00,0x5b,0x06,0xe0,0x90,0x03,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
/*
    #include <metal_stdlib>
    #include <simd/simd.h>

    using namespace metal;

    struct main0_out
    {
        float2 uv [[user(locn0)]];
        float4 color [[user(locn1)]];
        float4 gl_Position [[position]];
    };

    struct main0_in
    {
        float2 position [[attribute(0)]];
        float2 texcoord0 [[attribute(1)]];
        float4 color0 [[attribute(2)]];
    };

    vertex main0_out main0(main0_in in [[stage_in]])
    {
        main0_out out = {};
        out.gl_Position = float4(fma(in.position, float2(2.0, -2.0), float2(-1.0, 1.0)), 0.0, 1.0);
        out.uv = in.texcoord0;
        out.color = in.color0;
        return out;
    }
*/
static const uint8_t _sdtx_vs_bytecode_metal_ios[2876] = {
    0x4d,0x54,0x4c,0x42,0x01,0x00,0x02,0x00,0x02,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x3c,0x0b,0x00,0x00,0x00,0x00,0x00,0x00,0x58,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x6d,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xc9,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x3b,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x04,0x01,0x00,0x00,0x00,0x00,0x00,0x00,
    0x08,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0c,0x01,0x00,0x00,0x00,0x00,0x00,0x00,
    0x30,0x0a,0x00,0x00,0x00,0x00,0x00,0x00,0x01,0x00,0x00,0x00,0x6d,0x00,0x00,0x00,
    0x4e,0x41,0x4d,0x45,0x06,0x00,0x6d,0x61,0x69,0x6e,0x30,0x00,0x54,0x59,0x50,0x45,
    0x01,0x00,0x00,0x48,0x41,0x53,0x48,0x20,0x00,0x54,0x27,0x6d,0x17,0x18,0x1b,0xd1,
    0x64,0x91,0xfe,0x8a,0x4c,0x49,0xdf,0xe3,0x15,0x74,0xcb,0x3e,0x43,0xde,0xeb,0xfa,
    0x8e,0xf0,0xf5,0xf8,0x4b,0xd2,0xac,0x23,0xe4,0x4f,0x46,0x46,0x54,0x18,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x56,0x45,0x52,0x53,0x08,0x00,0x01,0x00,0x08,
    0x00,0x01,0x00,0x01,0x00,0x45,0x4e,0x44,0x54,0x37,0x00,0x00,0x00,0x56,0x41,0x54,
    0x54,0x22,0x00,0x03,0x00,0x70,0x6f,0x73,0x69,0x74,0x69,0x6f,0x6e,0x00,0x00,0x80,
    0x74,0x65,0x78,0x63,0x6f,0x6f,0x72,0x64,0x30,0x00,0x01,0x80,0x63,0x6f,0x6c,0x6f,
    0x72,0x30,0x00,0x02,0x80,0x56,0x41,0x54,0x59,0x05,0x00,0x03,0x00,0x04,0x04,0x06,
    0x45,0x4e,0x44,0x54,0x04,0x00,0x00,0x00,0x45,0x4e,0x44,0x54,0xde,0xc0,0x17,0x0b,
    0x00,0x00,0x00,0x00,0x14,0x00,0x00,0x00,0x18,0x0a,0x00,0x00,0xff,0xff,0xff,0xff,
    0x42,0x43,0xc0,0xde,0x21,0x0c,0x00,0x00,0x83,0x02,0x00,0x00,0x0b,0x82,0x20,0x00,
    0x02,0x00,0x00,0x00,0x12,0x00,0x00,0x00,0x07,0x81,0x23,0x91,0x41,0xc8,0x04,0x49,
    0x06,0x10,0x32,0x39,0x92,0x01,0x84,0x0c,0x25,0x05,0x08,0x19,0x1e,0x04,0x8b,0x62,
    0x80,0x10,0x45,0x02,0x42,0x92,0x0b,0x42,0x84,0x10,0x32,0x14,0x38,0x08,0x18,0x49,
    0x0a,0x32,0x44,0x24,0x48,0x0a,0x90,0x21,0x23,0xc4,0x52,0x80,0x0c,0x19,0x21,0x72,
    0x24,0x07,0xc8,0x08,0x11,0x62,0xa8,0xa0,0xa8,0x40,0xc6,0xf0,0x01,0x00,0x00,0x00,
    0x51,0x18,0x00,0x00,0x6c,0x00,0x00,0x00,0x1b,0x7e,0x24,0xf8,0xff,0xff,0xff,0xff,
    0x01,0x90,0x00,0x8a,0x08,0x07,0x78,0x80,0x07,0x79,0x78,0x07,0x7c,0x68,0x03,0x73,
    0xa8,0x07,0x77,0x18,0x87,0x36,0x30,0x07,0x78,0x68,0x83,0x76,0x08,0x07,0x7a,0x40,
    0x07,0x80,0x1e,0xe4,0xa1,0x1e,0xca,0x01,0x20,0xda,0x21,0x1d,0xdc,0xa1,0x0d,0xd8,
    0xa1,0x1c,0xce,0x21,0x1c,0xd8,0xa1,0x0d,0xec,0xa1,0x1c,0xc6,0x81,0x1e,0xde,0x41,
    0x1e,0xda,0xe0,0x1e,0xd2,0x81,0x1c,0xe8,0x01,0x1d,0x80,0x38,0x90,0x03,0x3c,0x00,
    0x06,0x77,0x78,0x87,0x36,0x10,0x87,0x7a,0x48,0x07,0x76,0xa0,0x87,0x74,0x70,0x87,
    0x79,0x00,0x08,0x77,0x78,0x87,0x36,0x30,0x07,0x79,0x08,0x87,0x76,0x28,0x87,0x36,
    0x80,0x87,0x77,0x48,0x07,0x77,0xa0,0x87,0x72,0x90,0x87,0x36,0x28,0x07,0x76,0x48,
    0x87,0x76,0x00,0xe8,0x41,0x1e,0xea,0xa1,0x1c,0x00,0xc2,0x1d,0xde,0xa1,0x0d,0xcc,
    0x41,0x1e,0xc2,0xa1,0x1d,0xca,0xa1,0x0d,0xe0,0xe1,0x1d,0xd2,0xc1,0x1d,0xe8,0xa1,
    0x1c,0xe4,0xa1,0x0d,0xca,0x81,0x1d,0xd2,0xa1,0x1d,0xda,0xc0,0x1d,0xde,0xc1,0x1d,
    0xda,0x80,0x1d,0xca,0x21,0x1c,0xcc,0x01,0xa0,0x07,0x79,0xa8,0x87,0x72,0x00,0x08,
    0x77,0x78,0x87,0x36,0x48,0x07,0x77,0x30,0x87,0x79,0x68,0x03,0x73,0x80,0x87,0x36,
    0x68,0x87,0x70,0xa0,0x07,0x74,0x00,0xe8,0x41,0x1e,0xea,0xa1,0x1c,0x00,0xc2,0x1d,
    0xde,0xa1,0x0d,0xdc,0x21,0x1c,0xdc,0x61,0x1e,0xda,0xc0,0x1c,0xe0,0xa1,0x0d,0xda,
    0x21,0x1c,0xe8,0x01,0x1d,0x00,0x7a,0x90,0x87,0x7a,0x28,0x07,0x80,0x70,0x87,0x77,
    0x68,0x83,0x79,0x48,0x87,0x73,0x70,0x87,0x72,0x20,0x87,0x36,0xd0,0x87,0x72,0x90,
    0x87,0x77,0x98,0x87,0x36,0x30,0x07,0x78,0x68,0x83,0x76,0x08,0x07,0x7a,0x40,0x07,
    0x80,0x1e,0xe4,0xa1,0x1e,0xca,0x01,0x20,0xdc,0xe1,0x1d,0xda,0x80,0x1e,0xe4,0x21,
    0x1c,0xe0,0x01,0x1e,0xd2,0xc1,0x1d,0xce,0xa1,0x0d,0xda,0x21,0x1c,0xe8,0x01,0x1d,
    0x00,0x7a,0x90,0x87,0x7a,0x28,0x07,0x80,0x98,0x07,0x7a,0x08,0x87,0x71,0x58,0x87,
    0x36,0x80,0x07,0x79,0x78,0x07,0x7a,0x28,0x87,0x71,0xa0,0x87,0x77,0x90,0x87,0x36,
    0x10,0x87,0x7a,0x30,0x07,0x73,0x28,0x07,0x79,0x68,0x83,0x79,0x48,0x07,0x7d,0x28,
    0x07,0x00,0x0f,0x00,0xa2,0x1e,0xdc,0x61,0x1e,0xc2,0xc1,0x1c,0xca,0xa1,0x0d,0xcc,
    0x01,0x1e,0xda,0xa0,0x1d,0xc2,0x81,0x1e,0xd0,0x01,0xa0,0x07,0x79,0xa8,0x87,0x72,
    0x00,0x36,0x18,0xc2,0xff,0xff,0xff,0xff,0x0f,0x80,0x04,0x50,0x1b,0x8c,0xe1,0xff,
    0xff,0xff,0xff,0x07,0x40,0x02,0x28,0x00,0x49,0x18,0x00,0x00,0x03,0x00,0x00,0x00,
    0x13,0x82,0x60,0x42,0x20,0x4c,0x08,0x06,0x00,0x00,0x00,0x00,0x89,0x20,0x00,0x00,
    0x11,0x00,0x00,0x00,0x32,0x22,0x08,0x09,0x20,0x64,0x85,0x04,0x13,0x22,0xa4,0x84,
    0x04,0x13,0x22,0xe3,0x84,0xa1,0x90,0x14,0x12,0x4c,0x88,0x8c,0x0b,0x84,0x84,0x4c,
    0x10,0x34,0x33,0x00,0xc3,0x08,0x02,0x30,0x8c,0x40,0x00,0x76,0x08,0x91,0x42,0x4c,
    0x84,0x10,0x15,0x22,0x22,0x82,0x6c,0x20,0x60,0x8e,0x00,0x0c,0x52,0x20,0x87,0x11,
    0x88,0x64,0x04,0x00,0x00,0x00,0x00,0x00,0x13,0xa8,0x70,0x48,0x07,0x79,0xb0,0x03,
    0x3a,0x68,0x83,0x70,0x80,0x07,0x78,0x60,0x87,0x72,0x68,0x83,0x74,0x78,0x87,0x79,
    0xc8,0x03,0x37,0x80,0x03,0x37,0x80,0x83,0x0d,0xb7,0x51,0x0e,0x6d,0x00,0x0f,0x7a,
    0x60,0x07,0x74,0xa0,0x07,0x76,0x40,0x07,0x7a,0x60,0x07,0x74,0xd0,0x06,0xe9,0x10,
    0x07,0x7a,0x80,0x07,0x7a,0x80,0x07,0x6d,0x90,0x0e,0x78,0xa0,0x07,0x78,0xa0,0x07,
    0x78,0xd0,0x06,0xe9,0x10,0x07,0x76,0xa0,0x07,0x71,0x60,0x07,0x7a,0x10,0x07,0x76,
    0xd0,0x06,0xe9,0x30,0x07,0x72,0xa0,0x07,0x73,0x20,0x07,0x7a,0x30,0x07,0x72,0xd0,
    0x06,0xe9,0x60,0x07,0x74,0xa0,0x07,0x76,0x40,0x07,0x7a,0x60,0x07,0x74,0xd0,0x06,
    0xe6,0x30,0x07,0x72,0xa0,0x07,0x73,0x20,0x07,0x7a,0x30,0x07,0x72,0xd0,0x06,0xe6,
    0x60,0x07,0x74,0xa0,0x07,0x76,0x40,0x07,0x7a,0x60,0x07,0x74,0xd0,0x06,0xf6,0x10,
    0x07,0x76,0xa0,0x07,0x71,0x60,0x07,0x7a,0x10,0x07,0x76,0xd0,0x06,0xf6,0x20,0x07,
    0x74,0xa0,0x07,0x73,0x20,0x07,0x7a,0x30,0x07,0x72,0xd0,0x06,0xf6,0x30,0x07,0x72,
    0xa0,0x07,0x73,0x20,0x07,0x7a,0x30,0x07,0x72,0xd0,0x06,0xf6,0x40,0x07,0x78,0xa0,
    0x07,0x76,0x40,0x07,0x7a,0x60,0x07,0x74,0xd0,0x06,0xf6,0x60,0x07,0x74,0xa0,0x07,
    0x76,0x40,0x07,0x7a,0x60,0x07,0x74,0xd0,0x06,0xf6,0x90,0x07,0x76,0xa0,0x07,0x71,
    0x20,0x07,0x78,0xa0,0x07,0x71,0x20,0x07,0x78,0xd0,0x06,0xf6,0x10,0x07,0x72,0x80,
    0x07,0x7a,0x10,0x07,0x72,0x80,0x07,0x7a,0x10,0x07,0x72,0x80,0x07,0x6d,0x60,0x0f,
    0x71,0x90,0x07,0x72,0xa0,0x07,0x72,0x50,0x07,0x76,0xa0,0x07,0x72,0x50,0x07,0x76,
    0xd0,0x06,0xf6,0x20,0x07,0x75,0x60,0x07,0x7a,0x20,0x07,0x75,0x60,0x07,0x7a,0x20,
    0x07,0x75,0x60,0x07,0x6d,0x60,0x0f,0x75,0x10,0x07,0x72,0xa0,0x07,0x75,0x10,0x07,
    0x72,0xa0,0x07,0x75,0x10,0x07,0x72,0xd0,0x06,0xf6,0x10,0x07,0x70,0x20,0x07,0x74,
    0xa0,0x07,0x71,0x00,0x07,0x72,0x40,0x07,0x7a,0x10,0x07,0x70,0x20,0x07,0x74,0xd0,
    0x06,0xee,0x80,0x07,0x7a,0x10,0x07,0x76,0xa0,0x07,0x73,0x20,0x07,0x43,0x98,0x02,
    0x00,0x80,0x00,0x00,0x00,0x00,0x00,0x80,0x21,0xcc,0x01,0x04,0x80,0x00,0x00,0x00,
    0x00,0x00,0x40,0x16,0x08,0x00,0x00,0x00,0x08,0x00,0x00,0x00,0x32,0x1e,0x98,0x10,
    0x19,0x11,0x4c,0x90,0x8c,0x09,0x26,0x47,0xc6,0x04,0x43,0xca,0x12,0x18,0x01,0x28,
    0x82,0x42,0x28,0x08,0xd2,0xb1,0x04,0x09,0x00,0x00,0x00,0x00,0x79,0x18,0x00,0x00,
    0xb1,0x00,0x00,0x00,0x1a,0x03,0x4c,0x10,0x97,0x29,0xa2,0x25,0x10,0xab,0x32,0xb9,
    0xb9,0xb4,0x37,0xb7,0x21,0x46,0x42,0x20,0x80,0x72,0x50,0xb9,0x1b,0x43,0x0b,0x93,
    0xfb,0x9a,0x4b,0xd3,0x2b,0x1b,0x62,0x24,0x02,0x22,0x24,0x05,0xe3,0x20,0x08,0x0e,
    0x8e,0xad,0x0c,0xa4,0xad,0x8c,0x2e,0x8c,0x0d,0xc4,0xae,0x4c,0x6e,0x2e,0xed,0xcd,
    0x0d,0x64,0x46,0x06,0x46,0x66,0xc6,0x65,0x66,0xa6,0x06,0x04,0xa5,0xad,0x8c,0x2e,
    0x8c,0xcd,0xac,0xac,0x65,0x46,0x06,0x46,0x66,0xc6,0x65,0x66,0xa6,0x26,0x65,0x88,
    0x80,0x10,0x43,0x8c,0x44,0x48,0x8c,0x64,0x60,0xd1,0x54,0x46,0x17,0xc6,0x36,0x04,
    0x41,0x8e,0x44,0x48,0x84,0x64,0xe0,0x16,0x96,0x26,0xe7,0x32,0xf6,0xd6,0x06,0x97,
    0xc6,0x56,0xe6,0x42,0x56,0xe6,0xf6,0x26,0xd7,0x36,0xf7,0x45,0x96,0x36,0x17,0x26,
    0xc6,0x56,0x36,0x44,0x40,0x12,0x72,0x61,0x69,0x72,0x2e,0x63,0x6f,0x6d,0x70,0x69,
    0x6c,0x65,0x2e,0x66,0x61,0x73,0x74,0x5f,0x6d,0x61,0x74,0x68,0x5f,0x65,0x6e,0x61,
    0x62,0x6c,0x65,0x43,0x04,0x64,0x21,0x19,0x84,0xa5,0xc9,0xb9,0x8c,0xbd,0xb5,0xc1,
    0xa5,0xb1,0x95,0xb9,0x98,0xc9,0x85,0xb5,0x95,0x89,0xd5,0x99,0x99,0x95,0xc9,0x7d,
    0x99,0x95,0xd1,0x8d,0xa1,0x7d,0x95,0xb9,0x85,0x89,0xb1,0x95,0x0d,0x11,0x90,0x86,
    0x51,0x58,0x9a,0x9c,0x8b,0x5d,0x99,0x1c,0x5d,0x19,0xde,0xd7,0x5b,0x1d,0x1d,0x5c,
    0x1d,0x1d,0x97,0xba,0xb9,0x32,0x39,0x14,0xb6,0xb7,0x31,0x37,0x98,0x14,0x46,0x61,
    0x69,0x72,0x2e,0x61,0x72,0x67,0x5f,0x74,0x79,0x70,0x65,0x5f,0x6e,0x61,0x6d,0x65,
    0x34,0xcc,0xd8,0xde,0xc2,0xe8,0x64,0xc8,0x84,0xa5,0xc9,0xb9,0x84,0xc9,0x9d,0x7d,
    0xb9,0x85,0xb5,0x95,0x51,0xa8,0xb3,0x1b,0xc2,0x20,0x0f,0x02,0x21,0x11,0x22,0x21,
    0x13,0x42,0x71,0xa9,0x9b,0x2b,0x93,0x43,0x61,0x7b,0x1b,0x73,0x8b,0x49,0xa1,0x61,
    0xc6,0xf6,0x16,0x46,0x47,0xc3,0x62,0xec,0x8d,0xed,0x4d,0x6e,0x08,0x83,0x3c,0x88,
    0x85,0x44,0xc8,0x85,0x4c,0x08,0x46,0x26,0x2c,0x4d,0xce,0x05,0xee,0x6d,0x2e,0x8d,
    0x2e,0xed,0xcd,0x8d,0xcb,0x19,0xdb,0x17,0xd4,0xdb,0x5c,0x1a,0x5d,0xda,0x9b,0xdb,
    0x10,0x05,0xd1,0x90,0x08,0xb9,0x90,0x09,0xd9,0x86,0x18,0x48,0x85,0x64,0x08,0x47,
    0x28,0x2c,0x4d,0xce,0xc5,0xae,0x4c,0x8e,0xae,0x0c,0xef,0x2b,0xcd,0x0d,0xae,0x8e,
    0x8e,0x52,0x58,0x9a,0x9c,0x0b,0xdb,0xdb,0x58,0x18,0x5d,0xda,0x9b,0xdb,0x57,0x9a,
    0x1b,0x59,0x19,0x1e,0xbd,0xb3,0x32,0xb7,0x32,0xb9,0x30,0xba,0x32,0x32,0x94,0xaf,
    0xaf,0xb0,0x34,0xb9,0x2f,0x38,0xb6,0xb0,0xb1,0x32,0xb4,0x37,0x36,0xb2,0x32,0xb9,
    0xaf,0xaf,0x14,0x22,0x70,0x6f,0x73,0x69,0x74,0x69,0x6f,0x6e,0x43,0xa8,0x64,0x40,
    0x3c,0xe4,0x4b,0x86,0x44,0x40,0xc0,0x00,0x89,0x10,0x09,0x99,0x90,0x30,0x60,0x42,
    0x57,0x86,0x37,0xf6,0xf6,0x26,0x47,0x06,0x33,0x84,0x4a,0x04,0xc4,0x43,0xbe,0x44,
    0x48,0x04,0x04,0x0c,0x90,0x08,0x91,0x90,0x09,0x19,0x03,0x1a,0x63,0x6f,0x6c,0x6f,
    0x72,0x30,0x43,0xa8,0x84,0x40,0x3c,0xe4,0x4b,0x88,0x44,0x40,0xc0,0x00,0x89,0x90,
    0x0b,0x99,0x90,0x32,0x18,0x62,0x20,0x62,0x80,0x90,0x01,0x62,0x06,0x43,0x8c,0x02,
    0x40,0x3a,0xe4,0x0c,0x46,0x44,0xec,0xc0,0x0e,0xf6,0xd0,0x0e,0x6e,0xd0,0x0e,0xef,
    0x40,0x0e,0xf5,0xc0,0x0e,0xe5,0xe0,0x06,0xe6,0xc0,0x0e,0xe1,0x70,0x0e,0xf3,0x30,
    0x45,0x08,0x86,0x11,0x0a,0x3b,0xb0,0x83,0x3d,0xb4,0x83,0x1b,0xa4,0x03,0x39,0x94,
    0x83,0x3b,0xd0,0xc3,0x94,0xa0,0x18,0xb1,0x84,0x43,0x3a,0xc8,0x83,0x1b,0xd8,0x43,
    0x39,0xc8,0xc3,0x3c,0xa4,0xc3,0x3b,0xb8,0xc3,0x94,0xc0,0x18,0x41,0x85,0x43,0x3a,
    0xc8,0x83,0x1b,0xb0,0x43,0x38,0xb8,0xc3,0x39,0xd4,0x43,0x38,0x9c,0x43,0x39,0xfc,
    0x82,0x3d,0x94,0x83,0x3c,0xcc,0x43,0x3a,0xbc,0x83,0x3b,0x4c,0x09,0x90,0x11,0x53,
    0x38,0xa4,0x83,0x3c,0xb8,0xc1,0x38,0xbc,0x43,0x3b,0xc0,0x43,0x3a,0xb0,0x43,0x39,
    0xfc,0xc2,0x3b,0xc0,0x03,0x3d,0xa4,0xc3,0x3b,0xb8,0xc3,0x3c,0x4c,0x19,0x14,0xc6,
    0x19,0xa1,0x84,0x43,0x3a,0xc8,0x83,0x1b,0xd8,0x43,0x39,0xc8,0x03,0x3d,0x94,0x03,
    0x3e,0x4c,0x09,0xd0,0x00,0x00,0x00,0x00,0x79,0x18,0x00,0x00,0xa5,0x00,0x00,0x00,
    0x33,0x08,0x80,0x1c,0xc4,0xe1,0x1c,0x66,0x14,0x01,0x3d,0x88,0x43,0x38,0x84,0xc3,
    0x8c,0x42,0x80,0x07,0x79,0x78,0x07,0x73,0x98,0x71,0x0c,0xe6,0x00,0x0f,0xed,0x10,
    0x0e,0xf4,0x80,0x0e,0x33,0x0c,0x42,0x1e,0xc2,0xc1,0x1d,0xce,0xa1,0x1c,0x66,0x30,
    0x05,0x3d,0x88,0x43,0x38,0x84,0x83,0x1b,0xcc,0x03,0x3d,0xc8,0x43,0x3d,0x8c,0x03,
    0x3d,0xcc,0x78,0x8c,0x74,0x70,0x07,0x7b,0x08,0x07,0x79,0x48,0x87,0x70,0x70,0x07,
    0x7a,0x70,0x03,0x76,0x78,0x87,0x70,0x20,0x87,0x19,0xcc,0x11,0x0e,0xec,0x90,0x0e,
    0xe1,0x30,0x0f,0x6e,0x30,0x0f,0xe3,0xf0,0x0e,0xf0,0x50,0x0e,0x33,0x10,0xc4,0x1d,
    0xde,0x21,0x1c,0xd8,0x21,0x1d,0xc2,0x61,0x1e,0x66,0x30,0x89,0x3b,0xbc,0x83,0x3b,
    0xd0,0x43,0x39,0xb4,0x03,0x3c,0xbc,0x83,0x3c,0x84,0x03,0x3b,0xcc,0xf0,0x14,0x76,
    0x60,0x07,0x7b,0x68,0x07,0x37,0x68,0x87,0x72,0x68,0x07,0x37,0x80,0x87,0x70,0x90,
    0x87,0x70,0x60,0x07,0x76,0x28,0x07,0x76,0xf8,0x05,0x76,0x78,0x87,0x77,0x80,0x87,
    0x5f,0x08,0x87,0x71,0x18,0x87,0x72,0x98,0x87,0x79,0x98,0x81,0x2c,0xee,0xf0,0x0e,
    0xee,0xe0,0x0e,0xf5,0xc0,0x0e,0xec,0x30,0x03,0x62,0xc8,0xa1,0x1c,0xe4,0xa1,0x1c,
    0xcc,0xa1,0x1c,0xe4,0xa1,0x1c,0xdc,0x61,0x1c,0xca,0x21,0x1c,0xc4,0x81,0x1d,0xca,
    0x61,0x06,0xd6,0x90,0x43,0x39,0xc8,0x43,0x39,0x98,0x43,0x39,0xc8,0x43,0x39,0xb8,
    0xc3,0x38,0x94,0x43,0x38,0x88,0x03,0x3b,0x94,0xc3,0x2f,0xbc,0x83,0x3c,0xfc,0x82,
    0x3b,0xd4,0x03,0x3b,0xb0,0xc3,0x0c,0xc7,0x69,0x87,0x70,0x58,0x87,0x72,0x70,0x83,
    0x74,0x68,0x07,0x78,0x60,0x87,0x74,0x18,0x87,0x74,0xa0,0x87,0x19,0xce,0x53,0x0f,
    0xee,0x00,0x0f,0xf2,0x50,0x0e,0xe4,0x90,0x0e,0xe3,0x40,0x0f,0xe1,0x20,0x0e,0xec,
    0x50,0x0e,0x33,0x20,0x28,0x1d,0xdc,0xc1,0x1e,0xc2,0x41,0x1e,0xd2,0x21,0x1c,0xdc,
    0x81,0x1e,0xdc,0xe0,0x1c,0xe4,0xe1,0x1d,0xea,0x01,0x1e,0x66,0x18,0x51,0x38,0xb0,
    0x43,0x3a,0x9c,0x83,0x3b,0xcc,0x50,0x24,0x76,0x60,0x07,0x7b,0x68,0x07,0x37,0x60,
    0x87,0x77,0x78,0x07,0x78,0x98,0x51,0x4c,0xf4,0x90,0x0f,0xf0,0x50,0x0e,0x33,0x1e,
    0x6a,0x1e,0xca,0x61,0x1c,0xe8,0x21,0x1d,0xde,0xc1,0x1d,0x7e,0x01,0x1e,0xe4,0xa1,
    0x1c,0xcc,0x21,0x1d,0xf0,0x61,0x06,0x54,0x85,0x83,0x38,0xcc,0xc3,0x3b,0xb0,0x43,
    0x3d,0xd0,0x43,0x39,0xfc,0xc2,0x3c,0xe4,0x43,0x3b,0x88,0xc3,0x3b,0xb0,0xc3,0x8c,
    0xc5,0x0a,0x87,0x79,0x98,0x87,0x77,0x18,0x87,0x74,0x08,0x07,0x7a,0x28,0x07,0x72,
    0x98,0x81,0x5c,0xe3,0x10,0x0e,0xec,0xc0,0x0e,0xe5,0x50,0x0e,0xf3,0x30,0x23,0xc1,
    0xd2,0x41,0x1e,0xe4,0xe1,0x17,0xd8,0xe1,0x1d,0xde,0x01,0x1e,0x66,0x48,0x19,0x3b,
    0xb0,0x83,0x3d,0xb4,0x83,0x1b,0x84,0xc3,0x38,0x8c,0x43,0x39,0xcc,0xc3,0x3c,0xb8,
    0xc1,0x39,0xc8,0xc3,0x3b,0xd4,0x03,0x3c,0xcc,0x48,0xb4,0x71,0x08,0x07,0x76,0x60,
    0x07,0x71,0x08,0x87,0x71,0x58,0x87,0x19,0xdb,0xc6,0x0e,0xec,0x60,0x0f,0xed,0xe0,
    0x06,0xf0,0x20,0x0f,0xe5,0x30,0x0f,0xe5,0x20,0x0f,0xf6,0x50,0x0e,0x6e,0x10,0x0e,
    0xe3,0x30,0x0e,0xe5,0x30,0x0f,0xf3,0xe0,0x06,0xe9,0xe0,0x0e,0xe4,0x50,0x0e,0xf8,
    0x30,0x23,0xe2,0xec,0x61,0x1c,0xc2,0x81,0x1d,0xd8,0xe1,0x17,0xec,0x21,0x1d,0xe6,
    0x21,0x1d,0xc4,0x21,0x1d,0xd8,0x21,0x1d,0xe8,0x21,0x1f,0x66,0x20,0x9d,0x3b,0xbc,
    0x43,0x3d,0xb8,0x03,0x39,0x94,0x83,0x39,0xcc,0x58,0xbc,0x70,0x70,0x07,0x77,0x78,
    0x07,0x7a,0x08,0x07,0x7a,0x48,0x87,0x77,0x70,0x87,0x19,0xce,0x87,0x0e,0xe5,0x10,
    0x0e,0xf0,0x10,0x0e,0xec,0xc0,0x0e,0xef,0x30,0x0e,0xf3,0x90,0x0e,0xf4,0x50,0x0e,
    0x33,0x28,0x30,0x08,0x87,0x74,0x90,0x07,0x37,0x30,0x87,0x7a,0x70,0x87,0x71,0xa0,
    0x87,0x74,0x78,0x07,0x77,0xf8,0x85,0x73,0x90,0x87,0x77,0xa8,0x07,0x78,0x98,0x07,
    0x00,0x00,0x00,0x00,0x71,0x20,0x00,0x00,0x05,0x00,0x00,0x00,0x06,0x50,0x30,0x00,
    0xd2,0xd0,0x16,0xd0,0x00,0x48,0xe4,0x17,0x0c,0xe0,0x57,0x76,0x71,0xdb,0x00,0x00,
    0x61,0x20,0x00,0x00,0x1b,0x00,0x00,0x00,0x13,0x04,0x41,0x2c,0x10,0x00,0x00,0x00,
    0x10,0x00,0x00,0x00,0xb4,0x63,0x11,0x40,0x60,0x1c,0x73,0x10,0x83,0xd0,0x34,0x94,
    0x33,0x00,0x14,0x63,0x09,0x20,0x08,0x82,0x20,0x18,0x80,0x20,0x08,0x82,0xe0,0x30,
    0x96,0x00,0x82,0x20,0x88,0xff,0x02,0x08,0x82,0x20,0xfe,0xcd,0x00,0x90,0xcc,0x41,
    0x54,0x15,0x35,0xd1,0xcc,0x00,0x10,0x8c,0x11,0x80,0x20,0x08,0xe2,0xdf,0x08,0xc0,
    0x0c,0x00,0x00,0x00,0x23,0x06,0xc6,0x10,0x54,0x0e,0x72,0x0c,0x32,0x04,0xc7,0x32,
    0xc8,0x10,0x1c,0xcd,0x6c,0xc3,0x01,0x01,0xb3,0x0d,0x01,0x14,0xcc,0x36,0x04,0x83,
    0x90,0x01,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
/*
    #include <metal_stdlib>
    #include <simd/simd.h>

    using namespace metal;

    struct main0_out
    {
        float4 frag_color [[color(0)]];
    };

    struct main0_in
    {
        float2 uv [[user(locn0)]];
        float4 color [[user(locn1)]];
    };

    fragment main0_out main0(main0_in in [[stage_in]], texture2d<float> tex [[texture(0)]], sampler smp [[sampler(0)]])
    {
        main0_out out = {};
        out.frag_color = tex.sample(smp, in.uv).xxxx * in.color;
        return out;
    }

*/
static const uint8_t _sdtx_fs_bytecode_metal_ios[3033] = {
    0x4d,0x54,0x4c,0x42,0x01,0x00,0x02,0x00,0x02,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0xd9,0x0b,0x00,0x00,0x00,0x00,0x00,0x00,0x58,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x6d,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xc9,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x08,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xd1,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x08,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xd9,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x0b,0x00,0x00,0x00,0x00,0x00,0x00,0x01,0x00,0x00,0x00,0x6d,0x00,0x00,0x00,
    0x4e,0x41,0x4d,0x45,0x06,0x00,0x6d,0x61,0x69,0x6e,0x30,0x00,0x54,0x59,0x50,0x45,
    0x01,0x00,0x01,0x48,0x41,0x53,0x48,0x20,0x00,0x64,0xff,0xce,0xd3,0x48,0x71,0xaf,
    0x68,0xf3,0x79,0x4e,0x9f,0x9e,0xbf,0x16,0xda,0xcc,0x0f,0x60,0xbc,0x56,0x43,0x8f,
    0x4b,0x45,0xa7,0x93,0x35,0x40,0xfe,0x60,0x45,0x4f,0x46,0x46,0x54,0x18,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x56,0x45,0x52,0x53,0x08,0x00,0x01,0x00,0x08,
    0x00,0x01,0x00,0x01,0x00,0x45,0x4e,0x44,0x54,0x04,0x00,0x00,0x00,0x45,0x4e,0x44,
    0x54,0x04,0x00,0x00,0x00,0x45,0x4e,0x44,0x54,0xde,0xc0,0x17,0x0b,0x00,0x00,0x00,
    0x00,0x14,0x00,0x00,0x00,0xe4,0x0a,0x00,0x00,0xff,0xff,0xff,0xff,0x42,0x43,0xc0,
    0xde,0x21,0x0c,0x00,0x00,0xb6,0x02,0x00,0x00,0x0b,0x82,0x20,0x00,0x02,0x00,0x00,
    0x00,0x12,0x00,0x00,0x00,0x07,0x81,0x23,0x91,0x41,0xc8,0x04,0x49,0x06,0x10,0x32,
    0x39,0x92,0x01,0x84,0x0c,0x25,0x05,0x08,0x19,0x1e,0x04,0x8b,0x62,0x80,0x14,0x45,
    0x02,0x42,0x92,0x0b,0x42,0xa4,0x10,0x32,0x14,0x38,0x08,0x18,0x49,0x0a,0x32,0x44,
    0x24,0x48,0x0a,0x90,0x21,0x23,0xc4,0x52,0x80,0x0c,0x19,0x21,0x72,0x24,0x07,0xc8,
    0x48,0x11,0x62,0xa8,0xa0,0xa8,0x40,0xc6,0xf0,0x01,0x00,0x00,0x00,0x51,0x18,0x00,
    0x00,0x74,0x00,0x00,0x00,0x1b,0xc2,0x24,0xf8,0xff,0xff,0xff,0xff,0x01,0x60,0x00,
    0x09,0xa8,0x88,0x70,0x80,0x07,0x78,0x90,0x87,0x77,0xc0,0x87,0x36,0x30,0x87,0x7a,
    0x70,0x87,0x71,0x68,0x03,0x73,0x80,0x87,0x36,0x68,0x87,0x70,0xa0,0x07,0x74,0x00,
    0xe8,0x41,0x1e,0xea,0xa1,0x1c,0x00,0xa2,0x1d,0xd2,0xc1,0x1d,0xda,0x80,0x1d,0xca,
    0xe1,0x1c,0xc2,0x81,0x1d,0xda,0xc0,0x1e,0xca,0x61,0x1c,0xe8,0xe1,0x1d,0xe4,0xa1,
    0x0d,0xee,0x21,0x1d,0xc8,0x81,0x1e,0xd0,0x01,0x88,0x03,0x39,0xc0,0x03,0x60,0x70,
    0x87,0x77,0x68,0x03,0x71,0xa8,0x87,0x74,0x60,0x07,0x7a,0x48,0x07,0x77,0x98,0x07,
    0x80,0x70,0x87,0x77,0x68,0x03,0x73,0x90,0x87,0x70,0x68,0x87,0x72,0x68,0x03,0x78,
    0x78,0x87,0x74,0x70,0x07,0x7a,0x28,0x07,0x79,0x68,0x83,0x72,0x60,0x87,0x74,0x68,
    0x07,0x80,0x1e,0xe4,0xa1,0x1e,0xca,0x01,0x20,0xdc,0xe1,0x1d,0xda,0xc0,0x1c,0xe4,
    0x21,0x1c,0xda,0xa1,0x1c,0xda,0x00,0x1e,0xde,0x21,0x1d,0xdc,0x81,0x1e,0xca,0x41,
    0x1e,0xda,0xa0,0x1c,0xd8,0x21,0x1d,0xda,0xa1,0x0d,0xdc,0xe1,0x1d,0xdc,0xa1,0x0d,
    0xd8,0xa1,0x1c,0xc2,0xc1,0x1c,0x00,0x7a,0x90,0x87,0x7a,0x28,0x07,0x80,0x70,0x87,
    0x77,0x68,0x83,0x74,0x70,0x07,0x73,0x98,0x87,0x36,0x30,0x07,0x78,0x68,0x83,0x76,
    0x08,0x07,0x7a,0x40,0x07,0x80,0x1e,0xe4,0xa1,0x1e,0xca,0x01,0x20,0xdc,0xe1,0x1d,
    0xda,0xc0,0x1d,0xc2,0xc1,0x1d,0xe6,0xa1,0x0d,0xcc,0x01,0x1e,0xda,0xa0,0x1d,0xc2,
    0x81,0x1e,0xd0,0x01,0xa0,0x07,0x79,0xa8,0x87,0x72,0x00,0x08,0x77,0x78,0x87,0x36,
    0x98,0x87,0x74,0x38,0x07,0x77,0x28,0x07,0x72,0x68,0x03,0x7d,0x28,0x07,0x79,0x78,
    0x87,0x79,0x68,0x03,0x73,0x80,0x87,0x36,0x68,0x87,0x70,0xa0,0x07,0x74,0x00,0xe8,
    0x41,0x1e,0xea,0xa1,0x1c,0x00,0xc2,0x1d,0xde,0xa1,0x0d,0xe8,0x41,0x1e,0xc2,0x01,
    0x1e,0xe0,0x21,0x1d,0xdc,0xe1,0x1c,0xda,0xa0,0x1d,0xc2,0x81,0x1e,0xd0,0x01,0xa0,
    0x07,0x79,0xa8,0x87,0x72,0x00,0x88,0x79,0xa0,0x87,0x70,0x18,0x87,0x75,0x68,0x03,
    0x78,0x90,0x87,0x77,0xa0,0x87,0x72,0x18,0x07,0x7a,0x78,0x07,0x79,0x68,0x03,0x71,
    0xa8,0x07,0x73,0x30,0x87,0x72,0x90,0x87,0x36,0x98,0x87,0x74,0xd0,0x87,0x72,0x00,
    0xf0,0x00,0x20,0xea,0xc1,0x1d,0xe6,0x21,0x1c,0xcc,0xa1,0x1c,0xda,0xc0,0x1c,0xe0,
    0xa1,0x0d,0xda,0x21,0x1c,0xe8,0x01,0x1d,0x00,0x7a,0x90,0x87,0x7a,0x28,0x07,0x60,
    0x83,0x21,0x0c,0xc0,0x02,0x54,0x1b,0x8c,0x81,0x00,0x16,0xa0,0xda,0x80,0x10,0xff,
    0xff,0xff,0xff,0x3f,0x00,0x0c,0x20,0x01,0xd5,0x06,0xa3,0x08,0x80,0x05,0xa8,0x36,
    0x18,0x86,0x00,0x2c,0x40,0xb5,0x01,0x39,0xfe,0xff,0xff,0xff,0x7f,0x00,0x18,0x40,
    0x02,0x2a,0x00,0x00,0x00,0x49,0x18,0x00,0x00,0x04,0x00,0x00,0x00,0x13,0x86,0x40,
    0x18,0x26,0x0c,0x44,0x61,0x4c,0x18,0x8e,0xc2,0x00,0x00,0x00,0x00,0x89,0x20,0x00,
    0x00,0x1e,0x00,0x00,0x00,0x32,0x22,0x48,0x09,0x20,0x64,0x85,0x04,0x93,0x22,0xa4,
    0x84,0x04,0x93,0x22,0xe3,0x84,0xa1,0x90,0x14,0x12,0x4c,0x8a,0x8c,0x0b,0x84,0xa4,
    0x4c,0x10,0x4c,0x33,0x00,0xc3,0x08,0x04,0x60,0x83,0x30,0x8c,0x20,0x00,0x47,0x49,
    0x53,0x44,0x09,0x93,0xff,0x4f,0xc4,0x35,0x51,0x11,0xf1,0xdb,0xc3,0x3f,0x8d,0x11,
    0x00,0x83,0x08,0x44,0x70,0x91,0x34,0x45,0x94,0x30,0xf9,0xbf,0x04,0x30,0xcf,0x42,
    0x44,0xff,0x34,0x46,0x00,0x0c,0x22,0x18,0x42,0x29,0xc4,0x08,0xe5,0x10,0x9a,0x23,
    0x08,0xe6,0x08,0xc0,0x60,0x18,0x41,0x58,0x0a,0x12,0xca,0x19,0x8a,0x29,0x40,0x6d,
    0x20,0x20,0x05,0xd6,0x30,0x02,0xb1,0x8c,0x00,0x00,0x00,0x00,0x00,0x13,0xa8,0x70,
    0x48,0x07,0x79,0xb0,0x03,0x3a,0x68,0x83,0x70,0x80,0x07,0x78,0x60,0x87,0x72,0x68,
    0x83,0x74,0x78,0x87,0x79,0xc8,0x03,0x37,0x80,0x03,0x37,0x80,0x83,0x0d,0xb7,0x51,
    0x0e,0x6d,0x00,0x0f,0x7a,0x60,0x07,0x74,0xa0,0x07,0x76,0x40,0x07,0x7a,0x60,0x07,
    0x74,0xd0,0x06,0xe9,0x10,0x07,0x7a,0x80,0x07,0x7a,0x80,0x07,0x6d,0x90,0x0e,0x78,
    0xa0,0x07,0x78,0xa0,0x07,0x78,0xd0,0x06,0xe9,0x10,0x07,0x76,0xa0,0x07,0x71,0x60,
    0x07,0x7a,0x10,0x07,0x76,0xd0,0x06,0xe9,0x30,0x07,0x72,0xa0,0x07,0x73,0x20,0x07,
    0x7a,0x30,0x07,0x72,0xd0,0x06,0xe9,0x60,0x07,0x74,0xa0,0x07,0x76,0x40,0x07,0x7a,
    0x60,0x07,0x74,0xd0,0x06,0xe6,0x30,0x07,0x72,0xa0,0x07,0x73,0x20,0x07,0x7a,0x30,
    0x07,0x72,0xd0,0x06,0xe6,0x60,0x07,0x74,0xa0,0x07,0x76,0x40,0x07,0x7a,0x60,0x07,
    0x74,0xd0,0x06,0xf6,0x10,0x07,0x76,0xa0,0x07,0x71,0x60,0x07,0x7a,0x10,0x07,0x76,
    0xd0,0x06,0xf6,0x20,0x07,0x74,0xa0,0x07,0x73,0x20,0x07,0x7a,0x30,0x07,0x72,0xd0,
    0x06,0xf6,0x30,0x07,0x72,0xa0,0x07,0x73,0x20,0x07,0x7a,0x30,0x07,0x72,0xd0,0x06,
    0xf6,0x40,0x07,0x78,0xa0,0x07,0x76,0x40,0x07,0x7a,0x60,0x07,0x74,0xd0,0x06,0xf6,
    0x60,0x07,0x74,0xa0,0x07,0x76,0x40,0x07,0x7a,0x60,0x07,0x74,0xd0,0x06,0xf6,0x90,
    0x07,0x76,0xa0,0x07,0x71,0x20,0x07,0x78,0xa0,0x07,0x71,0x20,0x07,0x78,0xd0,0x06,
    0xf6,0x10,0x07,0x72,0x80,0x07,0x7a,0x10,0x07,0x72,0x80,0x07,0x7a,0x10,0x07,0x72,
    0x80,0x07,0x6d,0x60,0x0f,0x71,0x90,0x07,0x72,0xa0,0x07,0x72,0x50,0x07,0x76,0xa0,
    0x07,0x72,0x50,0x07,0x76,0xd0,0x06,0xf6,0x20,0x07,0x75,0x60,0x07,0x7a,0x20,0x07,
    0x75,0x60,0x07,0x7a,0x20,0x07,0x75,0x60,0x07,0x6d,0x60,0x0f,0x75,0x10,0x07,0x72,
    0xa0,0x07,0x75,0x10,0x07,0x72,0xa0,0x07,0x75,0x10,0x07,0x72,0xd0,0x06,0xf6,0x10,
    0x07,0x70,0x20,0x07,0x74,0xa0,0x07,0x71,0x00,0x07,0x72,0x40,0x07,0x7a,0x10,0x07,
    0x70,0x20,0x07,0x74,0xd0,0x06,0xee,0x80,0x07,0x7a,0x10,0x07,0x76,0xa0,0x07,0x73,
    0x20,0x07,0x43,0x98,0x04,0x00,0x80,0x00,0x00,0x00,0x00,0x00,0x80,0x21,0x8c,0x03,
    0x04,0x80,0x00,0x00,0x00,0x00,0x00,0x40,0x16,0x08,0x00,0x00,0x00,0x08,0x00,0x00,
    0x00,0x32,0x1e,0x98,0x10,0x19,0x11,0x4c,0x90,0x8c,0x09,0x26,0x47,0xc6,0x04,0x43,
    0x5a,0x25,0x30,0x02,0x50,0x04,0x85,0x50,0x10,0x65,0x40,0x70,0x2c,0x41,0x02,0x00,
    0x00,0x79,0x18,0x00,0x00,0xd2,0x00,0x00,0x00,0x1a,0x03,0x4c,0x10,0x97,0x29,0xa2,
    0x25,0x10,0xab,0x32,0xb9,0xb9,0xb4,0x37,0xb7,0x21,0xc6,0x42,0x3c,0x00,0x84,0x50,
    0xb9,0x1b,0x43,0x0b,0x93,0xfb,0x9a,0x4b,0xd3,0x2b,0x1b,0x62,0x2c,0xc2,0x23,0x2c,
    0x05,0xe3,0x20,0x08,0x0e,0x8e,0xad,0x0c,0xa4,0xad,0x8c,0x2e,0x8c,0x0d,0xc4,0xae,
    0x4c,0x6e,0x2e,0xed,0xcd,0x0d,0x64,0x46,0x06,0x46,0x66,0xc6,0x65,0x66,0xa6,0x06,
    0x04,0xa5,0xad,0x8c,0x2e,0x8c,0xcd,0xac,0xac,0x65,0x46,0x06,0x46,0x66,0xc6,0x65,
    0x66,0xa6,0x26,0x65,0x88,0xf0,0x10,0x43,0x8c,0x45,0x58,0x8c,0x65,0x60,0xd1,0x54,
    0x46,0x17,0xc6,0x36,0x04,0x79,0x8e,0x45,0x58,0x84,0x65,0xe0,0x16,0x96,0x26,0xe7,
    0x32,0xf6,0xd6,0x06,0x97,0xc6,0x56,0xe6,0x42,0x56,0xe6,0xf6,0x26,0xd7,0x36,0xf7,
    0x45,0x96,0x36,0x17,0x26,0xc6,0x56,0x36,0x44,0x78,0x12,0x72,0x61,0x69,0x72,0x2e,
    0x63,0x6f,0x6d,0x70,0x69,0x6c,0x65,0x2e,0x66,0x61,0x73,0x74,0x5f,0x6d,0x61,0x74,
    0x68,0x5f,0x65,0x6e,0x61,0x62,0x6c,0x65,0x43,0x84,0x67,0x21,0x19,0x84,0xa5,0xc9,
    0xb9,0x8c,0xbd,0xb5,0xc1,0xa5,0xb1,0x95,0xb9,0x98,0xc9,0x85,0xb5,0x95,0x89,0xd5,
    0x99,0x99,0x95,0xc9,0x7d,0x99,0x95,0xd1,0x8d,0xa1,0x7d,0x95,0xb9,0x85,0x89,0xb1,
    0x95,0x0d,0x11,0x9e,0x86,0x51,0x58,0x9a,0x9c,0x8b,0x5c,0x99,0x1b,0x59,0x99,0xdc,
    0x17,0x5d,0x98,0xdc,0x59,0x19,0x1d,0xa3,0xb0,0x34,0x39,0x97,0x30,0xb9,0xb3,0x2f,
    0xba,0x3c,0xb8,0xb2,0x2f,0xb7,0xb0,0xb6,0x32,0x1a,0x66,0x6c,0x6f,0x61,0x74,0x34,
    0x64,0xc2,0xd2,0xe4,0x5c,0xc2,0xe4,0xce,0xbe,0xdc,0xc2,0xda,0xca,0xa8,0x98,0xc9,
    0x85,0x9d,0x7d,0x8d,0xbd,0xb1,0xbd,0xc9,0x0d,0x61,0x9e,0x67,0x19,0x1e,0xe8,0x89,
    0x1e,0xe9,0x99,0x86,0x08,0x0f,0x45,0x29,0x2c,0x4d,0xce,0xc5,0x4c,0x2e,0xec,0xac,
    0xad,0xcc,0x8d,0xee,0x2b,0xcd,0x0d,0xae,0x8e,0x8e,0x4b,0xdd,0x5c,0x99,0x1c,0x0a,
    0xdb,0xdb,0x98,0x1b,0x4c,0x0a,0x95,0xb0,0x34,0x39,0x97,0xb1,0x32,0x37,0xba,0x32,
    0x39,0x3e,0x61,0x69,0x72,0x2e,0x70,0x65,0x72,0x73,0x70,0x65,0x63,0x74,0x69,0x76,
    0x65,0x34,0xcc,0xd8,0xde,0xc2,0xe8,0x64,0x28,0xd4,0xd9,0x0d,0x91,0x96,0xe1,0xb1,
    0x9e,0xeb,0xc1,0x9e,0xec,0x81,0x1e,0xed,0x91,0x9e,0x8d,0x4b,0xdd,0x5c,0x99,0x1c,
    0x0a,0xdb,0xdb,0x98,0x5b,0x4c,0x0a,0x8b,0xb1,0x37,0xb6,0x37,0xb9,0x21,0xd2,0x22,
    0x3c,0xd6,0xd3,0x3d,0xd8,0x93,0x3d,0xd0,0x13,0x3d,0xd2,0xe3,0x71,0x09,0x4b,0x93,
    0x73,0xa1,0x2b,0xc3,0xa3,0xab,0x93,0x2b,0xa3,0x14,0x96,0x26,0xe7,0xc2,0xf6,0x36,
    0x16,0x46,0x97,0xf6,0xe6,0xf6,0x95,0xe6,0x46,0x56,0x86,0x47,0x25,0x2c,0x4d,0xce,
    0x65,0x2e,0xac,0x0d,0x8e,0xad,0x8c,0x18,0x5d,0x19,0x1e,0x5d,0x9d,0x5c,0x99,0x0c,
    0x19,0x8f,0x19,0xdb,0x5b,0x18,0x1d,0x0b,0xc8,0x5c,0x58,0x1b,0x1c,0x5b,0x99,0x0f,
    0x07,0xba,0x32,0xbc,0x21,0xd4,0x42,0x3c,0x60,0xf0,0x84,0xc1,0x32,0x2c,0xc2,0x23,
    0x06,0x0f,0xf4,0x8c,0xc1,0x23,0x3d,0x64,0xc0,0x25,0x2c,0x4d,0xce,0x65,0x2e,0xac,
    0x0d,0x8e,0xad,0x4c,0x8e,0xc7,0x5c,0x58,0x1b,0x1c,0x5b,0x99,0x1c,0x87,0xb9,0x36,
    0xb8,0x21,0xd2,0x72,0x3c,0x66,0xf0,0x84,0xc1,0x32,0x2c,0xc2,0x03,0x3d,0x67,0xf0,
    0x48,0x0f,0x1a,0x0c,0x41,0x1e,0xee,0xf9,0x9e,0x32,0x78,0xd2,0x60,0x88,0x91,0x00,
    0x4f,0xf5,0xa8,0x01,0xaf,0xb0,0x34,0xb9,0x96,0x30,0xb6,0xb4,0xb0,0xb9,0x96,0xb9,
    0xb1,0x37,0xb8,0xb2,0x39,0x94,0xb6,0xb0,0x34,0x37,0x98,0x94,0x21,0xc4,0xd3,0x06,
    0x0f,0x1b,0x10,0x0b,0x4b,0x93,0x6b,0x09,0x63,0x4b,0x0b,0x9b,0x6b,0x99,0x1b,0x7b,
    0x83,0x2b,0x6b,0xa1,0x2b,0xc3,0xa3,0xab,0x93,0x2b,0x9b,0x1b,0x62,0x3c,0x6f,0xf0,
    0xb4,0xc1,0xe3,0x06,0xc4,0xc2,0xd2,0xe4,0x5a,0xc2,0xd8,0xd2,0xc2,0xe6,0x5a,0xe6,
    0xc6,0xde,0xe0,0xca,0x5a,0xe6,0xc2,0xda,0xe0,0xd8,0xca,0xe4,0xe6,0x86,0x18,0x4f,
    0x1c,0x3c,0x6d,0xf0,0xc0,0xc1,0x10,0xe2,0x79,0x83,0x27,0x0e,0x46,0x44,0xec,0xc0,
    0x0e,0xf6,0xd0,0x0e,0x6e,0xd0,0x0e,0xef,0x40,0x0e,0xf5,0xc0,0x0e,0xe5,0xe0,0x06,
    0xe6,0xc0,0x0e,0xe1,0x70,0x0e,0xf3,0x30,0x45,0x08,0x86,0x11,0x0a,0x3b,0xb0,0x83,
    0x3d,0xb4,0x83,0x1b,0xa4,0x03,0x39,0x94,0x83,0x3b,0xd0,0xc3,0x94,0xa0,0x18,0xb1,
    0x84,0x43,0x3a,0xc8,0x83,0x1b,0xd8,0x43,0x39,0xc8,0xc3,0x3c,0xa4,0xc3,0x3b,0xb8,
    0xc3,0x94,0xc0,0x18,0x41,0x85,0x43,0x3a,0xc8,0x83,0x1b,0xb0,0x43,0x38,0xb8,0xc3,
    0x39,0xd4,0x43,0x38,0x9c,0x43,0x39,0xfc,0x82,0x3d,0x94,0x83,0x3c,0xcc,0x43,0x3a,
    0xbc,0x83,0x3b,0x4c,0x09,0x90,0x11,0x53,0x38,0xa4,0x83,0x3c,0xb8,0xc1,0x38,0xbc,
    0x43,0x3b,0xc0,0x43,0x3a,0xb0,0x43,0x39,0xfc,0xc2,0x3b,0xc0,0x03,0x3d,0xa4,0xc3,
    0x3b,0xb8,0xc3,0x3c,0x4c,0x19,0x14,0xc6,0x19,0xc1,0x84,0x43,0x3a,0xc8,0x83,0x1b,
    0x98,0x83,0x3c,0x84,0xc3,0x39,0xb4,0x43,0x39,0xb8,0x03,0x3d,0x4c,0x09,0xd6,0x00,
    0x00,0x79,0x18,0x00,0x00,0xa5,0x00,0x00,0x00,0x33,0x08,0x80,0x1c,0xc4,0xe1,0x1c,
    0x66,0x14,0x01,0x3d,0x88,0x43,0x38,0x84,0xc3,0x8c,0x42,0x80,0x07,0x79,0x78,0x07,
    0x73,0x98,0x71,0x0c,0xe6,0x00,0x0f,0xed,0x10,0x0e,0xf4,0x80,0x0e,0x33,0x0c,0x42,
    0x1e,0xc2,0xc1,0x1d,0xce,0xa1,0x1c,0x66,0x30,0x05,0x3d,0x88,0x43,0x38,0x84,0x83,
    0x1b,0xcc,0x03,0x3d,0xc8,0x43,0x3d,0x8c,0x03,0x3d,0xcc,0x78,0x8c,0x74,0x70,0x07,
    0x7b,0x08,0x07,0x79,0x48,0x87,0x70,0x70,0x07,0x7a,0x70,0x03,0x76,0x78,0x87,0x70,
    0x20,0x87,0x19,0xcc,0x11,0x0e,0xec,0x90,0x0e,0xe1,0x30,0x0f,0x6e,0x30,0x0f,0xe3,
    0xf0,0x0e,0xf0,0x50,0x0e,0x33,0x10,0xc4,0x1d,0xde,0x21,0x1c,0xd8,0x21,0x1d,0xc2,
    0x61,0x1e,0x66,0x30,0x89,0x3b,0xbc,0x83,0x3b,0xd0,0x43,0x39,0xb4,0x03,0x3c,0xbc,
    0x83,0x3c,0x84,0x03,0x3b,0xcc,0xf0,0x14,0x76,0x60,0x07,0x7b,0x68,0x07,0x37,0x68,
    0x87,0x72,0x68,0x07,0x37,0x80,0x87,0x70,0x90,0x87,0x70,0x60,0x07,0x76,0x28,0x07,
    0x76,0xf8,0x05,0x76,0x78,0x87,0x77,0x80,0x87,0x5f,0x08,0x87,0x71,0x18,0x87,0x72,
    0x98,0x87,0x79,0x98,0x81,0x2c,0xee,0xf0,0x0e,0xee,0xe0,0x0e,0xf5,0xc0,0x0e,0xec,
    0x30,0x03,0x62,0xc8,0xa1,0x1c,0xe4,0xa1,0x1c,0xcc,0xa1,0x1c,0xe4,0xa1,0x1c,0xdc,
    0x61,0x1c,0xca,0x21,0x1c,0xc4,0x81,0x1d,0xca,0x61,0x06,0xd6,0x90,0x43,0x39,0xc8,
    0x43,0x39,0x98,0x43,0x39,0xc8,0x43,0x39,0xb8,0xc3,0x38,0x94,0x43,0x38,0x88,0x03,
    0x3b,0x94,0xc3,0x2f,0xbc,0x83,0x3c,0xfc,0x82,0x3b,0xd4,0x03,0x3b,0xb0,0xc3,0x0c,
    0xc7,0x69,0x87,0x70,0x58,0x87,0x72,0x70,0x83,0x74,0x68,0x07,0x78,0x60,0x87,0x74,
    0x18,0x87,0x74,0xa0,0x87,0x19,0xce,0x53,0x0f,0xee,0x00,0x0f,0xf2,0x50,0x0e,0xe4,
    0x90,0x0e,0xe3,0x40,0x0f,0xe1,0x20,0x0e,0xec,0x50,0x0e,0x33,0x20,0x28,0x1d,0xdc,
    0xc1,0x1e,0xc2,0x41,0x1e,0xd2,0x21,0x1c,0xdc,0x81,0x1e,0xdc,0xe0,0x1c,0xe4,0xe1,
    0x1d,0xea,0x01,0x1e,0x66,0x18,0x51,0x38,0xb0,0x43,0x3a,0x9c,0x83,0x3b,0xcc,0x50,
    0x24,0x76,0x60,0x07,0x7b,0x68,0x07,0x37,0x60,0x87,0x77,0x78,0x07,0x78,0x98,0x51,
    0x4c,0xf4,0x90,0x0f,0xf0,0x50,0x0e,0x33,0x1e,0x6a,0x1e,0xca,0x61,0x1c,0xe8,0x21,
    0x1d,0xde,0xc1,0x1d,0x7e,0x01,0x1e,0xe4,0xa1,0x1c,0xcc,0x21,0x1d,0xf0,0x61,0x06,
    0x54,0x85,0x83,0x38,0xcc,0xc3,0x3b,0xb0,0x43,0x3d,0xd0,0x43,0x39,0xfc,0xc2,0x3c,
    0xe4,0x43,0x3b,0x88,0xc3,0x3b,0xb0,0xc3,0x8c,0xc5,0x0a,0x87,0x79,0x98,0x87,0x77,
    0x18,0x87,0x74,0x08,0x07,0x7a,0x28,0x07,0x72,0x98,0x81,0x5c,0xe3,0x10,0x0e,0xec,
    0xc0,0x0e,0xe5,0x50,0x0e,0xf3,0x30,0x23,0xc1,0xd2,0x41,0x1e,0xe4,0xe1,0x17,0xd8,
    0xe1,0x1d,0xde,0x01,0x1e,0x66,0x48,0x19,0x3b,0xb0,0x83,0x3d,0xb4,0x83,0x1b,0x84,
    0xc3,0x38,0x8c,0x43,0x39,0xcc,0xc3,0x3c,0xb8,0xc1,0x39,0xc8,0xc3,0x3b,0xd4,0x03,
    0x3c,0xcc,0x48,0xb4,0x71,0x08,0x07,0x76,0x60,0x07,0x71,0x08,0x87,0x71,0x58,0x87,
    0x19,0xdb,0xc6,0x0e,0xec,0x60,0x0f,0xed,0xe0,0x06,0xf0,0x20,0x0f,0xe5,0x30,0x0f,
    0xe5,0x20,0x0f,0xf6,0x50,0x0e,0x6e,0x10,0x0e,0xe3,0x30,0x0e,0xe5,0x30,0x0f,0xf3,
    0xe0,0x06,0xe9,0xe0,0x0e,0xe4,0x50,0x0e,0xf8,0x30,0x23,0xe2,0xec,0x61,0x1c,0xc2,
    0x81,0x1d,0xd8,0xe1,0x17,0xec,0x21,0x1d,0xe6,0x21,0x1d,0xc4,0x21,0x1d,0xd8,0x21,
    0x1d,0xe8,0x21,0x1f,0x66,0x20,0x9d,0x3b,0xbc,0x43,0x3d,0xb8,0x03,0x39,0x94,0x83,
    0x39,0xcc,0x58,0xbc,0x70,0x70,0x07,0x77,0x78,0x07,0x7a,0x08,0x07,0x7a,0x48,0x87,
    0x77,0x70,0x87,0x19,0xce,0x87,0x0e,0xe5,0x10,0x0e,0xf0,0x10,0x0e,0xec,0xc0,0x0e,
    0xef,0x30,0x0e,0xf3,0x90,0x0e,0xf4,0x50,0x0e,0x33,0x28,0x30,0x08,0x87,0x74,0x90,
    0x07,0x37,0x30,0x87,0x7a,0x70,0x87,0x71,0xa0,0x87,0x74,0x78,0x07,0x77,0xf8,0x85,
    0x73,0x90,0x87,0x77,0xa8,0x07,0x78,0x98,0x07,0x00,0x00,0x00,0x00,0x71,0x20,0x00,
    0x00,0x08,0x00,0x00,0x00,0x16,0xb0,0x01,0x48,0xe4,0x4b,0x00,0xf3,0x2c,0xc4,0x3f,
    0x11,0xd7,0x44,0x45,0xc4,0x6f,0x0f,0x7e,0x85,0x17,0xb7,0x6d,0x00,0x05,0x03,0x20,
    0x0d,0x0d,0x00,0x00,0x00,0x61,0x20,0x00,0x00,0x14,0x00,0x00,0x00,0x13,0x04,0x41,
    0x2c,0x10,0x00,0x00,0x00,0x06,0x00,0x00,0x00,0x14,0x47,0x00,0x88,0x8d,0x00,0x90,
    0x1a,0x01,0xa8,0x01,0x12,0x33,0x00,0x14,0x66,0x00,0x08,0x8c,0x00,0x00,0x00,0x00,
    0x00,0x23,0x06,0xca,0x10,0x4c,0x09,0xb2,0x10,0x46,0x11,0x0c,0x32,0x04,0x03,0x62,
    0x01,0x23,0x9f,0xd9,0x06,0x23,0x00,0x32,0x08,0x88,0x01,0x00,0x00,0x02,0x00,0x00,
    0x00,0x5b,0x06,0xe0,0x90,0x03,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
/*
    #include <metal_stdlib>
    #include <simd/simd.h>

    using namespace metal;

    struct main0_out
    {
        float2 uv [[user(locn0)]];
        float4 color [[user(locn1)]];
        float4 gl_Position [[position]];
    };

    struct main0_in
    {
        float2 position [[attribute(0)]];
        float2 texcoord0 [[attribute(1)]];
        float4 color0 [[attribute(2)]];
    };

    vertex main0_out main0(main0_in in [[stage_in]])
    {
        main0_out out = {};
        out.gl_Position = float4(fma(in.position, float2(2.0, -2.0), float2(-1.0, 1.0)), 0.0, 1.0);
        out.uv = in.texcoord0;
        out.color = in.color0;
        return out;
    }

*/
static const uint8_t _sdtx_vs_source_metal_sim[577] = {
    0x23,0x69,0x6e,0x63,0x6c,0x75,0x64,0x65,0x20,0x3c,0x6d,0x65,0x74,0x61,0x6c,0x5f,
    0x73,0x74,0x64,0x6c,0x69,0x62,0x3e,0x0a,0x23,0x69,0x6e,0x63,0x6c,0x75,0x64,0x65,
    0x20,0x3c,0x73,0x69,0x6d,0x64,0x2f,0x73,0x69,0x6d,0x64,0x2e,0x68,0x3e,0x0a,0x0a,
    0x75,0x73,0x69,0x6e,0x67,0x20,0x6e,0x61,0x6d,0x65,0x73,0x70,0x61,0x63,0x65,0x20,
    0x6d,0x65,0x74,0x61,0x6c,0x3b,0x0a,0x0a,0x73,0x74,0x72,0x75,0x63,0x74,0x20,0x6d,
    0x61,0x69,0x6e,0x30,0x5f,0x6f,0x75,0x74,0x0a,0x7b,0x0a,0x20,0x20,0x20,0x20,0x66,
    0x6c,0x6f,0x61,0x74,0x32,0x20,0x75,0x76,0x20,0x5b,0x5b,0x75,0x73,0x65,0x72,0x28,
    0x6c,0x6f,0x63,0x6e,0x30,0x29,0x5d,0x5d,0x3b,0x0a,0x20,0x20,0x20,0x20,0x66,0x6c,
    0x6f,0x61,0x74,0x34,0x20,0x63,0x6f,0x6c,0x6f,0x72,0x20,0x5b,0x5b,0x75,0x73,0x65,
    0x72,0x28,0x6c,0x6f,0x63,0x6e,0x31,0x29,0x5d,0x5d,0x3b,0x0a,0x20,0x20,0x20,0x20,
    0x66,0x6c,0x6f,0x61,0x74,0x34,0x20,0x67,0x6c,0x5f,0x50,0x6f,0x73,0x69,0x74,0x69,
    0x6f,0x6e,0x20,0x5b,0x5b,0x70,0x6f,0x73,0x69,0x74,0x69,0x6f,0x6e,0x5d,0x5d,0x3b,
    0x0a,0x7d,0x3b,0x0a,0x0a,0x73,0x74,0x72,0x75,0x63,0x74,0x20,0x6d,0x61,0x69,0x6e,
    0x30,0x5f,0x69,0x6e,0x0a,0x7b,0x0a,0x20,0x20,0x20,0x20,0x66,0x6c,0x6f,0x61,0x74,
    0x32,0x20,0x70,0x6f,0x73,0x69,0x74,0x69,0x6f,0x6e,0x20,0x5b,0x5b,0x61,0x74,0x74,
    0x72,0x69,0x62,0x75,0x74,0x65,0x28,0x30,0x29,0x5d,0x5d,0x3b,0x0a,0x20,0x20,0x20,
    0x20,0x66,0x6c,0x6f,0x61,0x74,0x32,0x20,0x74,0x65,0x78,0x63,0x6f,0x6f,0x72,0x64,
    0x30,0x20,0x5b,0x5b,0x61,0x74,0x74,0x72,0x69,0x62,0x75,0x74,0x65,0x28,0x31,0x29,
    0x5d,0x5d,0x3b,0x0a,0x20,0x20,0x20,0x20,0x66,0x6c,0x6f,0x61,0x74,0x34,0x20,0x63,
    0x6f,0x6c,0x6f,0x72,0x30,0x20,0x5b,0x5b,0x61,0x74,0x74,0x72,0x69,0x62,0x75,0x74,
    0x65,0x28,0x32,0x29,0x5d,0x5d,0x3b,0x0a,0x7d,0x3b,0x0a,0x0a,0x76,0x65,0x72,0x74,
    0x65,0x78,0x20,0x6d,0x61,0x69,0x6e,0x30,0x5f,0x6f,0x75,0x74,0x20,0x6d,0x61,0x69,
    0x6e,0x30,0x28,0x6d,0x61,0x69,0x6e,0x30,0x5f,0x69,0x6e,0x20,0x69,0x6e,0x20,0x5b,
    0x5b,0x73,0x74,0x61,0x67,0x65,0x5f,0x69,0x6e,0x5d,0x5d,0x29,0x0a,0x7b,0x0a,0x20,
    0x20,0x20,0x20,0x6d,0x61,0x69,0x6e,0x30,0x5f,0x6f,0x75,0x74,0x20,0x6f,0x75,0x74,
    0x20,0x3d,0x20,0x7b,0x7d,0x3b,0x0a,0x20,0x20,0x20,0x20,0x6f,0x75,0x74,0x2e,0x67,
    0x6c,0x5f,0x50,0x6f,0x73,0x69,0x74,0x69,0x6f,0x6e,0x20,0x3d,0x20,0x66,0x6c,0x6f,
    0x61,0x74,0x34,0x28,0x66,0x6d,0x61,0x28,0x69,0x6e,0x2e,0x70,0x6f,0x73,0x69,0x74,
    0x69,0x6f,0x6e,0x2c,0x20,0x66,0x6c,0x6f,0x61,0x74,0x32,0x28,0x32,0x2e,0x30,0x2c,
    0x20,0x2d,0x32,0x2e,0x30,0x29,0x2c,0x20,0x66,0x6c,0x6f,0x61,0x74,0x32,0x28,0x2d,
    0x31,0x2e,0x30,0x2c,0x20,0x31,0x2e,0x30,0x29,0x29,0x2c,0x20,0x30,0x2e,0x30,0x2c,
    0x20,0x31,0x2e,0x30,0x29,0x3b,0x0a,0x20,0x20,0x20,0x20,0x6f,0x75,0x74,0x2e,0x75,
    0x76,0x20,0x3d,0x20,0x69,0x6e,0x2e,0x74,0x65,0x78,0x63,0x6f,0x6f,0x72,0x64,0x30,
    0x3b,0x0a,0x20,0x20,0x20,0x20,0x6f,0x75,0x74,0x2e,0x63,0x6f,0x6c,0x6f,0x72,0x20,
    0x3d,0x20,0x69,0x6e,0x2e,0x63,0x6f,0x6c,0x6f,0x72,0x30,0x3b,0x0a,0x20,0x20,0x20,
    0x20,0x72,0x65,0x74,0x75,0x72,0x6e,0x20,0x6f,0x75,0x74,0x3b,0x0a,0x7d,0x0a,0x0a,
    0x00,
};
/*
    #include <metal_stdlib>
    #include <simd/simd.h>

    using namespace metal;

    struct main0_out
    {
        float4 frag_color [[color(0)]];
    };

    struct main0_in
    {
        float2 uv [[user(locn0)]];
        float4 color [[user(locn1)]];
    };

    fragment main0_out main0(main0_in in [[stage_in]], texture2d<float> tex [[texture(0)]], sampler smp [[sampler(0)]])
    {
        main0_out out = {};
        out.frag_color = tex.sample(smp, in.uv).xxxx * in.color;
        return out;
    }

*/
static const uint8_t _sdtx_fs_source_metal_sim[441] = {
    0x23,0x69,0x6e,0x63,0x6c,0x75,0x64,0x65,0x20,0x3c,0x6d,0x65,0x74,0x61,0x6c,0x5f,
    0x73,0x74,0x64,0x6c,0x69,0x62,0x3e,0x0a,0x23,0x69,0x6e,0x63,0x6c,0x75,0x64,0x65,
    0x20,0x3c,0x73,0x69,0x6d,0x64,0x2f,0x73,0x69,0x6d,0x64,0x2e,0x68,0x3e,0x0a,0x0a,
    0x75,0x73,0x69,0x6e,0x67,0x20,0x6e,0x61,0x6d,0x65,0x73,0x70,0x61,0x63,0x65,0x20,
    0x6d,0x65,0x74,0x61,0x6c,0x3b,0x0a,0x0a,0x73,0x74,0x72,0x75,0x63,0x74,0x20,0x6d,
    0x61,0x69,0x6e,0x30,0x5f,0x6f,0x75,0x74,0x0a,0x7b,0x0a,0x20,0x20,0x20,0x20,0x66,
    0x6c,0x6f,0x61,0x74,0x34,0x20,0x66,0x72,0x61,0x67,0x5f,0x63,0x6f,0x6c,0x6f,0x72,
    0x20,0x5b,0x5b,0x63,0x6f,0x6c,0x6f,0x72,0x28,0x30,0x29,0x5d,0x5d,0x3b,0x0a,0x7d,
    0x3b,0x0a,0x0a,0x73,0x74,0x72,0x75,0x63,0x74,0x20,0x6d,0x61,0x69,0x6e,0x30,0x5f,
    0x69,0x6e,0x0a,0x7b,0x0a,0x20,0x20,0x20,0x20,0x66,0x6c,0x6f,0x61,0x74,0x32,0x20,
    0x75,0x76,0x20,0x5b,0x5b,0x75,0x73,0x65,0x72,0x28,0x6c,0x6f,0x63,0x6e,0x30,0x29,
    0x5d,0x5d,0x3b,0x0a,0x20,0x20,0x20,0x20,0x66,0x6c,0x6f,0x61,0x74,0x34,0x20,0x63,
    0x6f,0x6c,0x6f,0x72,0x20,0x5b,0x5b,0x75,0x73,0x65,0x72,0x28,0x6c,0x6f,0x63,0x6e,
    0x31,0x29,0x5d,0x5d,0x3b,0x0a,0x7d,0x3b,0x0a,0x0a,0x66,0x72,0x61,0x67,0x6d,0x65,
    0x6e,0x74,0x20,0x6d,0x61,0x69,0x6e,0x30,0x5f,0x6f,0x75,0x74,0x20,0x6d,0x61,0x69,
    0x6e,0x30,0x28,0x6d,0x61,0x69,0x6e,0x30,0x5f,0x69,0x6e,0x20,0x69,0x6e,0x20,0x5b,
    0x5b,0x73,0x74,0x61,0x67,0x65,0x5f,0x69,0x6e,0x5d,0x5d,0x2c,0x20,0x74,0x65,0x78,
    0x74,0x75,0x72,0x65,0x32,0x64,0x3c,0x66,0x6c,0x6f,0x61,0x74,0x3e,0x20,0x74,0x65,
    0x78,0x20,0x5b,0x5b,0x74,0x65,0x78,0x74,0x75,0x72,0x65,0x28,0x30,0x29,0x5d,0x5d,
    0x2c,0x20,0x73,0x61,0x6d,0x70,0x6c,0x65,0x72,0x20,0x73,0x6d,0x70,0x20,0x5b,0x5b,
    0x73,0x61,0x6d,0x70,0x6c,0x65,0x72,0x28,0x30,0x29,0x5d,0x5d,0x29,0x0a,0x7b,0x0a,
    0x20,0x20,0x20,0x20,0x6d,0x61,0x69,0x6e,0x30,0x5f,0x6f,0x75,0x74,0x20,0x6f,0x75,
    0x74,0x20,0x3d,0x20,0x7b,0x7d,0x3b,0x0a,0x20,0x20,0x20,0x20,0x6f,0x75,0x74,0x2e,
    0x66,0x72,0x61,0x67,0x5f,0x63,0x6f,0x6c,0x6f,0x72,0x20,0x3d,0x20,0x74,0x65,0x78,
    0x2e,0x73,0x61,0x6d,0x70,0x6c,0x65,0x28,0x73,0x6d,0x70,0x2c,0x20,0x69,0x6e,0x2e,
    0x75,0x76,0x29,0x2e,0x78,0x78,0x78,0x78,0x20,0x2a,0x20,0x69,0x6e,0x2e,0x63,0x6f,
    0x6c,0x6f,0x72,0x3b,0x0a,0x20,0x20,0x20,0x20,0x72,0x65,0x74,0x75,0x72,0x6e,0x20,
    0x6f,0x75,0x74,0x3b,0x0a,0x7d,0x0a,0x0a,0x00,
};
#elif defined(SOKOL_D3D11)
/*
    static float4 gl_Position;
    static float2 position;
    static float2 uv;
    static float2 texcoord0;
    static float4 color;
    static float4 color0;

    struct SPIRV_Cross_Input
    {
        float2 position : TEXCOORD0;
        float2 texcoord0 : TEXCOORD1;
        float4 color0 : TEXCOORD2;
    };

    struct SPIRV_Cross_Output
    {
        float2 uv : TEXCOORD0;
        float4 color : TEXCOORD1;
        float4 gl_Position : SV_Position;
    };

    void vert_main()
    {
        gl_Position = float4(mad(position, float2(2.0f, -2.0f), float2(-1.0f, 1.0f)), 0.0f, 1.0f);
        uv = texcoord0;
        color = color0;
    }

    SPIRV_Cross_Output main(SPIRV_Cross_Input stage_input)
    {
        position = stage_input.position;
        texcoord0 = stage_input.texcoord0;
        color0 = stage_input.color0;
        vert_main();
        SPIRV_Cross_Output stage_output;
        stage_output.gl_Position = gl_Position;
        stage_output.uv = uv;
        stage_output.color = color;
        return stage_output;
    }
*/
static const uint8_t _sdtx_vs_bytecode_hlsl4[692] = {
    0x44,0x58,0x42,0x43,0x07,0x05,0xa0,0xb3,0x53,0xc1,0x0a,0x0d,0x1e,0xf4,0xe4,0xa6,
    0x91,0xaf,0x4c,0xca,0x01,0x00,0x00,0x00,0xb4,0x02,0x00,0x00,0x05,0x00,0x00,0x00,
    0x34,0x00,0x00,0x00,0x80,0x00,0x00,0x00,0xe4,0x00,0x00,0x00,0x54,0x01,0x00,0x00,
    0x38,0x02,0x00,0x00,0x52,0x44,0x45,0x46,0x44,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x1c,0x00,0x00,0x00,0x00,0x04,0xfe,0xff,
    0x10,0x81,0x00,0x00,0x1c,0x00,0x00,0x00,0x4d,0x69,0x63,0x72,0x6f,0x73,0x6f,0x66,
    0x74,0x20,0x28,0x52,0x29,0x20,0x48,0x4c,0x53,0x4c,0x20,0x53,0x68,0x61,0x64,0x65,
    0x72,0x20,0x43,0x6f,0x6d,0x70,0x69,0x6c,0x65,0x72,0x20,0x31,0x30,0x2e,0x31,0x00,
    0x49,0x53,0x47,0x4e,0x5c,0x00,0x00,0x00,0x03,0x00,0x00,0x00,0x08,0x00,0x00,0x00,
    0x50,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x03,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x03,0x03,0x00,0x00,0x50,0x00,0x00,0x00,0x01,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x03,0x00,0x00,0x00,0x01,0x00,0x00,0x00,0x03,0x03,0x00,0x00,
    0x50,0x00,0x00,0x00,0x02,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x03,0x00,0x00,0x00,
    0x02,0x00,0x00,0x00,0x0f,0x0f,0x00,0x00,0x54,0x45,0x58,0x43,0x4f,0x4f,0x52,0x44,
    0x00,0xab,0xab,0xab,0x4f,0x53,0x47,0x4e,0x68,0x00,0x00,0x00,0x03,0x00,0x00,0x00,
    0x08,0x00,0x00,0x00,0x50,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x03,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x03,0x0c,0x00,0x00,0x50,0x00,0x00,0x00,
    0x01,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x03,0x00,0x00,0x00,0x01,0x00,0x00,0x00,
    0x0f,0x00,0x00,0x00,0x59,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x01,0x00,0x00,0x00,
    0x03,0x00,0x00,0x00,0x02,0x00,0x00,0x00,0x0f,0x00,0x00,0x00,0x54,0x45,0x58,0x43,
    0x4f,0x4f,0x52,0x44,0x00,0x53,0x56,0x5f,0x50,0x6f,0x73,0x69,0x74,0x69,0x6f,0x6e,
    0x00,0xab,0xab,0xab,0x53,0x48,0x44,0x52,0xdc,0x00,0x00,0x00,0x40,0x00,0x01,0x00,
    0x37,0x00,0x00,0x00,0x5f,0x00,0x00,0x03,0x32,0x10,0x10,0x00,0x00,0x00,0x00,0x00,
    0x5f,0x00,0x00,0x03,0x32,0x10,0x10,0x00,0x01,0x00,0x00,0x00,0x5f,0x00,0x00,0x03,
    0xf2,0x10,0x10,0x00,0x02,0x00,0x00,0x00,0x65,0x00,0x00,0x03,0x32,0x20,0x10,0x00,
    0x00,0x00,0x00,0x00,0x65,0x00,0x00,0x03,0xf2,0x20,0x10,0x00,0x01,0x00,0x00,0x00,
    0x67,0x00,0x00,0x04,0xf2,0x20,0x10,0x00,0x02,0x00,0x00,0x00,0x01,0x00,0x00,0x00,
    0x36,0x00,0x00,0x05,0x32,0x20,0x10,0x00,0x00,0x00,0x00,0x00,0x46,0x10,0x10,0x00,
    0x01,0x00,0x00,0x00,0x36,0x00,0x00,0x05,0xf2,0x20,0x10,0x00,0x01,0x00,0x00,0x00,
    0x46,0x1e,0x10,0x00,0x02,0x00,0x00,0x00,0x32,0x00,0x00,0x0f,0x32,0x20,0x10,0x00,
    0x02,0x00,0x00,0x00,0x46,0x10,0x10,0x00,0x00,0x00,0x00,0x00,0x02,0x40,0x00,0x00,
    0x00,0x00,0x00,0x40,0x00,0x00,0x00,0xc0,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x02,0x40,0x00,0x00,0x00,0x00,0x80,0xbf,0x00,0x00,0x80,0x3f,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x36,0x00,0x00,0x08,0xc2,0x20,0x10,0x00,0x02,0x00,0x00,0x00,
    0x02,0x40,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x80,0x3f,0x3e,0x00,0x00,0x01,0x53,0x54,0x41,0x54,0x74,0x00,0x00,0x00,
    0x05,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x06,0x00,0x00,0x00,
    0x01,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x01,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x03,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,
};
/*
    Texture2D<float4> tex : register(t0);
    SamplerState smp : register(s0);

    static float4 frag_color;
    static float2 uv;
    static float4 color;

    struct SPIRV_Cross_Input
    {
        float2 uv : TEXCOORD0;
        float4 color : TEXCOORD1;
    };

    struct SPIRV_Cross_Output
    {
        float4 frag_color : SV_Target0;
    };

    void frag_main()
    {
        frag_color = tex.Sample(smp, uv).xxxx * color;
    }

    SPIRV_Cross_Output main(SPIRV_Cross_Input stage_input)
    {
        uv = stage_input.uv;
        color = stage_input.color;
        frag_main();
        SPIRV_Cross_Output stage_output;
        stage_output.frag_color = frag_color;
        return stage_output;
    }
*/
static const uint8_t _sdtx_fs_bytecode_hlsl4[608] = {
    0x44,0x58,0x42,0x43,0xb7,0xcd,0xbd,0xb1,0x6f,0x85,0x5d,0x59,0x07,0x7e,0xa3,0x6e,
    0xe2,0x23,0x68,0xa0,0x01,0x00,0x00,0x00,0x60,0x02,0x00,0x00,0x05,0x00,0x00,0x00,
    0x34,0x00,0x00,0x00,0xc8,0x00,0x00,0x00,0x14,0x01,0x00,0x00,0x48,0x01,0x00,0x00,
    0xe4,0x01,0x00,0x00,0x52,0x44,0x45,0x46,0x8c,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x02,0x00,0x00,0x00,0x1c,0x00,0x00,0x00,0x00,0x04,0xff,0xff,
    0x10,0x81,0x00,0x00,0x64,0x00,0x00,0x00,0x5c,0x00,0x00,0x00,0x03,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x01,0x00,0x00,0x00,0x01,0x00,0x00,0x00,0x60,0x00,0x00,0x00,0x02,0x00,0x00,0x00,
    0x05,0x00,0x00,0x00,0x04,0x00,0x00,0x00,0xff,0xff,0xff,0xff,0x00,0x00,0x00,0x00,
    0x01,0x00,0x00,0x00,0x0d,0x00,0x00,0x00,0x73,0x6d,0x70,0x00,0x74,0x65,0x78,0x00,
    0x4d,0x69,0x63,0x72,0x6f,0x73,0x6f,0x66,0x74,0x20,0x28,0x52,0x29,0x20,0x48,0x4c,
    0x53,0x4c,0x20,0x53,0x68,0x61,0x64,0x65,0x72,0x20,0x43,0x6f,0x6d,0x70,0x69,0x6c,
    0x65,0x72,0x20,0x31,0x30,0x2e,0x31,0x00,0x49,0x53,0x47,0x4e,0x44,0x00,0x00,0x00,
    0x02,0x00,0x00,0x00,0x08,0x00,0x00,0x00,0x38,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x03,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x03,0x03,0x00,0x00,
    0x38,0x00,0x00,0x00,0x01,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x03,0x00,0x00,0x00,
    0x01,0x00,0x00,0x00,0x0f,0x0f,0x00,0x00,0x54,0x45,0x58,0x43,0x4f,0x4f,0x52,0x44,
    0x00,0xab,0xab,0xab,0x4f,0x53,0x47,0x4e,0x2c,0x00,0x00,0x00,0x01,0x00,0x00,0x00,
    0x08,0x00,0x00,0x00,0x20,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x03,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0f,0x00,0x00,0x00,0x53,0x56,0x5f,0x54,
    0x61,0x72,0x67,0x65,0x74,0x00,0xab,0xab,0x53,0x48,0x44,0x52,0x94,0x00,0x00,0x00,
    0x40,0x00,0x00,0x00,0x25,0x00,0x00,0x00,0x5a,0x00,0x00,0x03,0x00,0x60,0x10,0x00,
    0x00,0x00,0x00,0x00,0x58,0x18,0x00,0x04,0x00,0x70,0x10,0x00,0x00,0x00,0x00,0x00,
    0x55,0x55,0x00,0x00,0x62,0x10,0x00,0x03,0x32,0x10,0x10,0x00,0x00,0x00,0x00,0x00,
    0x62,0x10,0x00,0x03,0xf2,0x10,0x10,0x00,0x01,0x00,0x00,0x00,0x65,0x00,0x00,0x03,
    0xf2,0x20,0x10,0x00,0x00,0x00,0x00,0x00,0x68,0x00,0x00,0x02,0x01,0x00,0x00,0x00,
    0x45,0x00,0x00,0x09,0xf2,0x00,0x10,0x00,0x00,0x00,0x00,0x00,0x46,0x10,0x10,0x00,
    0x00,0x00,0x00,0x00,0x46,0x7e,0x10,0x00,0x00,0x00,0x00,0x00,0x00,0x60,0x10,0x00,
    0x00,0x00,0x00,0x00,0x38,0x00,0x00,0x07,0xf2,0x20,0x10,0x00,0x00,0x00,0x00,0x00,
    0x06,0x00,0x10,0x00,0x00,0x00,0x00,0x00,0x46,0x1e,0x10,0x00,0x01,0x00,0x00,0x00,
    0x3e,0x00,0x00,0x01,0x53,0x54,0x41,0x54,0x74,0x00,0x00,0x00,0x03,0x00,0x00,0x00,
    0x01,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x03,0x00,0x00,0x00,0x01,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x01,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x01,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
#elif defined(SOKOL_WGPU)
/*
    diagnostic(off, derivative_uniformity);

    var<private> position_1 : vec2f;

    var<private> uv : vec2f;

    var<private> texcoord0 : vec2f;

    var<private> color : vec4f;

    var<private> color0 : vec4f;

    var<private> gl_Position : vec4f;

    fn main_1() {
      let x_19 : vec2f = position_1;
      let x_27 : vec2f = ((x_19 * vec2f(2.0f, -2.0f)) + vec2f(-1.0f, 1.0f));
      gl_Position = vec4f(x_27.x, x_27.y, 0.0f, 1.0f);
      let x_37 : vec2f = texcoord0;
      uv = x_37;
      let x_41 : vec4f = color0;
      color = x_41;
      return;
    }

    struct main_out {
      @builtin(position)
      gl_Position : vec4f,
      @location(0)
      uv_1 : vec2f,
      @location(1)
      color_1 : vec4f,
    }

    @vertex
    fn main(@location(0) position_1_param : vec2f, @location(1) texcoord0_param : vec2f, @location(2) color0_param : vec4f) -> main_out {
      position_1 = position_1_param;
      texcoord0 = texcoord0_param;
      color0 = color0_param;
      main_1();
      return main_out(gl_Position, uv, color);
    }

*/
static const uint8_t _sdtx_vs_source_wgsl[922] = {
    0x64,0x69,0x61,0x67,0x6e,0x6f,0x73,0x74,0x69,0x63,0x28,0x6f,0x66,0x66,0x2c,0x20,
    0x64,0x65,0x72,0x69,0x76,0x61,0x74,0x69,0x76,0x65,0x5f,0x75,0x6e,0x69,0x66,0x6f,
    0x72,0x6d,0x69,0x74,0x79,0x29,0x3b,0x0a,0x0a,0x76,0x61,0x72,0x3c,0x70,0x72,0x69,
    0x76,0x61,0x74,0x65,0x3e,0x20,0x70,0x6f,0x73,0x69,0x74,0x69,0x6f,0x6e,0x5f,0x31,
    0x20,0x3a,0x20,0x76,0x65,0x63,0x32,0x66,0x3b,0x0a,0x0a,0x76,0x61,0x72,0x3c,0x70,
    0x72,0x69,0x76,0x61,0x74,0x65,0x3e,0x20,0x75,0x76,0x20,0x3a,0x20,0x76,0x65,0x63,
    0x32,0x66,0x3b,0x0a,0x0a,0x76,0x61,0x72,0x3c,0x70,0x72,0x69,0x76,0x61,0x74,0x65,
    0x3e,0x20,0x74,0x65,0x78,0x63,0x6f,0x6f,0x72,0x64,0x30,0x20,0x3a,0x20,0x76,0x65,
    0x63,0x32,0x66,0x3b,0x0a,0x0a,0x76,0x61,0x72,0x3c,0x70,0x72,0x69,0x76,0x61,0x74,
    0x65,0x3e,0x20,0x63,0x6f,0x6c,0x6f,0x72,0x20,0x3a,0x20,0x76,0x65,0x63,0x34,0x66,
    0x3b,0x0a,0x0a,0x76,0x61,0x72,0x3c,0x70,0x72,0x69,0x76,0x61,0x74,0x65,0x3e,0x20,
    0x63,0x6f,0x6c,0x6f,0x72,0x30,0x20,0x3a,0x20,0x76,0x65,0x63,0x34,0x66,0x3b,0x0a,
    0x0a,0x76,0x61,0x72,0x3c,0x70,0x72,0x69,0x76,0x61,0x74,0x65,0x3e,0x20,0x67,0x6c,
    0x5f,0x50,0x6f,0x73,0x69,0x74,0x69,0x6f,0x6e,0x20,0x3a,0x20,0x76,0x65,0x63,0x34,
    0x66,0x3b,0x0a,0x0a,0x66,0x6e,0x20,0x6d,0x61,0x69,0x6e,0x5f,0x31,0x28,0x29,0x20,
    0x7b,0x0a,0x20,0x20,0x6c,0x65,0x74,0x20,0x78,0x5f,0x31,0x39,0x20,0x3a,0x20,0x76,
    0x65,0x63,0x32,0x66,0x20,0x3d,0x20,0x70,0x6f,0x73,0x69,0x74,0x69,0x6f,0x6e,0x5f,
    0x31,0x3b,0x0a,0x20,0x20,0x6c,0x65,0x74,0x20,0x78,0x5f,0x32,0x37,0x20,0x3a,0x20,
    0x76,0x65,0x63,0x32,0x66,0x20,0x3d,0x20,0x28,0x28,0x78,0x5f,0x31,0x39,0x20,0x2a,
    0x20,0x76,0x65,0x63,0x32,0x66,0x28,0x32,0x2e,0x30,0x66,0x2c,0x20,0x2d,0x32,0x2e,
    0x30,0x66,0x29,0x29,0x20,0x2b,0x20,0x76,0x65,0x63,0x32,0x66,0x28,0x2d,0x31,0x2e,
    0x30,0x66,0x2c,0x20,0x31,0x2e,0x30,0x66,0x29,0x29,0x3b,0x0a,0x20,0x20,0x67,0x6c,
    0x5f,0x50,0x6f,0x73,0x69,0x74,0x69,0x6f,0x6e,0x20,0x3d,0x20,0x76,0x65,0x63,0x34,
    0x66,0x28,0x78,0x5f,0x32,0x37,0x2e,0x78,0x2c,0x20,0x78,0x5f,0x32,0x37,0x2e,0x79,
    0x2c,0x20,0x30,0x2e,0x30,0x66,0x2c,0x20,0x31,0x2e,0x30,0x66,0x29,0x3b,0x0a,0x20,
    0x20,0x6c,0x65,0x74,0x20,0x78,0x5f,0x33,0x37,0x20,0x3a,0x20,0x76,0x65,0x63,0x32,
    0x66,0x20,0x3d,0x20,0x74,0x65,0x78,0x63,0x6f,0x6f,0x72,0x64,0x30,0x3b,0x0a,0x20,
    0x20,0x75,0x76,0x20,0x3d,0x20,0x78,0x5f,0x33,0x37,0x3b,0x0a,0x20,0x20,0x6c,0x65,
    0x74,0x20,0x78,0x5f,0x34,0x31,0x20,0x3a,0x20,0x76,0x65,0x63,0x34,0x66,0x20,0x3d,
    0x20,0x63,0x6f,0x6c,0x6f,0x72,0x30,0x3b,0x0a,0x20,0x20,0x63,0x6f,0x6c,0x6f,0x72,
    0x20,0x3d,0x20,0x78,0x5f,0x34,0x31,0x3b,0x0a,0x20,0x20,0x72,0x65,0x74,0x75,0x72,
    0x6e,0x3b,0x0a,0x7d,0x0a,0x0a,0x73,0x74,0x72,0x75,0x63,0x74,0x20,0x6d,0x61,0x69,
    0x6e,0x5f,0x6f,0x75,0x74,0x20,0x7b,0x0a,0x20,0x20,0x40,0x62,0x75,0x69,0x6c,0x74,
    0x69,0x6e,0x28,0x70,0x6f,0x73,0x69,0x74,0x69,0x6f,0x6e,0x29,0x0a,0x20,0x20,0x67,
    0x6c,0x5f,0x50,0x6f,0x73,0x69,0x74,0x69,0x6f,0x6e,0x20,0x3a,0x20,0x76,0x65,0x63,
    0x34,0x66,0x2c,0x0a,0x20,0x20,0x40,0x6c,0x6f,0x63,0x61,0x74,0x69,0x6f,0x6e,0x28,
    0x30,0x29,0x0a,0x20,0x20,0x75,0x76,0x5f,0x31,0x20,0x3a,0x20,0x76,0x65,0x63,0x32,
    0x66,0x2c,0x0a,0x20,0x20,0x40,0x6c,0x6f,0x63,0x61,0x74,0x69,0x6f,0x6e,0x28,0x31,
    0x29,0x0a,0x20,0x20,0x63,0x6f,0x6c,0x6f,0x72,0x5f,0x31,0x20,0x3a,0x20,0x76,0x65,
    0x63,0x34,0x66,0x2c,0x0a,0x7d,0x0a,0x0a,0x40,0x76,0x65,0x72,0x74,0x65,0x78,0x0a,
    0x66,0x6e,0x20,0x6d,0x61,0x69,0x6e,0x28,0x40,0x6c,0x6f,0x63,0x61,0x74,0x69,0x6f,
    0x6e,0x28,0x30,0x29,0x20,0x70,0x6f,0x73,0x69,0x74,0x69,0x6f,0x6e,0x5f,0x31,0x5f,
    0x70,0x61,0x72,0x61,0x6d,0x20,0x3a,0x20,0x76,0x65,0x63,0x32,0x66,0x2c,0x20,0x40,
    0x6c,0x6f,0x63,0x61,0x74,0x69,0x6f,0x6e,0x28,0x31,0x29,0x20,0x74,0x65,0x78,0x63,
    0x6f,0x6f,0x72,0x64,0x30,0x5f,0x70,0x61,0x72,0x61,0x6d,0x20,0x3a,0x20,0x76,0x65,
    0x63,0x32,0x66,0x2c,0x20,0x40,0x6c,0x6f,0x63,0x61,0x74,0x69,0x6f,0x6e,0x28,0x32,
    0x29,0x20,0x63,0x6f,0x6c,0x6f,0x72,0x30,0x5f,0x70,0x61,0x72,0x61,0x6d,0x20,0x3a,
    0x20,0x76,0x65,0x63,0x34,0x66,0x29,0x20,0x2d,0x3e,0x20,0x6d,0x61,0x69,0x6e,0x5f,
    0x6f,0x75,0x74,0x20,0x7b,0x0a,0x20,0x20,0x70,0x6f,0x73,0x69,0x74,0x69,0x6f,0x6e,
    0x5f,0x31,0x20,0x3d,0x20,0x70,0x6f,0x73,0x69,0x74,0x69,0x6f,0x6e,0x5f,0x31,0x5f,
    0x70,0x61,0x72,0x61,0x6d,0x3b,0x0a,0x20,0x20,0x74,0x65,0x78,0x63,0x6f,0x6f,0x72,
    0x64,0x30,0x20,0x3d,0x20,0x74,0x65,0x78,0x63,0x6f,0x6f,0x72,0x64,0x30,0x5f,0x70,
    0x61,0x72,0x61,0x6d,0x3b,0x0a,0x20,0x20,0x63,0x6f,0x6c,0x6f,0x72,0x30,0x20,0x3d,
    0x20,0x63,0x6f,0x6c,0x6f,0x72,0x30,0x5f,0x70,0x61,0x72,0x61,0x6d,0x3b,0x0a,0x20,
    0x20,0x6d,0x61,0x69,0x6e,0x5f,0x31,0x28,0x29,0x3b,0x0a,0x20,0x20,0x72,0x65,0x74,
    0x75,0x72,0x6e,0x20,0x6d,0x61,0x69,0x6e,0x5f,0x6f,0x75,0x74,0x28,0x67,0x6c,0x5f,
    0x50,0x6f,0x73,0x69,0x74,0x69,0x6f,0x6e,0x2c,0x20,0x75,0x76,0x2c,0x20,0x63,0x6f,
    0x6c,0x6f,0x72,0x29,0x3b,0x0a,0x7d,0x0a,0x0a,0x00,
};
/*
    diagnostic(off, derivative_uniformity);

    var<private> frag_color : vec4f;

    @group(1) @binding(64) var tex : texture_2d<f32>;

    @group(1) @binding(80) var smp : sampler;

    var<private> uv : vec2f;

    var<private> color : vec4f;

    fn main_1() {
      let x_23 : vec2f = uv;
      let x_24 : vec4f = textureSample(tex, smp, x_23);
      let x_28 : vec4f = color;
      frag_color = (vec4f(x_24.x, x_24.x, x_24.x, x_24.x) * x_28);
      return;
    }

    struct main_out {
      @location(0)
      frag_color_1 : vec4f,
    }

    @fragment
    fn main(@location(0) uv_param : vec2f, @location(1) color_param : vec4f) -> main_out {
      uv = uv_param;
      color = color_param;
      main_1();
      return main_out(frag_color);
    }
*/
static const uint8_t _sdtx_fs_source_wgsl[663] = {
    0x64,0x69,0x61,0x67,0x6e,0x6f,0x73,0x74,0x69,0x63,0x28,0x6f,0x66,0x66,0x2c,0x20,
    0x64,0x65,0x72,0x69,0x76,0x61,0x74,0x69,0x76,0x65,0x5f,0x75,0x6e,0x69,0x66,0x6f,
    0x72,0x6d,0x69,0x74,0x79,0x29,0x3b,0x0a,0x0a,0x76,0x61,0x72,0x3c,0x70,0x72,0x69,
    0x76,0x61,0x74,0x65,0x3e,0x20,0x66,0x72,0x61,0x67,0x5f,0x63,0x6f,0x6c,0x6f,0x72,
    0x20,0x3a,0x20,0x76,0x65,0x63,0x34,0x66,0x3b,0x0a,0x0a,0x40,0x67,0x72,0x6f,0x75,
    0x70,0x28,0x31,0x29,0x20,0x40,0x62,0x69,0x6e,0x64,0x69,0x6e,0x67,0x28,0x36,0x34,
    0x29,0x20,0x76,0x61,0x72,0x20,0x74,0x65,0x78,0x20,0x3a,0x20,0x74,0x65,0x78,0x74,
    0x75,0x72,0x65,0x5f,0x32,0x64,0x3c,0x66,0x33,0x32,0x3e,0x3b,0x0a,0x0a,0x40,0x67,
    0x72,0x6f,0x75,0x70,0x28,0x31,0x29,0x20,0x40,0x62,0x69,0x6e,0x64,0x69,0x6e,0x67,
    0x28,0x38,0x30,0x29,0x20,0x76,0x61,0x72,0x20,0x73,0x6d,0x70,0x20,0x3a,0x20,0x73,
    0x61,0x6d,0x70,0x6c,0x65,0x72,0x3b,0x0a,0x0a,0x76,0x61,0x72,0x3c,0x70,0x72,0x69,
    0x76,0x61,0x74,0x65,0x3e,0x20,0x75,0x76,0x20,0x3a,0x20,0x76,0x65,0x63,0x32,0x66,
    0x3b,0x0a,0x0a,0x76,0x61,0x72,0x3c,0x70,0x72,0x69,0x76,0x61,0x74,0x65,0x3e,0x20,
    0x63,0x6f,0x6c,0x6f,0x72,0x20,0x3a,0x20,0x76,0x65,0x63,0x34,0x66,0x3b,0x0a,0x0a,
    0x66,0x6e,0x20,0x6d,0x61,0x69,0x6e,0x5f,0x31,0x28,0x29,0x20,0x7b,0x0a,0x20,0x20,
    0x6c,0x65,0x74,0x20,0x78,0x5f,0x32,0x33,0x20,0x3a,0x20,0x76,0x65,0x63,0x32,0x66,
    0x20,0x3d,0x20,0x75,0x76,0x3b,0x0a,0x20,0x20,0x6c,0x65,0x74,0x20,0x78,0x5f,0x32,
    0x34,0x20,0x3a,0x20,0x76,0x65,0x63,0x34,0x66,0x20,0x3d,0x20,0x74,0x65,0x78,0x74,
    0x75,0x72,0x65,0x53,0x61,0x6d,0x70,0x6c,0x65,0x28,0x74,0x65,0x78,0x2c,0x20,0x73,
    0x6d,0x70,0x2c,0x20,0x78,0x5f,0x32,0x33,0x29,0x3b,0x0a,0x20,0x20,0x6c,0x65,0x74,
    0x20,0x78,0x5f,0x32,0x38,0x20,0x3a,0x20,0x76,0x65,0x63,0x34,0x66,0x20,0x3d,0x20,
    0x63,0x6f,0x6c,0x6f,0x72,0x3b,0x0a,0x20,0x20,0x66,0x72,0x61,0x67,0x5f,0x63,0x6f,
    0x6c,0x6f,0x72,0x20,0x3d,0x20,0x28,0x76,0x65,0x63,0x34,0x66,0x28,0x78,0x5f,0x32,
    0x34,0x2e,0x78,0x2c,0x20,0x78,0x5f,0x32,0x34,0x2e,0x78,0x2c,0x20,0x78,0x5f,0x32,
    0x34,0x2e,0x78,0x2c,0x20,0x78,0x5f,0x32,0x34,0x2e,0x78,0x29,0x20,0x2a,0x20,0x78,
    0x5f,0x32,0x38,0x29,0x3b,0x0a,0x20,0x20,0x72,0x65,0x74,0x75,0x72,0x6e,0x3b,0x0a,
    0x7d,0x0a,0x0a,0x73,0x74,0x72,0x75,0x63,0x74,0x20,0x6d,0x61,0x69,0x6e,0x5f,0x6f,
    0x75,0x74,0x20,0x7b,0x0a,0x20,0x20,0x40,0x6c,0x6f,0x63,0x61,0x74,0x69,0x6f,0x6e,
    0x28,0x30,0x29,0x0a,0x20,0x20,0x66,0x72,0x61,0x67,0x5f,0x63,0x6f,0x6c,0x6f,0x72,
    0x5f,0x31,0x20,0x3a,0x20,0x76,0x65,0x63,0x34,0x66,0x2c,0x0a,0x7d,0x0a,0x0a,0x40,
    0x66,0x72,0x61,0x67,0x6d,0x65,0x6e,0x74,0x0a,0x66,0x6e,0x20,0x6d,0x61,0x69,0x6e,
    0x28,0x40,0x6c,0x6f,0x63,0x61,0x74,0x69,0x6f,0x6e,0x28,0x30,0x29,0x20,0x75,0x76,
    0x5f,0x70,0x61,0x72,0x61,0x6d,0x20,0x3a,0x20,0x76,0x65,0x63,0x32,0x66,0x2c,0x20,
    0x40,0x6c,0x6f,0x63,0x61,0x74,0x69,0x6f,0x6e,0x28,0x31,0x29,0x20,0x63,0x6f,0x6c,
    0x6f,0x72,0x5f,0x70,0x61,0x72,0x61,0x6d,0x20,0x3a,0x20,0x76,0x65,0x63,0x34,0x66,
    0x29,0x20,0x2d,0x3e,0x20,0x6d,0x61,0x69,0x6e,0x5f,0x6f,0x75,0x74,0x20,0x7b,0x0a,
    0x20,0x20,0x75,0x76,0x20,0x3d,0x20,0x75,0x76,0x5f,0x70,0x61,0x72,0x61,0x6d,0x3b,
    0x0a,0x20,0x20,0x63,0x6f,0x6c,0x6f,0x72,0x20,0x3d,0x20,0x63,0x6f,0x6c,0x6f,0x72,
    0x5f,0x70,0x61,0x72,0x61,0x6d,0x3b,0x0a,0x20,0x20,0x6d,0x61,0x69,0x6e,0x5f,0x31,
    0x28,0x29,0x3b,0x0a,0x20,0x20,0x72,0x65,0x74,0x75,0x72,0x6e,0x20,0x6d,0x61,0x69,
    0x6e,0x5f,0x6f,0x75,0x74,0x28,0x66,0x72,0x61,0x67,0x5f,0x63,0x6f,0x6c,0x6f,0x72,
    0x29,0x3b,0x0a,0x7d,0x0a,0x0a,0x00,
};
#elif defined(SOKOL_DUMMY_BACKEND)
static const char* _sdtx_vs_src_dummy = "";
static const char* _sdtx_fs_src_dummy = "";
#else
#error "Please define one of SOKOL_GLCORE, SOKOL_GLES3, SOKOL_D3D11, SOKOL_METAL, SOKOL_WGPU or SOKOL_DUMMY_BACKEND!"
#endif

// ███████ ████████ ██████  ██    ██  ██████ ████████ ███████
// ██         ██    ██   ██ ██    ██ ██         ██    ██
// ███████    ██    ██████  ██    ██ ██         ██    ███████
//      ██    ██    ██   ██ ██    ██ ██         ██         ██
// ███████    ██    ██   ██  ██████   ██████    ██    ███████
//
// >>structs
typedef struct {
    uint32_t id;
    sg_resource_state state;
} _sdtx_slot_t;

typedef struct {
    int size;
    int queue_top;
    uint32_t* gen_ctrs;
    int* free_queue;
} _sdtx_pool_t;

typedef struct {
    float x, y;
} _sdtx_float2_t;

typedef struct {
    float x, y;
    uint16_t u, v;
    uint32_t color;
} _sdtx_vertex_t;

typedef struct {
    int layer_id;
    int first_vertex;
    int num_vertices;
} _sdtx_command_t;

typedef struct {
    _sdtx_slot_t slot;
    sdtx_context_desc_t desc;
    uint32_t frame_id;
    uint32_t update_frame_id;
    struct {
        int cap;
        int next;
        _sdtx_vertex_t* ptr;
    } vertices;
    struct {
        int cap;
        int next;
        _sdtx_command_t* ptr;
    } commands;
    sg_buffer vbuf;
    sg_pipeline pip;
    int cur_font;
    int cur_layer_id;
    _sdtx_float2_t canvas_size;
    _sdtx_float2_t glyph_size;
    _sdtx_float2_t origin;
    _sdtx_float2_t pos;
    float tab_width;
    uint32_t color;
} _sdtx_context_t;

typedef struct {
    _sdtx_pool_t pool;
    _sdtx_context_t* contexts;
} _sdtx_context_pool_t;

typedef struct {
    uint32_t init_cookie;
    sdtx_desc_t desc;
    sg_image font_img;
    sg_view font_view;
    sg_sampler font_smp;
    sg_shader shader;
    uint32_t fmt_buf_size;
    char* fmt_buf;
    sdtx_context def_ctx_id;
    sdtx_context cur_ctx_id;
    _sdtx_context_t* cur_ctx;   // may be 0!
    _sdtx_context_pool_t context_pool;
    uint8_t font_pixels[SDTX_MAX_FONTS * 256 * 8 * 8];
} _sdtx_t;
static _sdtx_t _sdtx;

// ██       ██████   ██████   ██████  ██ ███    ██  ██████
// ██      ██    ██ ██       ██       ██ ████   ██ ██
// ██      ██    ██ ██   ███ ██   ███ ██ ██ ██  ██ ██   ███
// ██      ██    ██ ██    ██ ██    ██ ██ ██  ██ ██ ██    ██
// ███████  ██████   ██████   ██████  ██ ██   ████  ██████
//
// >>logging
#if defined(SOKOL_DEBUG)
#define _SDTX_LOGITEM_XMACRO(item,msg) #item ": " msg,
static const char* _sdtx_log_messages[] = {
    _SDTX_LOG_ITEMS
};
#undef _SDTX_LOGITEM_XMACRO
#endif // SOKOL_DEBUG

#define _SDTX_PANIC(code) _sdtx_log(SDTX_LOGITEM_ ##code, 0, __LINE__)
#define _SDTX_ERROR(code) _sdtx_log(SDTX_LOGITEM_ ##code, 1, __LINE__)
#define _SDTX_WARN(code) _sdtx_log(SDTX_LOGITEM_ ##code, 2, __LINE__)
#define _SDTX_INFO(code) _sdtx_log(SDTX_LOGITEM_ ##code, 3, __LINE__)

static void _sdtx_log(sdtx_log_item_t log_item, uint32_t log_level, uint32_t line_nr) {
    if (_sdtx.desc.logger.func) {
        #if defined(SOKOL_DEBUG)
            const char* filename = __FILE__;
            const char* message = _sdtx_log_messages[log_item];
        #else
            const char* filename = 0;
            const char* message = 0;
        #endif
        _sdtx.desc.logger.func("sdtx", log_level, (uint32_t)log_item, message, line_nr, filename, _sdtx.desc.logger.user_data);
    } else {
        // for log level PANIC it would be 'undefined behaviour' to continue
        if (log_level == 0) {
            abort();
        }
    }
}

// ███    ███ ███████ ███    ███  ██████  ██████  ██    ██
// ████  ████ ██      ████  ████ ██    ██ ██   ██  ██  ██
// ██ ████ ██ █████   ██ ████ ██ ██    ██ ██████    ████
// ██  ██  ██ ██      ██  ██  ██ ██    ██ ██   ██    ██
// ██      ██ ███████ ██      ██  ██████  ██   ██    ██
//
// >>memory
static void _sdtx_clear(void* ptr, size_t size) {
    SOKOL_ASSERT(ptr && (size > 0));
    memset(ptr, 0, size);
}

static void* _sdtx_malloc(size_t size) {
    SOKOL_ASSERT(size > 0);
    void* ptr;
    if (_sdtx.desc.allocator.alloc_fn) {
        ptr = _sdtx.desc.allocator.alloc_fn(size, _sdtx.desc.allocator.user_data);
    } else {
        ptr = malloc(size);
    }
    if (0 == ptr) {
        _SDTX_PANIC(MALLOC_FAILED);
    }
    return ptr;
}

static void* _sdtx_malloc_clear(size_t size) {
    void* ptr = _sdtx_malloc(size);
    _sdtx_clear(ptr, size);
    return ptr;
}

static void _sdtx_free(void* ptr) {
    if (_sdtx.desc.allocator.free_fn) {
        _sdtx.desc.allocator.free_fn(ptr, _sdtx.desc.allocator.user_data);
    } else {
        free(ptr);
    }
}

//  ██████  ██████  ███    ██ ████████ ███████ ██   ██ ████████     ██████   ██████   ██████  ██
// ██      ██    ██ ████   ██    ██    ██       ██ ██     ██        ██   ██ ██    ██ ██    ██ ██
// ██      ██    ██ ██ ██  ██    ██    █████     ███      ██        ██████  ██    ██ ██    ██ ██
// ██      ██    ██ ██  ██ ██    ██    ██       ██ ██     ██        ██      ██    ██ ██    ██ ██
//  ██████  ██████  ██   ████    ██    ███████ ██   ██    ██        ██       ██████   ██████  ███████
//
// >>context pool
static void _sdtx_init_pool(_sdtx_pool_t* pool, int num) {
    SOKOL_ASSERT(pool && (num >= 1));
    // slot 0 is reserved for the 'invalid id', so bump the pool size by 1
    pool->size = num + 1;
    pool->queue_top = 0;
    // generation counters indexable by pool slot index, slot 0 is reserved
    size_t gen_ctrs_size = sizeof(uint32_t) * (size_t)pool->size;
    pool->gen_ctrs = (uint32_t*) _sdtx_malloc_clear(gen_ctrs_size);
    // it's not a bug to only reserve 'num' here
    pool->free_queue = (int*) _sdtx_malloc_clear(sizeof(int) * (size_t)num);
    // never allocate the zero-th pool item since the invalid id is 0
    for (int i = pool->size-1; i >= 1; i--) {
        pool->free_queue[pool->queue_top++] = i;
    }
}

static void _sdtx_discard_pool(_sdtx_pool_t* pool) {
    SOKOL_ASSERT(pool);
    SOKOL_ASSERT(pool->free_queue);
    _sdtx_free(pool->free_queue);
    pool->free_queue = 0;
    SOKOL_ASSERT(pool->gen_ctrs);
    _sdtx_free(pool->gen_ctrs);
    pool->gen_ctrs = 0;
    pool->size = 0;
    pool->queue_top = 0;
}

static int _sdtx_pool_alloc_index(_sdtx_pool_t* pool) {
    SOKOL_ASSERT(pool);
    SOKOL_ASSERT(pool->free_queue);
    if (pool->queue_top > 0) {
        int slot_index = pool->free_queue[--pool->queue_top];
        SOKOL_ASSERT((slot_index > 0) && (slot_index < pool->size));
        return slot_index;
    } else {
        // pool exhausted
        return _SDTX_INVALID_SLOT_INDEX;
    }
}

static void _sdtx_pool_free_index(_sdtx_pool_t* pool, int slot_index) {
    SOKOL_ASSERT((slot_index > _SDTX_INVALID_SLOT_INDEX) && (slot_index < pool->size));
    SOKOL_ASSERT(pool);
    SOKOL_ASSERT(pool->free_queue);
    SOKOL_ASSERT(pool->queue_top < pool->size);
    #ifdef SOKOL_DEBUG
    // debug check against double-free
    for (int i = 0; i < pool->queue_top; i++) {
        SOKOL_ASSERT(pool->free_queue[i] != slot_index);
    }
    #endif
    pool->free_queue[pool->queue_top++] = slot_index;
    SOKOL_ASSERT(pool->queue_top <= (pool->size-1));
}

static void _sdtx_setup_context_pool(const sdtx_desc_t* desc) {
    SOKOL_ASSERT(desc);
    // note: the pool will have an additional item, since slot 0 is reserved
    SOKOL_ASSERT((desc->context_pool_size > 0) && (desc->context_pool_size < _SDTX_MAX_POOL_SIZE));
    _sdtx_init_pool(&_sdtx.context_pool.pool, desc->context_pool_size);
    size_t pool_byte_size = sizeof(_sdtx_context_t) * (size_t)_sdtx.context_pool.pool.size;
    _sdtx.context_pool.contexts = (_sdtx_context_t*) _sdtx_malloc_clear(pool_byte_size);
}

static void _sdtx_discard_context_pool(void) {
    SOKOL_ASSERT(_sdtx.context_pool.contexts);
    _sdtx_free(_sdtx.context_pool.contexts);
    _sdtx.context_pool.contexts = 0;
    _sdtx_discard_pool(&_sdtx.context_pool.pool);
}

/* allocate the slot at slot_index:
    - bump the slot's generation counter
    - create a resource id from the generation counter and slot index
    - set the slot's id to this id
    - set the slot's state to ALLOC
    - return the resource id
*/
static uint32_t _sdtx_slot_alloc(_sdtx_pool_t* pool, _sdtx_slot_t* slot, int slot_index) {
    /* FIXME: add handling for an overflowing generation counter,
       for now, just overflow (another option is to disable
       the slot)
    */
    SOKOL_ASSERT(pool && pool->gen_ctrs);
    SOKOL_ASSERT((slot_index > _SDTX_INVALID_SLOT_INDEX) && (slot_index < pool->size));
    SOKOL_ASSERT((slot->state == SG_RESOURCESTATE_INITIAL) && (slot->id == SG_INVALID_ID));
    uint32_t ctr = ++pool->gen_ctrs[slot_index];
    slot->id = (ctr<<_SDTX_SLOT_SHIFT)|(slot_index & _SDTX_SLOT_MASK);
    slot->state = SG_RESOURCESTATE_ALLOC;
    return slot->id;
}

// extract slot index from id
static int _sdtx_slot_index(uint32_t id) {
    int slot_index = (int) (id & _SDTX_SLOT_MASK);
    SOKOL_ASSERT(_SDTX_INVALID_SLOT_INDEX != slot_index);
    return slot_index;
}

// get context pointer without id-check
static _sdtx_context_t* _sdtx_context_at(uint32_t ctx_id) {
    SOKOL_ASSERT(SG_INVALID_ID != ctx_id);
    int slot_index = _sdtx_slot_index(ctx_id);
    SOKOL_ASSERT((slot_index > _SDTX_INVALID_SLOT_INDEX) && (slot_index < _sdtx.context_pool.pool.size));
    return &_sdtx.context_pool.contexts[slot_index];
}

// get context pointer with id-check, returns 0 if no match
static _sdtx_context_t* _sdtx_lookup_context(uint32_t ctx_id) {
    if (SG_INVALID_ID != ctx_id) {
        _sdtx_context_t* ctx = _sdtx_context_at(ctx_id);
        if (ctx->slot.id == ctx_id) {
            return ctx;
        }
    }
    return 0;
}

// make context handle from raw uint32_t id
static sdtx_context _sdtx_make_ctx_id(uint32_t ctx_id) {
    sdtx_context ctx;
    ctx.id = ctx_id;
    return ctx;
}

static sdtx_context _sdtx_alloc_context(void) {
    sdtx_context ctx_id;
    int slot_index = _sdtx_pool_alloc_index(&_sdtx.context_pool.pool);
    if (_SDTX_INVALID_SLOT_INDEX != slot_index) {
        ctx_id = _sdtx_make_ctx_id(_sdtx_slot_alloc(&_sdtx.context_pool.pool, &_sdtx.context_pool.contexts[slot_index].slot, slot_index));
    } else {
        // pool is exhausted
        ctx_id = _sdtx_make_ctx_id(SG_INVALID_ID);
    }
    return ctx_id;
}

static sdtx_context_desc_t _sdtx_context_desc_defaults(const sdtx_context_desc_t* desc) {
    sdtx_context_desc_t res = *desc;
    res.max_commands = _sdtx_def(res.max_commands, _SDTX_DEFAULT_MAX_COMMANDS);
    res.char_buf_size = _sdtx_def(res.char_buf_size, _SDTX_DEFAULT_CHAR_BUF_SIZE);
    res.canvas_width = _sdtx_def(res.canvas_width, _SDTX_DEFAULT_CANVAS_WIDTH);
    res.canvas_height = _sdtx_def(res.canvas_height, _SDTX_DEFAULT_CANVAS_HEIGHT);
    res.tab_width = _sdtx_def(res.tab_width, _SDTX_DEFAULT_TAB_WIDTH);
    // keep pixel format attrs are passed as is into pipeline creation
    SOKOL_ASSERT(res.char_buf_size > 0);
    SOKOL_ASSERT(!isnan(res.canvas_width));
    SOKOL_ASSERT(!isnan(res.canvas_height));
    SOKOL_ASSERT(res.canvas_width > 0.0f);
    SOKOL_ASSERT(res.canvas_height > 0.0f);
    return res;
}

static void _sdtx_set_layer(_sdtx_context_t* ctx, int layer_id);
static void _sdtx_rewind(_sdtx_context_t* ctx) {
    SOKOL_ASSERT(ctx);
    ctx->frame_id++;
    ctx->vertices.next = 0;
    ctx->commands.next = 0;
    _sdtx_set_layer(ctx, 0);
    ctx->cur_font = 0;
    ctx->pos.x = 0.0f;
    ctx->pos.y = 0.0f;
}

static void _sdtx_commit_listener(void* userdata) {
    _sdtx_context_t* ctx = _sdtx_lookup_context((uint32_t)(uintptr_t)userdata);
    if (ctx) {
        _sdtx_rewind(ctx);
    }
}

static sg_commit_listener _sdtx_make_commit_listener(_sdtx_context_t* ctx) {
    sg_commit_listener listener = { _sdtx_commit_listener, (void*)(uintptr_t)(ctx->slot.id) };
    return listener;
}

static void _sdtx_init_context(sdtx_context ctx_id, const sdtx_context_desc_t* in_desc) {
    sg_push_debug_group("sokol-debugtext");

    SOKOL_ASSERT((ctx_id.id != SG_INVALID_ID) && in_desc);
    _sdtx_context_t* ctx = _sdtx_lookup_context(ctx_id.id);
    SOKOL_ASSERT(ctx);
    ctx->desc = _sdtx_context_desc_defaults(in_desc);
    // NOTE: frame_id must be non-zero, so that updates trigger in first frame
    ctx->frame_id = 1;

    ctx->vertices.cap = 6 * ctx->desc.char_buf_size;
    const size_t vbuf_size = (size_t)ctx->vertices.cap * sizeof(_sdtx_vertex_t);
    ctx->vertices.ptr = (_sdtx_vertex_t*) _sdtx_malloc(vbuf_size);

    ctx->commands.cap = ctx->desc.max_commands;
    ctx->commands.ptr = (_sdtx_command_t*) _sdtx_malloc((size_t)ctx->commands.cap * sizeof(_sdtx_command_t));
    _sdtx_set_layer(ctx, 0);

    sg_buffer_desc vbuf_desc;
    _sdtx_clear(&vbuf_desc, sizeof(vbuf_desc));
    vbuf_desc.size = vbuf_size;
    vbuf_desc.usage.vertex_buffer = true;
    vbuf_desc.usage.stream_update = true;
    vbuf_desc.label = "sdtx-vbuf";
    ctx->vbuf = sg_make_buffer(&vbuf_desc);
    SOKOL_ASSERT(SG_INVALID_ID != ctx->vbuf.id);

    sg_pipeline_desc pip_desc;
    _sdtx_clear(&pip_desc, sizeof(pip_desc));
    pip_desc.layout.buffers[0].stride = sizeof(_sdtx_vertex_t);
    pip_desc.layout.attrs[0].format = SG_VERTEXFORMAT_FLOAT2;
    pip_desc.layout.attrs[1].format = SG_VERTEXFORMAT_USHORT2N;
    pip_desc.layout.attrs[2].format = SG_VERTEXFORMAT_UBYTE4N;
    pip_desc.shader = _sdtx.shader;
    pip_desc.index_type = SG_INDEXTYPE_NONE;
    pip_desc.sample_count = ctx->desc.sample_count;
    pip_desc.depth.pixel_format = ctx->desc.depth_format;
    pip_desc.colors[0].pixel_format = ctx->desc.color_format;
    pip_desc.colors[0].blend.enabled = true;
    pip_desc.colors[0].blend.src_factor_rgb = SG_BLENDFACTOR_SRC_ALPHA;
    pip_desc.colors[0].blend.dst_factor_rgb = SG_BLENDFACTOR_ONE_MINUS_SRC_ALPHA;
    pip_desc.colors[0].blend.src_factor_alpha = SG_BLENDFACTOR_ONE;
    pip_desc.colors[0].blend.dst_factor_alpha = SG_BLENDFACTOR_ZERO;
    pip_desc.label = "sdtx-pipeline";
    ctx->pip = sg_make_pipeline(&pip_desc);
    SOKOL_ASSERT(SG_INVALID_ID != ctx->pip.id);

    ctx->canvas_size.x = ctx->desc.canvas_width;
    ctx->canvas_size.y = ctx->desc.canvas_height;
    ctx->glyph_size.x = 8.0f / ctx->canvas_size.x;
    ctx->glyph_size.y = 8.0f / ctx->canvas_size.y;
    ctx->tab_width = (float) ctx->desc.tab_width;
    ctx->color = _SDTX_DEFAULT_COLOR;

    if (!sg_add_commit_listener(_sdtx_make_commit_listener(ctx))) {
        _SDTX_ERROR(ADD_COMMIT_LISTENER_FAILED);
    }
    sg_pop_debug_group();
}

static void _sdtx_destroy_context(sdtx_context ctx_id) {
    _sdtx_context_t* ctx = _sdtx_lookup_context(ctx_id.id);
    if (ctx) {
        if (ctx->vertices.ptr) {
            _sdtx_free(ctx->vertices.ptr);
            ctx->vertices.ptr = 0;
            ctx->vertices.cap = 0;
            ctx->vertices.next = 0;
        }
        if (ctx->commands.ptr) {
            _sdtx_free(ctx->commands.ptr);
            ctx->commands.ptr = 0;
            ctx->commands.cap = 0;
            ctx->commands.next = 0;
        }
        sg_push_debug_group("sokol_debugtext");
        sg_destroy_buffer(ctx->vbuf);
        sg_destroy_pipeline(ctx->pip);
        sg_remove_commit_listener(_sdtx_make_commit_listener(ctx));
        sg_pop_debug_group();
        _sdtx_clear(ctx, sizeof(*ctx));
        _sdtx_pool_free_index(&_sdtx.context_pool.pool, _sdtx_slot_index(ctx_id.id));
    }
}

static bool _sdtx_is_default_context(sdtx_context ctx_id) {
    return ctx_id.id == SDTX_DEFAULT_CONTEXT.id;
}

// ███    ███ ██ ███████  ██████
// ████  ████ ██ ██      ██
// ██ ████ ██ ██ ███████ ██
// ██  ██  ██ ██      ██ ██
// ██      ██ ██ ███████  ██████
//
// >>misc

// unpack linear 8x8 bits-per-pixel font data into 2D byte-per-pixel texture data
static void _sdtx_unpack_font(const sdtx_font_desc_t* font_desc, uint8_t* out_pixels) {
    SOKOL_ASSERT(font_desc->data.ptr);
    SOKOL_ASSERT((font_desc->data.size > 0) && ((font_desc->data.size % 8) == 0));
    SOKOL_ASSERT(font_desc->first_char <= font_desc->last_char);
    SOKOL_ASSERT((size_t)(((font_desc->last_char - font_desc->first_char) + 1) * 8) == font_desc->data.size);
    const uint8_t* ptr = (const uint8_t*) font_desc->data.ptr;
    for (int chr = font_desc->first_char; chr <= font_desc->last_char; chr++) {
        for (int line = 0; line < 8; line++) {
            uint8_t bits = *ptr++;
            for (int x = 0; x < 8; x++) {
                out_pixels[line*256*8 + chr*8 + x] = ((bits>>(7-x)) & 1) ? 0xFF : 0x00;
            }
        }
    }
}

static void _sdtx_setup_common(void) {

    // common printf formatting buffer
    _sdtx.fmt_buf_size = (uint32_t) _sdtx.desc.printf_buf_size + 1;
    _sdtx.fmt_buf = (char*) _sdtx_malloc_clear(_sdtx.fmt_buf_size);

    sg_push_debug_group("sokol-debugtext");

    // common shader for all contexts
    sg_shader_desc shd_desc;
    _sdtx_clear(&shd_desc, sizeof(shd_desc));
    shd_desc.label = "sokol-debugtext-shader";
    shd_desc.attrs[0].glsl_name = "position";
    shd_desc.attrs[1].glsl_name = "texcoord0";
    shd_desc.attrs[2].glsl_name = "color0";
    shd_desc.attrs[0].hlsl_sem_name = "TEXCOORD";
    shd_desc.attrs[0].hlsl_sem_index = 0;
    shd_desc.attrs[1].hlsl_sem_name = "TEXCOORD";
    shd_desc.attrs[1].hlsl_sem_index = 1;
    shd_desc.attrs[2].hlsl_sem_name = "TEXCOORD";
    shd_desc.attrs[2].hlsl_sem_index = 2;
    shd_desc.views[0].texture.stage = SG_SHADERSTAGE_FRAGMENT;
    shd_desc.views[0].texture.image_type = SG_IMAGETYPE_2D;
    shd_desc.views[0].texture.sample_type = SG_IMAGESAMPLETYPE_FLOAT;
    shd_desc.views[0].texture.hlsl_register_t_n = 0;
    shd_desc.views[0].texture.msl_texture_n = 0;
    shd_desc.views[0].texture.wgsl_group1_binding_n = 64;
    shd_desc.samplers[0].stage = SG_SHADERSTAGE_FRAGMENT;
    shd_desc.samplers[0].sampler_type = SG_SAMPLERTYPE_FILTERING;
    shd_desc.samplers[0].hlsl_register_s_n = 0;
    shd_desc.samplers[0].msl_sampler_n = 0;
    shd_desc.samplers[0].wgsl_group1_binding_n = 80;
    shd_desc.texture_sampler_pairs[0].stage = SG_SHADERSTAGE_FRAGMENT;
    shd_desc.texture_sampler_pairs[0].view_slot = 0;
    shd_desc.texture_sampler_pairs[0].sampler_slot = 0;
    shd_desc.texture_sampler_pairs[0].glsl_name = "tex_smp";
    #if defined(SOKOL_GLCORE)
        shd_desc.vertex_func.source = (const char*)_sdtx_vs_source_glsl410;
        shd_desc.fragment_func.source = (const char*)_sdtx_fs_source_glsl410;
    #elif defined(SOKOL_GLES3)
        shd_desc.vertex_func.source = (const char*)_sdtx_vs_source_glsl300es;
        shd_desc.fragment_func.source = (const char*)_sdtx_fs_source_glsl300es;
    #elif defined(SOKOL_METAL)
        shd_desc.vertex_func.entry = "main0";
        shd_desc.fragment_func.entry = "main0";
        switch (sg_query_backend()) {
            case SG_BACKEND_METAL_MACOS:
                shd_desc.vertex_func.bytecode = SG_RANGE(_sdtx_vs_bytecode_metal_macos);
                shd_desc.fragment_func.bytecode = SG_RANGE(_sdtx_fs_bytecode_metal_macos);
                break;
            case SG_BACKEND_METAL_IOS:
                shd_desc.vertex_func.bytecode = SG_RANGE(_sdtx_vs_bytecode_metal_ios);
                shd_desc.fragment_func.bytecode = SG_RANGE(_sdtx_fs_bytecode_metal_ios);
                break;
            default:
                shd_desc.vertex_func.source = (const char*)_sdtx_vs_source_metal_sim;
                shd_desc.fragment_func.source = (const char*)_sdtx_fs_source_metal_sim;
                break;
        }
    #elif defined(SOKOL_D3D11)
        shd_desc.vertex_func.bytecode = SG_RANGE(_sdtx_vs_bytecode_hlsl4);
        shd_desc.fragment_func.bytecode = SG_RANGE(_sdtx_fs_bytecode_hlsl4);
    #elif defined(SOKOL_WGPU)
        shd_desc.vertex_func.source = (const char*)_sdtx_vs_source_wgsl;
        shd_desc.fragment_func.source = (const char*)_sdtx_fs_source_wgsl;
    #else
        shd_desc.vertex_func.source = _sdtx_vs_src_dummy;
        shd_desc.fragment_func.source = _sdtx_fs_src_dummy;
    #endif
    _sdtx.shader = sg_make_shader(&shd_desc);
    SOKOL_ASSERT(SG_INVALID_ID != _sdtx.shader.id);

    // unpack font data
    memset(_sdtx.font_pixels, 0xFF, sizeof(_sdtx.font_pixels));
    const int unpacked_font_size = (int) (sizeof(_sdtx.font_pixels) / SDTX_MAX_FONTS);
    for (int i = 0; i < SDTX_MAX_FONTS; i++) {
        if (_sdtx.desc.fonts[i].data.ptr) {
            _sdtx_unpack_font(&_sdtx.desc.fonts[i], &_sdtx.font_pixels[i * unpacked_font_size]);
        }
    }

    // create font image, texture view and sampler
    sg_image_desc img_desc;
    _sdtx_clear(&img_desc, sizeof(img_desc));
    img_desc.width = 256 * 8;
    img_desc.height = SDTX_MAX_FONTS * 8;
    img_desc.pixel_format = SG_PIXELFORMAT_R8;
    img_desc.data.mip_levels[0] = SG_RANGE(_sdtx.font_pixels);
    img_desc.label = "sdtx-font-texture";
    _sdtx.font_img = sg_make_image(&img_desc);
    SOKOL_ASSERT(SG_INVALID_ID != _sdtx.font_img.id);

    sg_view_desc view_desc;
    _sdtx_clear(&view_desc, sizeof(view_desc));
    view_desc.texture.image = _sdtx.font_img;
    view_desc.label = "sdtx-font-texture-view";
    _sdtx.font_view = sg_make_view(&view_desc);
    SOKOL_ASSERT(SG_INVALID_ID != _sdtx.font_view.id);

    sg_sampler_desc smp_desc;
    _sdtx_clear(&smp_desc, sizeof(smp_desc));
    smp_desc.min_filter = SG_FILTER_NEAREST;
    smp_desc.mag_filter = SG_FILTER_NEAREST;
    smp_desc.wrap_u = SG_WRAP_CLAMP_TO_EDGE;
    smp_desc.wrap_v = SG_WRAP_CLAMP_TO_EDGE;
    smp_desc.label = "sdtx-font-sampler";
    _sdtx.font_smp = sg_make_sampler(&smp_desc);
    SOKOL_ASSERT(SG_INVALID_ID != _sdtx.font_smp.id);

    sg_pop_debug_group();
}

static void _sdtx_discard_common(void) {
    sg_push_debug_group("sokol-debugtext");
    sg_destroy_sampler(_sdtx.font_smp);
    sg_destroy_view(_sdtx.font_view);
    sg_destroy_image(_sdtx.font_img);
    sg_destroy_shader(_sdtx.shader);
    if (_sdtx.fmt_buf) {
        _sdtx_free(_sdtx.fmt_buf);
        _sdtx.fmt_buf = 0;
    }
    sg_pop_debug_group();
}

static uint32_t _sdtx_pack_rgbab(uint8_t r, uint8_t g, uint8_t b, uint8_t a) {
    return (uint32_t)(((uint32_t)a<<24)|((uint32_t)b<<16)|((uint32_t)g<<8)|r);
}

static float _sdtx_clamp(float v, float lo, float hi) {
    if (v < lo) return lo;
    else if (v > hi) return hi;
    else return v;
}

static uint32_t _sdtx_pack_rgbaf(float r, float g, float b, float a) {
    uint8_t r_u8 = (uint8_t) (_sdtx_clamp(r, 0.0f, 1.0f) * 255.0f);
    uint8_t g_u8 = (uint8_t) (_sdtx_clamp(g, 0.0f, 1.0f) * 255.0f);
    uint8_t b_u8 = (uint8_t) (_sdtx_clamp(b, 0.0f, 1.0f) * 255.0f);
    uint8_t a_u8 = (uint8_t) (_sdtx_clamp(a, 0.0f, 1.0f) * 255.0f);
    return _sdtx_pack_rgbab(r_u8, g_u8, b_u8, a_u8);
}

static void _sdtx_ctrl_char(_sdtx_context_t* ctx, uint8_t c) {
    switch (c) {
        case '\r':
            ctx->pos.x = 0.0f;
            break;
        case '\n':
            ctx->pos.x = 0.0f;
            ctx->pos.y += 1.0f;
            break;
        case '\t':
            ctx->pos.x = (ctx->pos.x - fmodf(ctx->pos.x, ctx->tab_width)) + ctx->tab_width;
            break;
        case ' ':
            ctx->pos.x += 1.0f;
            break;
    }
}

static _sdtx_vertex_t* _sdtx_next_vertex(_sdtx_context_t* ctx) {
    if ((ctx->vertices.next + 6) <= ctx->vertices.cap) {
        _sdtx_vertex_t* vx = &ctx->vertices.ptr[ctx->vertices.next];
        ctx->vertices.next += 6;
        return vx;
    } else {
        return 0;
    }
}

static _sdtx_command_t* _sdtx_cur_command(_sdtx_context_t* ctx) {
    if (ctx->commands.next > 0) {
        return &ctx->commands.ptr[ctx->commands.next - 1];
    } else {
        return 0;
    }
}

static _sdtx_command_t* _sdtx_next_command(_sdtx_context_t* ctx) {
    if (ctx->commands.next < ctx->commands.cap) {
        return &ctx->commands.ptr[ctx->commands.next++];
    } else {
        _SDTX_ERROR(COMMAND_BUFFER_FULL);
        return 0;
    }
}

static void _sdtx_set_layer(_sdtx_context_t* ctx, int layer_id) {
    ctx->cur_layer_id = layer_id;
    _sdtx_command_t* cur_cmd = _sdtx_cur_command(ctx);
    if (cur_cmd) {
        if ((cur_cmd->num_vertices == 0) || (cur_cmd->layer_id == layer_id)) {
            // no vertices recorded in current draw command, or layer hasn't changed, can just reuse this
            cur_cmd->layer_id = layer_id;
        } else {
            // layer has changed, need to start a new draw command
            _sdtx_command_t* next_cmd = _sdtx_next_command(ctx);
            if (next_cmd) {
                next_cmd->layer_id = layer_id;
                next_cmd->first_vertex = cur_cmd->first_vertex + cur_cmd->num_vertices;
                next_cmd->num_vertices = 0;
            }
        }
    } else {
        // first draw command in frame
        _sdtx_command_t* next_cmd = _sdtx_next_command(ctx);
        if (next_cmd) {
            next_cmd->layer_id = layer_id;
            next_cmd->first_vertex = 0;
            next_cmd->num_vertices = 0;
        }
    }
}

static void _sdtx_render_char(_sdtx_context_t* ctx, uint8_t c) {
    _sdtx_vertex_t* vx = _sdtx_next_vertex(ctx);
    _sdtx_command_t* cmd = _sdtx_cur_command(ctx);
    if (vx && cmd) {
        // update vertex count in current draw command
        cmd->num_vertices += 6;

        const float x0 = (ctx->origin.x + ctx->pos.x) * ctx->glyph_size.x;
        const float y0 = (ctx->origin.y + ctx->pos.y) * ctx->glyph_size.y;
        const float x1 = x0 + ctx->glyph_size.x;
        const float y1 = y0 + ctx->glyph_size.y;

        // glyph width and height in font texture space
        // NOTE: the '+1' and '-2' fixes texture bleeding into the neighboring font texture cell
        const uint16_t uvw = 0x10000 / 0x100;
        const uint16_t uvh = 0x10000 / SDTX_MAX_FONTS;
        const uint16_t u0 = (((uint16_t)c) * uvw) + 1;
        const uint16_t v0 = (((uint16_t)ctx->cur_font) * uvh) + 1;
        uint16_t u1 = (u0 + uvw) - 2;
        uint16_t v1 = (v0 + uvh) - 2;
        const uint32_t color = ctx->color;

        // write 6 vertices
        vx->x=x0; vx->y=y0; vx->u = u0; vx->v = v0; vx->color = color; vx++;
        vx->x=x1; vx->y=y0; vx->u = u1; vx->v = v0; vx->color = color; vx++;
        vx->x=x1; vx->y=y1; vx->u = u1; vx->v = v1; vx->color = color; vx++;

        vx->x=x0; vx->y=y0; vx->u = u0; vx->v = v0; vx->color = color; vx++;
        vx->x=x1; vx->y=y1; vx->u = u1; vx->v = v1; vx->color = color; vx++;
        vx->x=x0; vx->y=y1; vx->u = u0; vx->v = v1; vx->color = color; vx++;
    }
    ctx->pos.x += 1.0f;
}

static void _sdtx_put_char(_sdtx_context_t* ctx, char c) {
    uint8_t c_u8 = (uint8_t)c;
    if (c_u8 <= 32) {
        _sdtx_ctrl_char(ctx, c_u8);
    } else {
        _sdtx_render_char(ctx, c_u8);
    }
}

SOKOL_API_IMPL void _sdtx_draw_layer(_sdtx_context_t* ctx, int layer_id) {
    SOKOL_ASSERT(_SDTX_INIT_COOKIE == _sdtx.init_cookie);
    SOKOL_ASSERT(ctx);
    if ((ctx->vertices.next > 0) && (ctx->commands.next > 0)) {
        sg_push_debug_group("sokol-debugtext");

        if (ctx->update_frame_id != ctx->frame_id) {
            ctx->update_frame_id = ctx->frame_id;
            const sg_range range = { ctx->vertices.ptr, (size_t)ctx->vertices.next * sizeof(_sdtx_vertex_t) };
            sg_update_buffer(ctx->vbuf, &range);
        }

        sg_apply_pipeline(ctx->pip);
        sg_bindings bindings;
        _sdtx_clear(&bindings, sizeof(bindings));
        bindings.vertex_buffers[0] = ctx->vbuf;
        bindings.views[0] = _sdtx.font_view;
        bindings.samplers[0] = _sdtx.font_smp;
        sg_apply_bindings(&bindings);
        for (int cmd_index = 0; cmd_index < ctx->commands.next; cmd_index++) {
            const _sdtx_command_t* cmd = &ctx->commands.ptr[cmd_index];
            if (cmd->layer_id != layer_id) {
                continue;
            }
            SOKOL_ASSERT((cmd->num_vertices % 6) == 0);
            sg_draw(cmd->first_vertex, cmd->num_vertices, 1);
        }
        sg_pop_debug_group();
    }
}


static sdtx_desc_t _sdtx_desc_defaults(const sdtx_desc_t* desc) {
    SOKOL_ASSERT((desc->allocator.alloc_fn && desc->allocator.free_fn) || (!desc->allocator.alloc_fn && !desc->allocator.free_fn));
    sdtx_desc_t res = *desc;
    res.context_pool_size = _sdtx_def(res.context_pool_size, _SDTX_DEFAULT_CONTEXT_POOL_SIZE);
    res.printf_buf_size = _sdtx_def(res.printf_buf_size, _SDTX_DEFAULT_PRINTF_BUF_SIZE);
    for (int i = 0; i < SDTX_MAX_FONTS; i++) {
        if (res.fonts[i].data.ptr) {
            res.fonts[i].last_char = _sdtx_def(res.fonts[i].last_char, 255);
        }
    }
    res.context = _sdtx_context_desc_defaults(&res.context);
    SOKOL_ASSERT(res.context_pool_size > 0);
    SOKOL_ASSERT(res.printf_buf_size > 0);
    SOKOL_ASSERT(res.context.char_buf_size > 0);
    return res;
}

// ██████  ██    ██ ██████  ██      ██  ██████
// ██   ██ ██    ██ ██   ██ ██      ██ ██
// ██████  ██    ██ ██████  ██      ██ ██
// ██      ██    ██ ██   ██ ██      ██ ██
// ██       ██████  ██████  ███████ ██  ██████
//
// >>public
SOKOL_API_IMPL void sdtx_setup(const sdtx_desc_t* desc) {
    SOKOL_ASSERT(desc);
    _sdtx_clear(&_sdtx, sizeof(_sdtx));
    _sdtx.init_cookie = _SDTX_INIT_COOKIE;
    _sdtx.desc = _sdtx_desc_defaults(desc);
    _sdtx_setup_context_pool(&_sdtx.desc);
    _sdtx_setup_common();
    _sdtx.def_ctx_id = sdtx_make_context(&_sdtx.desc.context);
    SOKOL_ASSERT(SDTX_DEFAULT_CONTEXT.id == _sdtx.def_ctx_id.id);
    sdtx_set_context(_sdtx.def_ctx_id);
}

SOKOL_API_IMPL void sdtx_shutdown(void) {
    SOKOL_ASSERT(_SDTX_INIT_COOKIE == _sdtx.init_cookie);
    for (int i = 0; i < _sdtx.context_pool.pool.size; i++) {
        _sdtx_context_t* ctx = &_sdtx.context_pool.contexts[i];
        _sdtx_destroy_context(_sdtx_make_ctx_id(ctx->slot.id));
    }
    _sdtx_discard_common();
    _sdtx_discard_context_pool();
    _sdtx.init_cookie = 0;
}

SOKOL_API_IMPL sdtx_font_desc_t sdtx_font_kc853(void) {
    sdtx_font_desc_t desc = { { _sdtx_font_kc853, sizeof(_sdtx_font_kc853) }, 0, 255 };
    return desc;
}

SOKOL_API_IMPL sdtx_font_desc_t sdtx_font_kc854(void) {
    sdtx_font_desc_t desc = { { _sdtx_font_kc854, sizeof(_sdtx_font_kc854) }, 0, 255 };
    return desc;
}

SOKOL_API_IMPL sdtx_font_desc_t sdtx_font_z1013(void) {
    sdtx_font_desc_t desc = { { _sdtx_font_z1013, sizeof(_sdtx_font_z1013) }, 0, 255 };
    return desc;
}

SOKOL_API_IMPL sdtx_font_desc_t sdtx_font_cpc(void) {
    sdtx_font_desc_t desc = { { _sdtx_font_cpc, sizeof(_sdtx_font_cpc) }, 0, 255 };
    return desc;
}

SOKOL_API_IMPL sdtx_font_desc_t sdtx_font_c64(void) {
    sdtx_font_desc_t desc = { { _sdtx_font_c64, sizeof(_sdtx_font_c64) }, 0, 255 };
    return desc;
}

SOKOL_API_IMPL sdtx_font_desc_t sdtx_font_oric(void) {
    sdtx_font_desc_t desc = { { _sdtx_font_oric, sizeof(_sdtx_font_oric) }, 0, 255 };
    return desc;
}

SOKOL_API_IMPL sdtx_context sdtx_make_context(const sdtx_context_desc_t* desc) {
    SOKOL_ASSERT(_SDTX_INIT_COOKIE == _sdtx.init_cookie);
    SOKOL_ASSERT(desc);
    sdtx_context ctx_id = _sdtx_alloc_context();
    if (ctx_id.id != SG_INVALID_ID) {
        _sdtx_init_context(ctx_id, desc);
    } else {
        _SDTX_ERROR(CONTEXT_POOL_EXHAUSTED);
    }
    return ctx_id;
}

SOKOL_API_IMPL void sdtx_destroy_context(sdtx_context ctx_id) {
    SOKOL_ASSERT(_SDTX_INIT_COOKIE == _sdtx.init_cookie);
    if (_sdtx_is_default_context(ctx_id)) {
        _SDTX_ERROR(CANNOT_DESTROY_DEFAULT_CONTEXT);
        return;
    }
    _sdtx_destroy_context(ctx_id);
    // re-validate the current context pointer (this will return a nullptr
    // if we just destroyed the current context)
    _sdtx.cur_ctx = _sdtx_lookup_context(_sdtx.cur_ctx_id.id);
}

SOKOL_API_IMPL void sdtx_set_context(sdtx_context ctx_id) {
    SOKOL_ASSERT(_SDTX_INIT_COOKIE == _sdtx.init_cookie);
    if (_sdtx_is_default_context(ctx_id)) {
        _sdtx.cur_ctx_id = _sdtx.def_ctx_id;
    } else {
        _sdtx.cur_ctx_id = ctx_id;
    }
    // this may return a nullptr if the ctx_id handle is invalid
    _sdtx.cur_ctx = _sdtx_lookup_context(_sdtx.cur_ctx_id.id);
}

SOKOL_API_IMPL sdtx_context sdtx_get_context(void) {
    SOKOL_ASSERT(_SDTX_INIT_COOKIE == _sdtx.init_cookie);
    return _sdtx.cur_ctx_id;
}

SOKOL_API_IMPL sdtx_context sdtx_default_context(void) {
    return SDTX_DEFAULT_CONTEXT;
}

SOKOL_API_IMPL void sdtx_layer(int layer_id) {
    SOKOL_ASSERT(_SDTX_INIT_COOKIE == _sdtx.init_cookie);
    _sdtx_context_t* ctx = _sdtx.cur_ctx;
    if (ctx) {
        _sdtx_set_layer(ctx, layer_id);
    }
}

SOKOL_API_IMPL void sdtx_font(int font_index) {
    SOKOL_ASSERT(_SDTX_INIT_COOKIE == _sdtx.init_cookie);
    SOKOL_ASSERT((font_index >= 0) && (font_index < SDTX_MAX_FONTS));
    _sdtx_context_t* ctx = _sdtx.cur_ctx;
    if (ctx) {
        ctx->cur_font = font_index;
    }
}

SOKOL_API_IMPL void sdtx_canvas(float w, float h) {
    SOKOL_ASSERT(_SDTX_INIT_COOKIE == _sdtx.init_cookie);
    SOKOL_ASSERT(!isnan(w));
    SOKOL_ASSERT(!isnan(h));
    SOKOL_ASSERT((w > 0.0f) && (h > 0.0f));
    _sdtx_context_t* ctx = _sdtx.cur_ctx;
    if (ctx) {
        ctx->canvas_size.x = w;
        ctx->canvas_size.y = h;
        ctx->glyph_size.x = (8.0f / ctx->canvas_size.x);
        ctx->glyph_size.y = (8.0f / ctx->canvas_size.y);
        ctx->origin.x = 0.0f;
        ctx->origin.y = 0.0f;
        ctx->pos.x = 0.0f;
        ctx->pos.y = 0.0f;
    }
}

SOKOL_API_IMPL void sdtx_origin(float x, float y) {
    SOKOL_ASSERT(_SDTX_INIT_COOKIE == _sdtx.init_cookie);
    _sdtx_context_t* ctx = _sdtx.cur_ctx;
    if (ctx) {
        ctx->origin.x = x;
        ctx->origin.y = y;
    }
}

SOKOL_API_IMPL void sdtx_home(void) {
    SOKOL_ASSERT(_SDTX_INIT_COOKIE == _sdtx.init_cookie);
    _sdtx_context_t* ctx = _sdtx.cur_ctx;
    if (ctx) {
        ctx->pos.x = 0.0f;
        ctx->pos.y = 0.0f;
    }
}

SOKOL_API_IMPL void sdtx_pos(float x, float y) {
    SOKOL_ASSERT(_SDTX_INIT_COOKIE == _sdtx.init_cookie);
    _sdtx_context_t* ctx = _sdtx.cur_ctx;
    if (ctx) {
        ctx->pos.x = x;
        ctx->pos.y = y;
    }
}

SOKOL_API_IMPL void sdtx_pos_x(float x) {
    SOKOL_ASSERT(_SDTX_INIT_COOKIE == _sdtx.init_cookie);
    _sdtx_context_t* ctx = _sdtx.cur_ctx;
    if (ctx) {
        ctx->pos.x = x;
    }
}

SOKOL_API_IMPL void sdtx_pos_y(float y) {
    SOKOL_ASSERT(_SDTX_INIT_COOKIE == _sdtx.init_cookie);
    _sdtx_context_t* ctx = _sdtx.cur_ctx;
    if (ctx) {
        ctx->pos.y = y;
    }
}

SOKOL_API_IMPL void sdtx_move(float dx, float dy) {
    SOKOL_ASSERT(_SDTX_INIT_COOKIE == _sdtx.init_cookie);
    _sdtx_context_t* ctx = _sdtx.cur_ctx;
    if (ctx) {
        ctx->pos.x += dx;
        ctx->pos.y += dy;
    }
}

SOKOL_API_IMPL void sdtx_move_x(float dx) {
    SOKOL_ASSERT(_SDTX_INIT_COOKIE == _sdtx.init_cookie);
    _sdtx_context_t* ctx = _sdtx.cur_ctx;
    if (ctx) {
        ctx->pos.x += dx;
    }
}

SOKOL_API_IMPL void sdtx_move_y(float dy) {
    SOKOL_ASSERT(_SDTX_INIT_COOKIE == _sdtx.init_cookie);
    _sdtx_context_t* ctx = _sdtx.cur_ctx;
    if (ctx) {
        ctx->pos.y += dy;
    }
}

SOKOL_API_IMPL void sdtx_crlf(void) {
    SOKOL_ASSERT(_SDTX_INIT_COOKIE == _sdtx.init_cookie);
    _sdtx_context_t* ctx = _sdtx.cur_ctx;
    if (ctx) {
        ctx->pos.x = 0.0f;
        ctx->pos.y += 1.0f;
    }
}

SOKOL_API_IMPL void sdtx_color3b(uint8_t r, uint8_t g, uint8_t b) {
    SOKOL_ASSERT(_SDTX_INIT_COOKIE == _sdtx.init_cookie);
    _sdtx_context_t* ctx = _sdtx.cur_ctx;
    if (ctx) {
        ctx->color = _sdtx_pack_rgbab(r, g, b, 255);
    }
}

SOKOL_API_IMPL void sdtx_color3f(float r, float g, float b) {
    SOKOL_ASSERT(_SDTX_INIT_COOKIE == _sdtx.init_cookie);
    _sdtx_context_t* ctx = _sdtx.cur_ctx;
    if (ctx) {
        ctx->color = _sdtx_pack_rgbaf(r, g, b, 1.0f);
    }
}

SOKOL_API_IMPL void sdtx_color4b(uint8_t r, uint8_t g, uint8_t b, uint8_t a) {
    SOKOL_ASSERT(_SDTX_INIT_COOKIE == _sdtx.init_cookie);
    _sdtx_context_t* ctx = _sdtx.cur_ctx;
    if (ctx) {
        ctx->color = _sdtx_pack_rgbab(r, g, b, a);
    }
}

SOKOL_API_IMPL void sdtx_color4f(float r, float g, float b, float a) {
    SOKOL_ASSERT(_SDTX_INIT_COOKIE == _sdtx.init_cookie);
    _sdtx_context_t* ctx = _sdtx.cur_ctx;
    if (ctx) {
        ctx->color = _sdtx_pack_rgbaf(r, g, b, a);
    }
}

SOKOL_API_IMPL void sdtx_color1i(uint32_t rgba) {
    SOKOL_ASSERT(_SDTX_INIT_COOKIE == _sdtx.init_cookie);
    _sdtx_context_t* ctx = _sdtx.cur_ctx;
    if (ctx) {
        ctx->color = rgba;
    }
}

SOKOL_DEBUGTEXT_API_DECL void sdtx_putc(char chr) {
    SOKOL_ASSERT(_SDTX_INIT_COOKIE == _sdtx.init_cookie);
    _sdtx_context_t* ctx = _sdtx.cur_ctx;
    if (ctx) {
        _sdtx_put_char(ctx, chr);
    }
}

SOKOL_DEBUGTEXT_API_DECL void sdtx_puts(const char* str) {
    SOKOL_ASSERT(_SDTX_INIT_COOKIE == _sdtx.init_cookie);
    _sdtx_context_t* ctx = _sdtx.cur_ctx;
    if (ctx) {
        char chr;
        while (0 != (chr = *str++)) {
            _sdtx_put_char(ctx, chr);
        }
    }
}

SOKOL_DEBUGTEXT_API_DECL void sdtx_putr(const char* str, int len) {
    SOKOL_ASSERT(_SDTX_INIT_COOKIE == _sdtx.init_cookie);
    _sdtx_context_t* ctx = _sdtx.cur_ctx;
    if (ctx) {
        for (int i = 0; i < len; i++) {
            char chr = str[i];
            if (0 == chr) {
                break;
            }
            _sdtx_put_char(ctx, chr);
        }
    }
}

SOKOL_DEBUGTEXT_API_DECL int sdtx_vprintf(const char* fmt, va_list args) {
    SOKOL_ASSERT(_SDTX_INIT_COOKIE == _sdtx.init_cookie);
    SOKOL_ASSERT(_sdtx.fmt_buf && (_sdtx.fmt_buf_size >= 2));
    int res = SOKOL_VSNPRINTF(_sdtx.fmt_buf, _sdtx.fmt_buf_size, fmt, args);
    // make sure we're 0-terminated in case we're on an old MSVC
    _sdtx.fmt_buf[_sdtx.fmt_buf_size-1] = 0;
    sdtx_puts(_sdtx.fmt_buf);
    return res;
}

SOKOL_DEBUGTEXT_API_DECL int sdtx_printf(const char* fmt, ...) {
    SOKOL_ASSERT(_SDTX_INIT_COOKIE == _sdtx.init_cookie);
    SOKOL_ASSERT(_sdtx.fmt_buf && (_sdtx.fmt_buf_size >= 2));
    va_list args;
    va_start(args, fmt);
    int res = SOKOL_VSNPRINTF(_sdtx.fmt_buf, _sdtx.fmt_buf_size, fmt, args);
    va_end(args);
    // make sure we're 0-terminated in case we're on an old MSVC
    _sdtx.fmt_buf[_sdtx.fmt_buf_size-1] = 0;
    sdtx_puts(_sdtx.fmt_buf);
    return res;
}

SOKOL_DEBUGTEXT_API_DECL sdtx_range sdtx_get_cleared_fmt_buffer(void) {
    SOKOL_ASSERT(_SDTX_INIT_COOKIE == _sdtx.init_cookie);
    SOKOL_ASSERT(_sdtx.fmt_buf && (_sdtx.fmt_buf_size >= 2));
    memset(_sdtx.fmt_buf, 0, _sdtx.fmt_buf_size);
    sdtx_range res; _sdtx_clear(&res, sizeof(res));
    res.ptr = _sdtx.fmt_buf;
    res.size = _sdtx.fmt_buf_size - 1;
    return res;
}

SOKOL_API_IMPL void sdtx_draw(void) {
    SOKOL_ASSERT(_SDTX_INIT_COOKIE == _sdtx.init_cookie);
    _sdtx_context_t* ctx = _sdtx.cur_ctx;
    if (ctx) {
        _sdtx_draw_layer(ctx, 0);
    }
}

SOKOL_API_IMPL void sdtx_context_draw(sdtx_context ctx_id) {
    SOKOL_ASSERT(_SDTX_INIT_COOKIE == _sdtx.init_cookie);
    _sdtx_context_t* ctx = _sdtx_lookup_context(ctx_id.id);
    if (ctx) {
        _sdtx_draw_layer(ctx, 0);
    }
}

SOKOL_API_IMPL void sdtx_draw_layer(int layer_id) {
    SOKOL_ASSERT(_SDTX_INIT_COOKIE == _sdtx.init_cookie);
    _sdtx_context_t* ctx = _sdtx.cur_ctx;
    if (ctx) {
        _sdtx_draw_layer(ctx, layer_id);
    }
}

SOKOL_API_IMPL void sdtx_context_draw_layer(sdtx_context ctx_id, int layer_id) {
    SOKOL_ASSERT(_SDTX_INIT_COOKIE == _sdtx.init_cookie);
    _sdtx_context_t* ctx = _sdtx_lookup_context(ctx_id.id);
    if (ctx) {
        _sdtx_draw_layer(ctx, layer_id);
    }
}
#endif // SOKOL_DEBUGTEXT_IMPL
