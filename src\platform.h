#pragma once

#include <stdint.h> // for uint8_t

#define global static
#define persist static
#define internal static

#define STR(...) # __VA_ARGS__

#define Kilobytes(n) ((n) * 1024)
#define Megabytes(n) (Kilobytes(n) * 1024)
#define Gigabytes(n) (Megabytes(n) * 1024)

typedef struct {
    float x, y;
} Pos;

typedef struct {
    float w, h;
} Size;

typedef struct {
    float u, v;
} UV;

typedef struct {
    float r, g, b, a;
} Color;

typedef struct {
    uint8_t* base;
    size_t size;
    size_t offset;
} Arena;

typedef struct {
    Arena persistent_arena;
    Arena transient_arena;
    void (*renderer_push)(Pos pos, Size size, UV uv, Color color);
} PlatformLayer;